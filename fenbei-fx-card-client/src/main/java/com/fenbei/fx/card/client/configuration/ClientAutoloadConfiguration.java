package com.fenbei.fx.card.client.configuration;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Rpc Client 自动配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@Configuration
@EnableSpringUtil
@ComponentScan(basePackages = {"com.fenbei.fx.card.client"})
public class ClientAutoloadConfiguration {

}
