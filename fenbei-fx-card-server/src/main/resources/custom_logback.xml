<included>
    <!-- 标识是否有自定义 logger -->
    <property name="CUSTOM_LOGGER" value="true"/>

    <logger name="org.springframework.kafka" level="ERROR" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="net.sf.ehcache" level="ERROR" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.springframework.security" level="ERROR" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="shaded.org.apache" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.apache" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.apache.velocity" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.apache.myfaces" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.dbunitorg.springframework" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    <logger name="org.hibernate" level="WARN" additivity="false">
        <appender-ref ref="${EFFECTIVE_LOG_APPENDER}"/>
    </logger>
    
    <logger name="io.netty.handler.logging" level="warn" additivity="false"/>
	
	<logger name="com.alibaba.nacos" level="warn" additivity="false"/>
	
	<logger name="com.finhub.framework.logback.util" level="warn" additivity="false"/>
	
	<logger name="ShardingSphere-SQL" level="warn" additivity="false"/>
	
	<logger name="com.fenbeitong.finhub.dubbo.filter" level="warn" additivity="false"/>
	
	<logger name="com.fenbeitong.finhub.auth.core.auth" level="warn" additivity="false"/>

</included>
