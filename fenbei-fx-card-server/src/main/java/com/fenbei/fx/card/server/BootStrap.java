package com.fenbei.fx.card.server;

import com.fenbei.fx.card.rpc.RpcServer;
import com.fenbei.fx.card.web.WebServer;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * <pre>
 * 聚合启动类
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
public class BootStrap {
    public static void main(String[] args) {
        WebServer.start(new SpringApplicationBuilder(WebServer.class, RpcServer.class), args);
    }
}
