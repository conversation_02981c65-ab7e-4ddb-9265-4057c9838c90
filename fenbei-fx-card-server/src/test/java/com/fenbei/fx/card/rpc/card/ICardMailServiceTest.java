package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardMailService;
import com.fenbei.fx.card.api.card.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.api.card.dto.CardMailRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CardMailRpcRespDTO;
import com.fenbei.fx.card.api.card.dto.PageDTO;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-14 下午8:57
 */
@Slf4j
public class ICardMailServiceTest extends BaseJUnitTester {

    @Autowired
    ICardMailService iCardMailService;

    @Test
    public void queryCardMailStereoPageTest(){
        CardMailRpcReqDTO cardMailRpcReqDTO = new CardMailRpcReqDTO();
        PageDTO<CardMailRpcRespDTO> cardMailRpcRespDTOPageDTO = iCardMailService.queryCardMailStereoPage(cardMailRpcReqDTO);
        log.info("resp{}", JsonUtils.toJson(cardMailRpcRespDTOPageDTO));
    }

    @Test
    public void modifyCardMailStereoTest(){
        CardMailModifyReqDTO cardMailModifyReqDTO = new CardMailModifyReqDTO();
        cardMailModifyReqDTO.setId("1");
        cardMailModifyReqDTO.setChangedAddress("dfasdfasfsdf");
        Integer integer = iCardMailService.modifyCardMailStereo(cardMailModifyReqDTO);
        log.info("resp{}",integer);
    }
}
