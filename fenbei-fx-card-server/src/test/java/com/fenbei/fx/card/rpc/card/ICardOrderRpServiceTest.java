package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardOrderStereoService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-14 下午8:57
 */
@Slf4j
public class ICardOrderRpServiceTest extends BaseJUnitTester {

    @Autowired
    ICardOrderStereoService iCardOrderStereoService;

    @Test
    public void pageTest(){
        CardTradeInfoStereoPageRpcReqDTO pageRpcReqDTO = new CardTradeInfoStereoPageRpcReqDTO();
        pageRpcReqDTO.setPageNo(1);
        pageRpcReqDTO.setPageSize(3);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 2024);
        calendar.set(Calendar.MONTH, Calendar.JUNE); // 注意月份从0开始，即0对应1月
        calendar.set(Calendar.DATE, 20);
        pageRpcReqDTO.setCreateGeDate(calendar.getTime());
        calendar.set(Calendar.YEAR, 2024);
        calendar.set(Calendar.MONTH, Calendar.JULY); // 注意月份从0开始，即0对应1月
        calendar.set(Calendar.DATE, 20);
        pageRpcReqDTO.setCreateLeDate(calendar.getTime());
//        pageRpcReqDTO.setCompanyId("");
        PageDTO<CardTradeInfoStereoPageRpcResVO> pagination = iCardOrderStereoService.pagination(pageRpcReqDTO);
        log.info("resp{}", JsonUtils.toJson(pagination));
    }

}
