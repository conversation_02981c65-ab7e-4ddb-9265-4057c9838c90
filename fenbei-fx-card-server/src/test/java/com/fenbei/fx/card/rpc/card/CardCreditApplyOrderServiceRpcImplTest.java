package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 额度申请单RPC服务测试类
 */
@Slf4j
public class CardCreditApplyOrderServiceRpcImplTest extends BaseJUnitTester {

    @Autowired
    private CardCreditApplyOrderServiceRpcImpl cardCreditApplyOrderService;

    /**
     * 测试创建额度申请单
     */
    @Test
    public void testCreateApplyOrder() {
        CardCreditApplyOrderCreateRpcReqDTO createReqDTO = new CardCreditApplyOrderCreateRpcReqDTO();
        createReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        createReqDTO.setMeaningNo("5d1b1d2f23445f4dca763999");
        createReqDTO.setActiveModel(1);


        createReqDTO.setTitle("测试额度申请单");
        createReqDTO.setApplyAmount(new BigDecimal("100"));
        createReqDTO.setApplyReason("测试原因");
        createReqDTO.setCurrency("USD");

        createReqDTO.setCreaterId("621845d349430b0f22d81fd0");
        createReqDTO.setApplicantId("621845d349430b0f22d81fd0");

        createReqDTO.setFxCardId("FXC1821118003861917697");
        createReqDTO.setBankName("AIRWALLEX");


        CardCreditApplyOrderCreateRpcRespDTO respDTO = cardCreditApplyOrderService.createApplyOrder(createReqDTO);
        log.info("创建额度申请单结果: {}", JsonUtils.toJson(respDTO));
    }

    /**
     * 测试尝试发放额度
     */
    @Test
    public void testTrySend() {
        CardCreditApplyOrderTrySendRpcReqDTO trySendReqDTO = new CardCreditApplyOrderTrySendRpcReqDTO();
        // 这里需要填入实际存在的申请单ID
        trySendReqDTO.setApplyOrderId("APPLY_ORDER_ID_HERE");

        CardCreditApplyOrderTrySendRpcRespDTO respDTO = cardCreditApplyOrderService.trySend(trySendReqDTO);
        log.info("尝试发放额度结果: {}", JsonUtils.toJson(respDTO));
    }

    /**
     * 测试查询额度申请单详情
     */
    @Test
    public void testGetApplyOrderDetail() {
        CardCreditApplyOrderDetailRpcReqDTO queryReqDTO = new CardCreditApplyOrderDetailRpcReqDTO();
        // 这里需要填入实际存在的申请单ID
        queryReqDTO.setApplyOrderId("APPLY_ORDER_ID_HERE");

        CardCreditApplyOrderDetailRpcRespDTO respDTO = cardCreditApplyOrderService.getApplyOrderDetail(queryReqDTO);
        log.info("查询额度申请单详情结果: {}", JsonUtils.toJson(respDTO));
    }


}
