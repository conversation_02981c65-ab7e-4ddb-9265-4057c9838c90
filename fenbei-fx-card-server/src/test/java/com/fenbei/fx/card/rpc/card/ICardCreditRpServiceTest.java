package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardCreditStereoService;
import com.fenbei.fx.card.api.card.ICardOrderStereoService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-14 下午8:57
 */
@Slf4j
public class ICardCreditRpServiceTest extends BaseJUnitTester {

    @Autowired
    ICardCreditStereoService iCardCreditStereoService;

    @Test
    public void pageTest(){
        CardCreditStereoRpcQueryDTO pageRpcReqDTO = new CardCreditStereoRpcQueryDTO();
        pageRpcReqDTO.setPageNo(1);
        pageRpcReqDTO.setPageSize(3);
        pageRpcReqDTO.setApplyType(1);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 2023);
        calendar.set(Calendar.MONTH, Calendar.MAY); // 注意月份从0开始，即0对应1月
        calendar.set(Calendar.DATE, 20);
        pageRpcReqDTO.setBeginApplyTime(calendar.getTime());
        calendar.set(Calendar.YEAR, 2023);
        calendar.set(Calendar.MONTH, Calendar.AUGUST); // 注意月份从0开始，即0对应1月
        calendar.set(Calendar.DATE, 20);
        pageRpcReqDTO.setEndApplyTime(calendar.getTime());
        PageDTO<CardCreditStereoRpcListDTO> pagination = iCardCreditStereoService.applyList(pageRpcReqDTO);
        log.info("resp{}", JsonUtils.toJson(pagination));
    }

}
