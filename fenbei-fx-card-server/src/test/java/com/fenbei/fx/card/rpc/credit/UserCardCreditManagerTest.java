package com.fenbei.fx.card.rpc.credit;

import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.fenbei.fx.card.service.cardmodelconfig.CardModelConfigService;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigAddReqDTO;
import com.fenbei.fx.card.service.usercard.UserCardCreditManager;
import com.fenbei.fx.card.service.usercard.dto.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.finhub.framework.core.page.Page;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-23 下午3:06
 */
@Slf4j
public class UserCardCreditManagerTest extends BaseJUnitTester {

    @Autowired
    UserCardCreditManager userCardCreditManager;

    @Autowired
    CardModelConfigService cardModelConfigService;

    @Test
    public void cardCreditChangeRecordInfoTest(){
        UserCardCreditChangeRecordDTO userCardCreditChangeRecordDTO = userCardCreditManager.cardCreditChangeRecordInfo(" FXC1821118003861917697");
        log.info("result{}", JsonUtils.toJson(userCardCreditChangeRecordDTO));
    }

    @Test
    public void creditChangeRecordListTest(){
        BankCardCreditChangeRecordPageDTO bankCardCreditChangeRecordPageDTO = new BankCardCreditChangeRecordPageDTO();
        bankCardCreditChangeRecordPageDTO.setFxCardId("FXC1821118003861917697");
//        bankCardCreditChangeRecordPageDTO.setCompanyId();
        Page<BankCardCreditChangeDTO> bankCardCreditChangeDTOPage = userCardCreditManager.creditChangeRecordList(bankCardCreditChangeRecordPageDTO);
        log.info("result{}", JsonUtils.toJson(bankCardCreditChangeDTOPage));
    }

    @Test
    public void creditGrantListTest(){
        UserCardCreditGrantListQueryDTO userCardCreditGrantListQueryDTO = new UserCardCreditGrantListQueryDTO();
        userCardCreditGrantListQueryDTO.setCompanyId("1111");
        Page<UserCardCreditGrantListDTO> userCardCreditGrantListDTOPage = userCardCreditManager.creditGrantList(userCardCreditGrantListQueryDTO);
        log.info("resp{}", JsonUtils.toJson(userCardCreditGrantListDTOPage));
    }

    @Test
    public void employeeModelConfigListTest(){
        EmployeeModelConfigListReqDTO employeeModelConfigListReqDTO = new EmployeeModelConfigListReqDTO();
        UserComInfoVO userComInfoVO = new UserComInfoVO();
        userComInfoVO.setCompany_id("5747fbc10f0e60e0709d8d7d");
        UserAuthHolder.putCurrentUser(userComInfoVO);
        Page<EmployeeModelConfigListDTO> employeeModelConfigListDTOPage = userCardCreditManager.employeeModelConfigList(employeeModelConfigListReqDTO);
        log.info("resp{}", JsonUtils.toJson(employeeModelConfigListDTOPage));
    }

    @Test
    public void saveOrUpdateTest(){
        CardModelConfigAddReqDTO cardModelConfigAddReqDTO = new CardModelConfigAddReqDTO();
        cardModelConfigAddReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        cardModelConfigAddReqDTO.setActiveModel(1);
        cardModelConfigAddReqDTO.setModelType(1);
        Boolean aBoolean = cardModelConfigService.saveOrUpdate(cardModelConfigAddReqDTO);
        log.info("resp=",aBoolean);
    }


}
