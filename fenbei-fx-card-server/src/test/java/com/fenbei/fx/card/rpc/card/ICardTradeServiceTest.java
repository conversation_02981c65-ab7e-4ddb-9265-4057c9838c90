package com.fenbei.fx.card.rpc.card;

import com.alibaba.fastjson.JSON;
import com.fenbei.fx.card.api.card.ICardTradeService;
import com.fenbei.fx.card.api.card.dto.AuthorizationRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.AuthorizationRpcRespDTO;
import com.fenbei.fx.card.api.card.dto.TransactionAckRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.UncheckOrderRpcReqDTO;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

public class ICardTradeServiceTest extends BaseJUnitTester {

    @Autowired
    private ICardTradeService iCardTradeService;

    @Autowired
    private CardOrderService cardOrderService;

    @Test
    @Rollback(false)
    public void authorizeByAuth() {
        String authorization = "{\n" +
            "    \"accountId\":\"acct_pZnMdTYEPRu31nWHfmin5w\",\n" +
            "    \"cardId\":\"10c49c33-9404-42d5-93d5-ca2b872ed96e\",\n" +
            "    \"transactionId\":\"b733d56f-6286-49ed-4444-adc74a76fb26\",\n" +
            "    \"transactionType\":\"AUTHORIZATION\",\n" +
            "    \"transactionDate\":\"2024-11-26T07:36:03.886+0000\",\n" +
            "    \"transactionCurrency\":\"USD\",\n" +
            "    \"transactionAmount\":0.06,\n" +
            "    \"merchant\":{\n" +
            "        \"categoryCode\":\"7531\",\n" +
            "        \"city\":\"E AUTH TEST\",\n" +
            "        \"country\":\"AWX\",\n" +
            "        \"name\":\"CARD_TX_GENERATOR REMOT\"\n" +
            "    },\n" +
            "    \"authCode\":\"785303\",\n" +
            "    \"maskedCardNumber\":\"************1150\",\n" +
            "    \"retrievalRef\":\"************\",\n" +
            "    \"clientData\":\"\",\n" +
            "    \"cardNickname\":\"\",\n" +
            "    \"networkTransactionId\":\"843608022631272\",\n" +
            "    \"billingOrder\":[\n" +
            "        {\n" +
            "            \"currency\":\"USD\",\n" +
            "            \"amount\":0.054\n" +
            "        }\n" +
            "    ]\n" +
            "}";
        AuthorizationRpcReqDTO authorizationRpcReqDTO = JSON.parseObject(authorization,AuthorizationRpcReqDTO.class);

        AuthorizationRpcRespDTO authorize  = iCardTradeService.authorize(authorizationRpcReqDTO);

        System.out.println(authorize);
    }

    @Test
    public void tradeByAUTHORIZATION_AP(){
        String tradeString = "{\n" +
            "    \"auth_code\": \"ABC123\",\n" +
            "    \"billing_amount\": -7.99,\n" +
            "    \"billing_currency\": \"USD\",\n" +
            "    \"card_id\": \"f4385a32-2db1-4403-903d-ae32f56f1051\",\n" +
            "    \"card_nickname\": \"Recurring payments\",\n" +
            "    \"client_data\": null,\n" +
            "    \"failure_reason\": \"CURRENCY_NOT_ALLOWED\",\n" +
            "    \"masked_card_number\": \"************4242\",\n" +
            "    \"merchant\": {\n" +
            "      \"category_code\": \"4829\",\n" +
            "      \"city\": \"Melbourne\",\n" +
            "      \"country\": \"Australia\",\n" +
            "      \"name\": \"Merchant A\"\n" +
            "    },\n" +
            "    \"network_transaction_id\": \"3951729271768745\",\n" +
            "    \"posted_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"retrieval_ref\": \"464665204556\",\n" +
            "    \"status\": \"APPROVED\",\n" +
            "    \"transaction_amount\": 11.11,\n" +
            "    \"transaction_currency\": \"AUD\",\n" +
            "    \"transaction_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"transaction_id\": \"47f9739c-3501-49ae-b929-202306030008\",\n" +
            "    \"transaction_type\": \"CLEARING\"\n" +
            "  }";
        TransactionAckRpcReqDTO transactionAckRpcReqDTO  = JSON.parseObject(tradeString,TransactionAckRpcReqDTO.class);
        iCardTradeService.trade(transactionAckRpcReqDTO);
    }
    @Test
    public void tradeByREVERSAL(){
        String tradeString = "{\n" +
            "    \"auth_code\": \"ABC123\",\n" +
            "    \"billing_amount\": -100,\n" +
            "    \"billing_currency\": \"HKD\",\n" +
            "    \"card_id\": \"f4385a32-2db1-4403-903d-ae32f56f1051\",\n" +
            "    \"card_nickname\": \"Recurring payments\",\n" +
            "    \"client_data\": null,\n" +
            "    \"failure_reason\": \"CURRENCY_NOT_ALLOWED\",\n" +
            "    \"masked_card_number\": \"************4242\",\n" +
            "    \"merchant\": {\n" +
            "      \"category_code\": \"4829\",\n" +
            "      \"city\": \"Melbourne\",\n" +
            "      \"country\": \"Australia\",\n" +
            "      \"name\": \"Merchant A\"\n" +
            "    },\n" +
            "    \"network_transaction_id\": \"3951729271768745\",\n" +
            "    \"posted_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"retrieval_ref\": \"464665204556\",\n" +
            "    \"status\": \"APPROVED\",\n" +
            "    \"transaction_amount\": 11.11,\n" +
            "    \"transaction_currency\": \"HKD\",\n" +
            "    \"transaction_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"transaction_id\": \"47f9739c-3501-49ae-b929-1001\",\n" +
            "    \"transaction_type\": \"REVERSAL\"\n" +
            "  }";
        TransactionAckRpcReqDTO transactionAckRpcReqDTO  = JSON.parseObject(tradeString,TransactionAckRpcReqDTO.class);
        iCardTradeService.trade(transactionAckRpcReqDTO);
    }

    @Test
    public void tradeByRefund(){
        String tradeString = "{\n" +
            "    \"auth_code\": \"ABC123\",\n" +
            "    \"billing_amount\": 7.99,\n" +
            "    \"billing_currency\": \"USD\",\n" +
            "    \"card_id\": \"f4385a32-2db1-4403-903d-ae32f56f1051\",\n" +
            "    \"card_nickname\": \"Recurring payments\",\n" +
            "    \"client_data\": null,\n" +
            "    \"failure_reason\": \"CURRENCY_NOT_ALLOWED\",\n" +
            "    \"masked_card_number\": \"************4242\",\n" +
            "    \"merchant\": {\n" +
            "      \"category_code\": \"4829\",\n" +
            "      \"city\": \"Melbourne\",\n" +
            "      \"country\": \"Australia\",\n" +
            "      \"name\": \"Merchant A\"\n" +
            "    },\n" +
            "    \"network_transaction_id\": \"3951729271768745\",\n" +
            "    \"posted_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"retrieval_ref\": \"464665204556\",\n" +
            "    \"status\": \"APPROVED\",\n" +
            "    \"transaction_amount\": 11.11,\n" +
            "    \"transaction_currency\": \"USD\",\n" +
            "    \"transaction_date\": \"2018-03-22T16:08:02.000+0000\",\n" +
            "    \"transaction_id\": \"47f9739c-3501-49ae-b929-202306030008\",\n" +
            "    \"transaction_type\": \"REFUND\"\n" +
            "  }";
        TransactionAckRpcReqDTO transactionAckRpcReqDTO  = JSON.parseObject(tradeString,TransactionAckRpcReqDTO.class);
        iCardTradeService.trade(transactionAckRpcReqDTO);
    }

    @Test
    public void uncheckOrderList(){
        UncheckOrderRpcReqDTO rpcReqDTO = new UncheckOrderRpcReqDTO();
        rpcReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        rpcReqDTO.setEmployId("621845d349430b0f22d81fd0");
        iCardTradeService.uncheckOrderList(rpcReqDTO);
    }

    @Test
    public void updateRate(){
        cardOrderService.updateHistoryValue("1890279582804750337");
    }
}
