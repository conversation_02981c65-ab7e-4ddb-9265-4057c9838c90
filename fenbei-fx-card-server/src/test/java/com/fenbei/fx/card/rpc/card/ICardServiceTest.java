package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardService;
import com.fenbei.fx.card.api.card.dto.CardBalanceRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CardBalanceRpcRespDTO;
import com.fenbei.fx.card.api.card.dto.UserFxcardRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.UserFxcardRpcRespDTO;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.finhub.framework.exception.MessageException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Slf4j
public class ICardServiceTest extends BaseJUnitTester {

    @Autowired
    private ICardService iCardService;

    @Test
    public void getBalance() {
        CardBalanceRpcReqDTO rpcReqDTO = new CardBalanceRpcReqDTO();
        rpcReqDTO.setCompanyId("1");
        rpcReqDTO.setPlatform("airwallex");

        List<CardBalanceRpcRespDTO> balance = iCardService.getBalance(rpcReqDTO);

        System.out.println(balance);
    }

    @Test
    public void findUserFxcardInfos() {
        UserFxcardRpcReqDTO rpcReqDTO = new UserFxcardRpcReqDTO();
        rpcReqDTO.setCompanyId("5747fbc10f0e60e0709d8d7d");
        rpcReqDTO.setEmployeeId("59a8d61623445f2e0fb09164");

        List<UserFxcardRpcRespDTO> userFxcardInfos = iCardService.findUserFxcardInfos(rpcReqDTO);

        System.out.println(userFxcardInfos);
    }
}
