package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.dto.CardCreditApplyRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CardCreditReturnRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CostAttribution;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import com.luastar.swift.base.json.JsonUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CardCreditManagerServiceRpcImplTest extends BaseJUnitTester {
    @Autowired
    private CardCreditManagerServiceRpcImpl cardCreditManagerService;
    @Test
    public void apply() {
        String data = "{\"saasApplyNo\":\"66bdc24fc57be775bb0cb9da\",\"saasApplyMeaningNo\":\"DTXU4000024081500003\",\"employeeId\":\"621845d349430b0f22d81fd0\",\"companyId\":\"5d1b1d2f23445f4dca76304b\",\"bankName\":\"LIANLIAN\",\"applyCreditAmount\":100,\"currency\":\"USD\",\"costAttributionId\":\"65bb4c80b230206dea0adab2\",\"costAttributionType\":2,\"costAttributionName\":\"项目二\",\"costAttributionOpt\":3,\"attributions\":[{\"costAttributionId\":\"65bb4c80b230206dea0adab2\",\"costAttributionType\":2,\"costAttributionName\":\"项目二\"}],\"budgetOpt\":1,\"deductionMode\":0,\"fxCardId\":\"FXC1821118003861917697\",\"appendFlag\":false,\"usdCnyExchangeRate\":1,\"cnyAmount\":100}";

        CardCreditApplyRpcReqDTO cardCreditApplyRpcReqDTO = JsonUtils.toObj(data,CardCreditApplyRpcReqDTO.class);
//            new CardCreditApplyRpcReqDTO();
//        cardCreditApplyRpcReqDTO.setFxCardId("FXC1655484168148226048");
//        cardCreditApplyRpcReqDTO.setApplyReason("测试");
//        cardCreditApplyRpcReqDTO.setApplyCreditAmount(new BigDecimal("1000"));
//        List<CostAttribution> costAttributions = new ArrayList<>();
//        CostAttribution costAttribution = new CostAttribution();
//        costAttribution.setCostAttributionId("632a83c053feb0470cc4ad2d");
//        costAttribution.setCostAttributionType(1);
//        costAttribution.setCostAttributionName("测试");
//        costAttributions.add(costAttribution);
//        cardCreditApplyRpcReqDTO.setAttributions(costAttributions);
//        cardCreditApplyRpcReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
//        cardCreditApplyRpcReqDTO.setEmployeeId("621845d349430b0f22d81fd0");
//        cardCreditApplyRpcReqDTO.setSaasApplyNo("6478008f1531f00899cb405a");

        cardCreditManagerService.apply(cardCreditApplyRpcReqDTO);
    }

    @Test
    public void applyAppend() {
        CardCreditApplyRpcReqDTO cardCreditApplyRpcReqDTO = new CardCreditApplyRpcReqDTO();
        cardCreditApplyRpcReqDTO.setFxCardId("FXC1655484168148226048");
        cardCreditApplyRpcReqDTO.setApplyReason("测试");
        cardCreditApplyRpcReqDTO.setApplyCreditAmount(new BigDecimal("100"));
        List<CostAttribution> costAttributions = new ArrayList<>();
        CostAttribution costAttribution = new CostAttribution();
        costAttribution.setCostAttributionId("63a169320012a37d1fe99e89");
        costAttribution.setCostAttributionType(1);
        costAttribution.setCostAttributionName("增超部门");
        costAttributions.add(costAttribution);
        cardCreditApplyRpcReqDTO.setAttributions(costAttributions);
        cardCreditApplyRpcReqDTO.setCostAttributionName("增超部门");
        cardCreditApplyRpcReqDTO.setCostAttributionId("63a169320012a37d1fe99e89");
        cardCreditApplyRpcReqDTO.setCostAttributionType(1);
        cardCreditApplyRpcReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        cardCreditApplyRpcReqDTO.setEmployeeId("621845d349430b0f22d81fd0");
        cardCreditApplyRpcReqDTO.setSaasApplyNo("PETTY1004");
        cardCreditApplyRpcReqDTO.setAppendFlag(true);
        cardCreditApplyRpcReqDTO.setApplyAppendBatchId("2");
        cardCreditManagerService.apply(cardCreditApplyRpcReqDTO);
    }

    @Test
    public void refund() {
        /**
         * {"fxCardId":"FXC1821118003861917697","dismissionEmployee":false,"refundCreditAmount":100,"returnAmount":{"66bde087c57be775bb0cbb2e":100}}
         */
        CardCreditReturnRpcReqDTO cardCreditReturnRpcReqDTO = new CardCreditReturnRpcReqDTO();
        cardCreditReturnRpcReqDTO.setFxCardId("FXC1821118003861917697");
        cardCreditReturnRpcReqDTO.setApplyReason("测试退还");
//        cardCreditReturnRpcReqDTO.setEmployeeId("621845d349430b0f22d81fd0");
        Map<String, BigDecimal> returnAmount = new HashMap<>();
        returnAmount.put("66bde087c57be775bb0cbb2e",new BigDecimal("100"));
        cardCreditReturnRpcReqDTO.setReturnAmount(returnAmount);
//        cardCreditReturnRpcReqDTO.setCompanyId("5d1b1d2f23445f4dca76304b");
        cardCreditReturnRpcReqDTO.setDismissionEmployee(false);
        cardCreditManagerService.refund(cardCreditReturnRpcReqDTO);
    }
}
