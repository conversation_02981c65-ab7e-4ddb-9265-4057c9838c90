package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardVerificationService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ICardVerficationServiceTest extends BaseJUnitTester {

    @Autowired
    private ICardVerificationService iCardVerificationService;

    @Test
    public void authorizeByAuth() {
        FxVerificationReqRpcDTO reqDTO = new FxVerificationReqRpcDTO();
        reqDTO.setDuringApplyId("123");

        List<FxVerificationReqRpcDTO.Item> itemList = new ArrayList<>();
        FxVerificationReqRpcDTO.Item item = new FxVerificationReqRpcDTO.Item();
        item.setBizNo("FXO1674262087066587136");
        item.setAmount(new BigDecimal("0.15"));
        itemList.add(item);
        reqDTO.setItems(itemList);

        List<FxVerificationReqRpcDTO.ApplyItem> applyItemList = new ArrayList<>();
        FxVerificationReqRpcDTO.ApplyItem applyItem = new FxVerificationReqRpcDTO.ApplyItem();
        applyItem.setAmount(new BigDecimal("0.15"));
        applyItem.setApplyTransNo("FCA1674274092884299776");
        applyItemList.add(applyItem);
        reqDTO.setApplyItems(applyItemList);

        boolean b = iCardVerificationService.applyInit(reqDTO);

    }
}
