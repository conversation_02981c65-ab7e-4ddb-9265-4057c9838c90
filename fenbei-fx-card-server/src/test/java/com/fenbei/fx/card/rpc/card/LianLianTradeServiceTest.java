package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.dto.lianlian.LianLianOrderV1PayList;
import com.fenbei.fx.card.api.card.dto.lianlian.LianLianOrderV1PayeeMerInfo;
import com.fenbei.fx.card.api.card.dto.lianlian.LianLianOrderV1StatusDTO;
import com.fenbei.fx.card.rpc.BaseJUnitTester;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class LianLianTradeServiceTest extends BaseJUnitTester {
    @Autowired
    private LianLianTradeServiceImpl lianLianTradeService;
    @Test
    public void auth() {
        LianLianOrderV1StatusDTO lianLianOrderV1StatusDTO = new LianLianOrderV1StatusDTO();
        lianLianOrderV1StatusDTO.setUserId("123456");
        lianLianOrderV1StatusDTO.setOrderNo("123457");
        lianLianOrderV1StatusDTO.setOrderStatus("SUCCESS");
        lianLianOrderV1StatusDTO.setOrderAmount("1.00");
        lianLianOrderV1StatusDTO.setOrderCurrency("CNY");
        lianLianOrderV1StatusDTO.setPayScene("VCC");
        lianLianOrderV1StatusDTO.setSettleAmount("0.99");
        lianLianOrderV1StatusDTO.setSettleCurrency("CNY");
        lianLianOrderV1StatusDTO.setSuccessTime("2024-07-29 09:55:00");
        lianLianOrderV1StatusDTO.setOrderDesc("测试单-1");
        LianLianOrderV1PayeeMerInfo payeeMerInfo = new LianLianOrderV1PayeeMerInfo();
        payeeMerInfo.setPayeeMerName("测试商家");
        payeeMerInfo.setPayeeMerCatCode("1034");
        lianLianOrderV1StatusDTO.setPayeeMerInfo(payeeMerInfo);
        List<LianLianOrderV1PayList> lianLianOrderV1PayLists = new ArrayList<>();
        LianLianOrderV1PayList lianLianOrderV1PayList = new LianLianOrderV1PayList();
        lianLianOrderV1PayList.setPayType("VCC");
        lianLianOrderV1PayList.setAmount("1.00");
        lianLianOrderV1PayList.setAccountNo("f4385a32-2db1-4403-903d-ae32f56f1051");
        lianLianOrderV1PayLists.add(lianLianOrderV1PayList);
        lianLianOrderV1StatusDTO.setPayList(lianLianOrderV1PayLists);
        boolean auth = lianLianTradeService.auth(lianLianOrderV1StatusDTO);

        System.out.println(auth);
    }
}
