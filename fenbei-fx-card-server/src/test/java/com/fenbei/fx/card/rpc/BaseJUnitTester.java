package com.fenbei.fx.card.rpc;

import com.fenbei.fx.card.dao.configuration.DaoConfiguration;
import com.fenbei.fx.card.rpc.configuration.RpcConfiguration;
import com.fenbei.fx.card.service.configuration.ServiceConfiguration;
import com.fenbei.fx.card.web.configuration.WebConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {WebConfiguration.class, RpcConfiguration.class, ServiceConfiguration.class, DaoConfiguration.class})
public class BaseJUnitTester {

    @BeforeClass
    public static void beforeClass() {
        System.setProperty("spring.profiles.active","tx-dev");
        System.setProperty("skywalking.agent.service_name","transaction-fenbei-fx-card-server");
        System.setProperty("nacos.config.server-addr","nacos.fenbeijinfu.com:8848");
        System.setProperty("nacos.config.refresh-data-id","transaction-fenbei-fx-card-server");
        System.setProperty("nacos.config.data-ids","transaction-fenbei-fx-card-server,default_port");
        System.setProperty("nacos.config.group","DEFAULT_GROUP");
        System.setProperty("log.appender","STDOUT");
    }

    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {
    }

    @AfterClass
    public static void afterClass() {
    }
}
