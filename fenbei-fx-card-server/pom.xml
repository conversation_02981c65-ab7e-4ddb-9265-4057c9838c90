<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fenbei.fx.card</groupId>
        <artifactId>fenbei-fx-card</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-fx-card-server</artifactId>
    <version>${fenbei-fx-card.version}</version>
    <packaging>jar</packaging>

    <properties>
        <skip-maven-deploy>true</skip-maven-deploy>
        <start-class>com.fenbei.fx.card.server.BootStrap</start-class>
        <version.kafka-clients>********</version.kafka-clients>
        <version.spring-kafka>1.3.8.RELEASE</version.spring-kafka>
    </properties>

    <dependencies>
        <!-- Module -->
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-rpc</artifactId>
        </dependency>
        <!-- END -->
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
