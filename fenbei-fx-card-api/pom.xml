<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fenbei.fx.card</groupId>
        <artifactId>fenbei-fx-card</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-fx-card-api</artifactId>
    <version>${fenbei-fx-card.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <!-- Module依赖 START -->
        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-exception</artifactId>
        </dependency>
        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>saas-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-base</artifactId>
        </dependency>
        <!-- Module依赖 END -->
    </dependencies>
</project>
