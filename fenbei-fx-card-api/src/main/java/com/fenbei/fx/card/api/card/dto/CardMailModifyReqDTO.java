package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-13 下午8:22
 */
@Data
public class CardMailModifyReqDTO implements Serializable {

    private String id;

    /**
     * 分贝通收件状态
     */
    private Integer fbtReceiveStatus;

    /**
     * 转运状态
     */
    private Integer forwardStatus;

    /**
     * 转运单号
     */
    private String forwardNo;

    /**
     * 转运供应商
     */
    private String forwardSupplier;

    /**
     * 收获地址变更
     */
    private String changedAddress;

    /**
     * 签收状态
     */
    private Integer signStatus;

    /**
     * 备注（限制255）
     */
    private String remark;

}
