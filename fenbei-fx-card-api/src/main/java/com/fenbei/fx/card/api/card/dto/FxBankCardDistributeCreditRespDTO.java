package com.fenbei.fx.card.api.card.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@ToString(callSuper = true)
public class FxBankCardDistributeCreditRespDTO implements Serializable {
    /**
     * 分发单号:海外预留，暂时没分发
     */
    private String distributeOrderNo;

    /**
     * 申请交易单号
     */
    private String applyTransNo;

    /**
     * 公司编号
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 分贝通卡号
     */
    private String fxCardId;

    /**
     * 银行卡号
     */
    private String bankAccountNo;

    /**
     * 银行名称简写
     */
    private String bankName;

    /**
     * 分发金额，单位为分
     */
    private BigDecimal distributeAmount;
    /**
     * 币种
     */
    private String currency;

    /**
     * 分发状态： 1成功,0失败
     */
    private Integer distributeStatus;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 失败原因Code
     */
    private Integer failureReasonCode;


}
