package com.fenbei.fx.card.api.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @date : 2023-02-01 20:44
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

}
