package com.fenbei.fx.card.api.card;

import com.fenbei.fx.card.api.card.dto.*;

import java.util.List;

/**
 * 海外卡交易
 * <AUTHOR>
 */
public interface ICardTradeService {
    /**
     * 授权接口
     * @param authorizationRpcReqDTO
     * @return
     */
    AuthorizationRpcRespDTO authorize(AuthorizationRpcReqDTO authorizationRpcReqDTO);
    /**
     * 交易接口
     */
    TransactionAckRpcRespDTO trade(TransactionAckRpcReqDTO transactionAckRpcReqDTO);

    /**
     * 待核销交易列表
     */
    List<CardOrderConvertRpcResDTO> uncheckOrderList(UncheckOrderRpcReqDTO rpcReqDTO);

    /**
     * 交易单详情
     */
    CardOrderConvertRpcResDTO orderDetail(OrderDetailRpcReqDTO rpcReqDTO);

    /**
     * 待创建费用的交易记录
     */
    Integer getUnbindCostRecordCount(UnbindCostOrderRpcReqDTO rpcReqDTO);

    /**
     * 检查企业所有交易记录是否全部核销完成
     * @param companyId
     * @return
     */
    Integer getNotCheckedCount(String companyId);

}
