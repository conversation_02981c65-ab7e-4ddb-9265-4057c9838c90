package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 海外卡额度申请
 * <AUTHOR>
 */
@Data
public class CardCreditApplyRpcReqDTO implements Serializable {
    /**
     * 审批单号
     */
    private String saasApplyNo;

    /**
     * apply_order表中的meaning_no字段
     */
    private String saasApplyMeaningNo;
    /**
     * 员工
     */
    @NotBlank
    private String employeeId;
    /**
     * 公司ID
     */
    @NotBlank
    private String companyId;

    @NotBlank
    private String bankName;
    /**
     * 申请额度,单位分
     */
    @NotNull
    @Min(0)
    private BigDecimal applyCreditAmount;
    /**
     * 币种
     */
    private String currency = "USD";
    /**
     * 额度申请原因
     */
    private String applyReason;
    /**
     * 申请原因描述
     */
    private String applyReasonDesc;
    /**
     * 费用归属id
     **/
    private String costAttributionId;
    /**
     * 归属类型
     */
    private Integer costAttributionType;
    /**
     * 费用归属名称
     */
    private String costAttributionName;
    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;
    /**
     * 费用归属list
     */
    private List<CostAttribution> attributions = new ArrayList<>();

    /**
     * 预算配置项
     */
    private Integer budgetOpt;

    /**
     * 备用金标题
     */
    private String pettyName;

    /**
     * 1普通备用金，2循环备用金
     */
    private Integer pettyType;

    /**
     * 备用金ID
     */
    private String pettyId;

    /**
     * 1申请扣，0核销扣,-1未知
     */
    private Integer deductionMode;

    /**
     * 备用金有效期
     */
    private Date expireDate;
    /**
     * 选中的海外卡卡ID
     */
    private String fxCardId;
    /**
     * 额度申请追加标签
     * 如查不到存在的额度申请记录,则默认为首次申请
     */
    private Boolean appendFlag = false;
    /**
     * 额度追加批次ID
     * applyAppendBatchId 新审批单
     *
     */
    private String applyAppendBatchId;

    /**
     * 美元兑人民币汇率
     */
    private BigDecimal usdCnyExchangeRate;

    /**
     * 人民币金额
     */
    private BigDecimal cnyAmount;
    /**
     * 费用类别
     */
    private String costType;
    /**
     * 费用类别名称
     */
    private String costTypeDesc;
}
