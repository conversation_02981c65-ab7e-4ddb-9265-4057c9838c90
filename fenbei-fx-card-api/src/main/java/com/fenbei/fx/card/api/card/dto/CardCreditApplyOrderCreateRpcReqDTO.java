package com.fenbei.fx.card.api.card.dto;

import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.saas.api.model.dto.apply.CostInfoDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 创建额度申请单请求DTO
 */
@Data
public class CardCreditApplyOrderCreateRpcReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司ID
     */
    @NotBlank
    private String companyId;

    @NotBlank
    private String employeeId;


    @NotBlank
    private String applyId;
    /**
     * 单据编号
     */
    @NotBlank
    private String meaningNo;

    /**
     * 审批单类型  50发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 50:虚拟卡发放单(小类别)
     */
    private Integer applyOrderSubType;

    /**
     * 当前生效模式
     * 1: 普通模式
     * 2: 备用金模式
     */
    private Integer activeModel;

    /**
     * 标题
     */
    @NotBlank
    @Length(max = 30)
    private String title;

    /**
     * 申请总金额(单位：元)
     */
    @NotNull
    @Min(0)
    private BigDecimal applyAmount;


    /**
     * 币种
     */
    private String currency;

    /**
     * 申请事由
     */
    @NotBlank
    @Length(max = 200)
    private String applyReason;

    /**
     * 申请事由id
     */
    private Integer applyReasonId;

    /**
     * 申请人id
     */
    @NotNull
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    @NotBlank
    private String fxCardId;

    @NotBlank
    private String bankAccountNo;

    @NotBlank
    private String bankName;

    /**
     * 制单人ID-增加注释
     */
    @NotNull
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;



    /**
     * 费用类别id
     */
    private String costCategoryId;
    /**
     * 费用类别名称
     */
    private String costCategoryName;


    /**
     * 费用归属
     */
    private CostInfo costInfo;


    /**
     * true: 员工所属部门 false: 不是
     */
    private boolean employeeDept;

}
