package com.fenbei.fx.card.api.card.dto.lianlian;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianOrderV1StatusDTO implements Serializable {

    private String userId;

    private String orderNo;

    private String orderStatus;

    private String orderAmount;

    private String orderCurrency;
    /**
     * CASHIER 收银台
     * ENT_CODE 企业码
     * VCC 跨境虚拟卡
     * TOKEN TOKEN支付
     */
    private String payScene;

    private String settleAmount;

    private String settleCurrency;
    /**
     * 交易成功时间
     * 可选
     * 只有当交易成功时返回
     * 正则匹配:
     * yyyy-MM-dd HH:mm:ss
     */
    private String successTime;

    private String failCode;

    private String failMsg;

    private String orderDesc;


    private LianLianOrderV1PayeeMerInfo payeeMerInfo;

    private List<LianLianOrderV1PayList> payList;
}
