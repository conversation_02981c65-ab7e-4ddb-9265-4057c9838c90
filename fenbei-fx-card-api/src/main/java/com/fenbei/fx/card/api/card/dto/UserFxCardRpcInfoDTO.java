package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-07 下午4:23
 */
@Data
public class UserFxCardRpcInfoDTO implements Serializable {

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatformName;

    /**
     * 发卡渠道 图标
     */
    private String cardPlatformIcon;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡的品牌 图标
     */
    private String cardBrandIcon;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;


    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private Integer activeStatus;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 币种 USD
     */
    private String currency;

    /**
     * 币种符号 $
     */
    private String currencySymbol;

    /**
     * 币种 美元
     */
    private String currencyName;

    /**
     * 卡可用余额
     */
    private BigDecimal balance;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 是否计费
     */
    private Boolean isContinueCharging;
}
