package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ApplyCreditApplyAppRpcVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    private String id;
    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    //费用侧申请单ID
    private String applyId;

    /**
     * 发放单单据主键ID
     */
    private String applyOrderId;

    /**
     * 审批单类型  50发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 申请单ID（支付测）
     */
    private String applyTransNo;

    /**
     * 申请单批次: 同一申请单可追加（支付测）
     */
    private String applyTransBatchNo;

    /**
     * 是否是虚拟卡自定义申请单
     */
    private Boolean isVirtualCustomForm = true;

    private String applyTime;
    /**
     * 关联金额 (单位：元)
     */
    private BigDecimal operationAmount;
    /**
     * 币种 美元-USD
     */
    private String currency;
    /**
     * 申请金额(单位：元)
     */
    private BigDecimal applyAmount;

    private String applyAmountShow;
    /**
     * 可使用余额(单位：元)
     */
    private BigDecimal useBalance;


    private String useBalanceShow;
    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 卡模式: 1.普通模式,2备用金模式
     */
    private Integer cardModel;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 费用类别
     */
    private String costType;

    /**
     * 费用归属
     */
    private String costAttribution;

    /**
     * 美元兑人民币汇率
     */
    private BigDecimal usdCnyExchangeRate;


}
