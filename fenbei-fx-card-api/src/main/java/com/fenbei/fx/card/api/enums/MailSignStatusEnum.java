package com.fenbei.fx.card.api.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 邮寄签收状态
 */
@AllArgsConstructor
public enum MailSignStatusEnum {

    /**
     * 签收状态 1.未签收 2 已签收 3 异常
     */
    UNSIGNED(1, "未签收"),
    SIGNED(2, "已签收"),
    EXCEPTION(3,"异常"),
    ;

    public static MailSignStatusEnum getEnum(Integer code) {
        for (MailSignStatusEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
