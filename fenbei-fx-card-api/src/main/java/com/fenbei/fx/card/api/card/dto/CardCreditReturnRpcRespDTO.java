package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class CardCreditReturnRpcRespDTO implements Serializable {
    private String companyId;
    private String employeeId;
    private String fxCardId;
    /**
     * 卡余额
     */
    private BigDecimal cardBalance;
    /**
     * 卡状态
     */
    private Integer cardStatus;
    /**
     * 退还金额
     */
    private BigDecimal refundAmount;
    /**
     * 申请交易单号
     */
    private String applyTransNo;

    /**
     * 申请额度单号（费控方）
     */
    private String bizNo;
}
