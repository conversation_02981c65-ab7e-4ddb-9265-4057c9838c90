package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 批量发放额度请求DTO
 */
@Data
public class CardCreditApplyOrderBatchTrySendRpcReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    @NotBlank(message = "公司ID不能为空")
    private String companyId;

    /**
     * 申请单ID列表
     */
    @NotEmpty(message = "申请单ID列表不能为空")
    private List<String> applyOrderIds;
}
