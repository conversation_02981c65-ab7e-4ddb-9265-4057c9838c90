package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 企业下发总额 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-07-24
 */
@Data
public class CompanyTotalAmountRpcResDTO extends BaseModel {

    /**
     * 余额
     */
    private BigDecimal allBalance = BigDecimal.ZERO;

    /**
     * 执行结果 默认为 false
     */
    private boolean result = false;
}
