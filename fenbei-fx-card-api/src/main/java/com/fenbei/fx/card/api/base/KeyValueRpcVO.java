package com.fenbei.fx.card.api.base;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@Data
public class KeyValueRpcVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer key;

    private String type;

    private String value;

    private String color;

    public KeyValueRpcVO(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public KeyValueRpcVO(String type, String value){
        this.type = type;
        this.value = value;
    }

    public KeyValueRpcVO() {
    }
}
