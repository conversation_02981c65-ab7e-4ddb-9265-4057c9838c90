package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午3:44
 */
@Data
public class CardCreditStereoRpcQueryDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    private String companyId;

    /**
     * 类型: 1额度申请,2额度退回
     */
    private Integer applyType = 1;

    /**
     * 申请单号
     */
    private String applyTransNo;

    /**
     * 申请单号（meaningNo）
     */
    private String applyMeaningNo;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 申请人
     */
    private String applyOperationUserName;

    /**
     * 申请开始时间
     */
    private Date beginApplyTime;

    /**
     * 申请结束时间
     */
    private Date endApplyTime;

    /**
     * 费用归属
     */
    private String costAttribution;

    /**
     * 申请状态 1成功,0失败
     */
    private Integer applyStatus;

    /**
     * 是否查看有效金额 是 true 否 false
     */
    private Boolean avalibleAmountFlag = false;


    private  Integer pageNo = 1;

    private Integer pageSize = 10;

}
