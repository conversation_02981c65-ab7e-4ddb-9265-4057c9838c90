package com.fenbei.fx.card.api.base;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 价格
 */
@Data
public class PriceRpcVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 颜色
     */
    private String color;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 显示金额
     */
    private String showPrice;

    /**
     * 币种
     */
    private String currencyCode;

}
