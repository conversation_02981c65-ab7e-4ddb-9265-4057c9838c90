package com.fenbei.fx.card.api.card;


import com.fenbei.fx.card.api.card.dto.FxCostBindReqRpcDTO;
import com.fenbei.fx.card.api.card.dto.FxPettyRespRpcDTO;
import com.fenbei.fx.card.api.card.dto.FxVerificationReqRpcDTO;

import java.util.List;

/**
 * Created by FBT on 2023/6/5.
 */
public interface ICardVerificationService {

    /**
     * 虚拟卡核销单：交易记录绑定 （费用创建）
     * @param reqRpcDTO
     */
    boolean costBind(FxCostBindReqRpcDTO reqRpcDTO);

    /**
     * 虚拟卡核销单：交易记录解绑  （费用删除、交易记录删除）
     * @param reqRpcDTO
     */
    boolean costUnBind(FxCostBindReqRpcDTO reqRpcDTO);


    /**
     * 核销中：虚拟卡核销申请单提交审批
     * @param initDTO
     */
    boolean applyInit(FxVerificationReqRpcDTO initDTO);


    /**
     * 将订单更改为待核销或无需核销
     * (审批撤回、被驳回)
     * @param delDTO
     */
    boolean applyDel(FxVerificationReqRpcDTO delDTO);


    /**
     * 审核通过撤回
     * @param delDTO
     */
    boolean applyDisCardDel(FxVerificationReqRpcDTO delDTO);

    /**
     * 已核销：审批通过
     * @param doneDTO
     */
    boolean applyDoneNew(FxVerificationReqRpcDTO doneDTO);

    /***
     * 是否包含循环备用
     */
    FxPettyRespRpcDTO queryRoundPettyByOrderIdList (List<String> orderIdList);

}
