package com.fenbei.fx.card.api.card;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-13 下午6:23
 */

import com.fenbei.fx.card.api.card.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.api.card.dto.CardMailRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.CardMailRpcRespDTO;
import com.fenbei.fx.card.api.card.dto.PageDTO;

/**
 * 邮寄rpc接口
 */
public interface ICardMailService {

    /**
     * 邮寄管理查询+导出
     * @param cardMailRpcReqDTO
     * @return
     */
     PageDTO<CardMailRpcRespDTO> queryCardMailStereoPage(CardMailRpcReqDTO cardMailRpcReqDTO);

    /**
     * 邮寄管理编辑
     * @param cardMailModifyReqDTO
     * @return
     */
     Integer modifyCardMailStereo(CardMailModifyReqDTO cardMailModifyReqDTO);
}
