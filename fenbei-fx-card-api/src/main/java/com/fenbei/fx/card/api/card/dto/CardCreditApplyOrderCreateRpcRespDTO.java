package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 创建额度申请单响应DTO
 */
@Data
public class CardCreditApplyOrderCreateRpcRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 单据编号
     */
    private String meaningNo;

    /**
     * 单据主键ID
     */
    private String applyOrderId;

    /**
     * -1:制单失败 0:制单中 1.制单成功(待发放) 2.发放中 3.下发额度成功 4.下发额度失败
     */
    private Integer applyState;

    private String applyResultDesc;


    /**
     * 制单人ID-增加注释
     */
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;


    /**
     * 发放人ID
     */
    private String issuedId;

    /**
     * 发放人名称
     */
    private String issuedName;

    /**
     * 发放时间
     */
    private Date issuedTime;


}
