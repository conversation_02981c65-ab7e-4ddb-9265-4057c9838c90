package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-13 下午8:03
 */
@Data
public class CardMailRpcRespDTO implements Serializable {

    private String id;

    private String cardOpenDate;

    private String employeeName;

    private String employeePhone;

    private String cardPlatformDesc;

    private String cardNo;

    //卡序列号  line2
    private String cardMailNo;

    private String forwardFlagDesc;
    /**
     * 分贝通收件状态  1-未收件，2-已收件，3-异常
     */
    private String fbtReceiveStatusDesc;

    private String forwardStatusDesc;

    private String forwardNo;

    private String forwardSupplier;

    private String signStatusDesc;

    private String remark;

    private String changedAddress;

    /**
     * 用户邮寄地址
     */
    private AddressRpcDto userPostalAddressDto;

}
