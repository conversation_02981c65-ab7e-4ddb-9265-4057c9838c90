package com.fenbei.fx.card.api.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 生效模式
 */
@AllArgsConstructor
public enum FbtReceiveStatusEnum {

    /**
     * 分贝通收件状态 1.未收件 2 已收件 3 异常
     */
    UNRECEIVED(1, "未收件"),
    RECEIVED(2, "已收件"),
    EXCEPTION(3,"异常"),
    ;

    public static FbtReceiveStatusEnum getEnum(Integer code) {
        for (FbtReceiveStatusEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
