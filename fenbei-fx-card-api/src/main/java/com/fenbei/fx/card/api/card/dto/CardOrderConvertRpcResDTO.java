package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.KeyValueRpcVO;
import com.fenbei.fx.card.api.base.PriceRpcVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡订单 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOrderConvertRpcResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * "shopName": "中国银联无卡快捷支付业务二级商户信息测试",
     * 商户名称：对应库表merchant_name
     */
    private String shopName;
    /**
     * 交易人名币金额
     */
    private PriceRpcVo cnyTradePrice;
    /**
     * 折算金额,美元
     */
    private PriceRpcVo totalPrice;

    /**
     * 折算币种
     */
    private String billCurrencyCode;

    /**
     * 交易金额
     */
    private PriceRpcVo tradePrice;

    /**
     * 交易币种
     */
    private String currencyCode;

    /**
     * "bankAccountNo": "**** 7817",//卡号：masked_card_number,
     */
    private String bankAccountNo;
    /**
     * bankAccountNo
     */
    private String bankAccountNoMasked;
    /**
     * 核销状态
     */
    private KeyValueRpcVO checkStatus;

    private KeyValueRpcVO transactionType;
    /**
     * "createTime": "2022-12-28 16:52:57",
     */
    private Date createTime;
    /**
     * "createTimeShow": "2022/12/28 16:52",//交易时间transaction_date(格林威治时间) todo
     */
    private String createTimeShow;
    /**
     *  "monthType": "2022年12月",
     */
    private String monthType;
    /**
     * *  "orderId": "OBK221228165257145572972",//交易单号
     */
    private String orderId;

    /**
     * * 原交易单号
     */
    private String oriOrderId;

    /**
     * "tradeAddress":"USA-NEW",
     */
    private String tradeAddress;
    /**
     * "uncheckConsume": 0,//未核销金额
     */
    private BigDecimal uncheckConsume;
    /**
     *  "uncheckUse": 0,//未核销次数
     */
    private Integer uncheckUse;
    /**
     * 银行名称，通道
     */
    private String bankName;

    /** 银行中文名称 */
    private String bankNameString;
    /**
     * 交易备注
     */
    private String tradeRemark;
    /**
     * 交易币种兑人民币汇率
     */
    private BigDecimal tradeCnyExchangeRate;

    /** 0 未关联新的交易记录 1 关联了新的交易记录*/
    private String isNew;

    /** 银行+卡号后四位缩写 */
    private String bankDesc;
    /** 交易记录备注 */
    private String remarks;

    /** 备用金ID */
    private String pettyId;
    /** 2022.8.10日 增加 事由*/
    private String applyReason;
    /** 备用金名称 */
    private String title;
    /** 备用金名称 */
    private String pettyName;
    /** 审批单号 */
    private String bizNo;
    /** 是否存在关联交易 4.7.1添加 */
    private boolean hasRelate;
    /** 4.22日 增加 */
    private String pettyCreateTime;
    private BigDecimal applyAmount;

    //是否隐藏还款入口
    private boolean hasPayBackEntry;
    /** 是否创建费用: 1 未创建费用     2 已创建费用 */
    private KeyValueRpcVO bankBindStatus;
    /** 父级交易编号 */
    private String fbOrderId;
    /** 平安银行虚拟卡交易记录联动 新增字段*/
    private String rootOrderId;
    /** 还款状态 0无还款 1已还款 2 部分还款*/
    private KeyValueRpcVO payBackStatus;
    /** 已还款金额 */
    private PriceRpcVo payBackPrice;
    /** 退款状态 0无退款，1已退款，2已退款   （1部分退款，2全额退款,key不一样，value都是已退款） */
    private KeyValueRpcVO refundStatus;
    /** 退款金额 */
    private PriceRpcVo refundPrice;
    /**支付方式  */
    private String payMethod;


}
