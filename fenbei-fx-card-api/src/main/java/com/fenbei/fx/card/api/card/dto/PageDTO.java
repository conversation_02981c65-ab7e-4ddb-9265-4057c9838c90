package com.fenbei.fx.card.api.card.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageDTO<T> implements Serializable {

    @JsonProperty("currentPage")
    private Integer currentPage; // 当前页

    @JsonProperty("pageSize")
    private Integer pageSize; // 查询数量

    @JsonProperty("totalPages")
    private Integer totalPages; // 总页数

    @JsonProperty("totalSize")
    private Integer totalSize; // 总条数

    private List<T> list;

}
