package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 待核销单据列表
 * <AUTHOR>
 */
@Data
public class UncheckOrderRpcReqDTO extends BaseModel {
    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employId;

    /**
     * 费用id
     */
    private String costId;

    /**
     * 交易日期 yyyy-MM-dd
     */
    private String beginTradeTime;
    private String endTradeTime;

    /**
     * 交易币种
     * 单位 元
     */
    private String tradeCurrency;
    private BigDecimal beginTradeAmount;
    private BigDecimal endTradeAmount;

    /**
     * 结算币种
     * 单位 元
     */
    private String billTradeCurrency;
    private BigDecimal beginBillTradeAmount;
    private BigDecimal endBillTradeAmount;

    /**
     * 人名币
     */
    private BigDecimal beginCnyTradeAmount;
    private BigDecimal endCnyTradeAmount;


    /**
     * 排序
     * 参考 UnCheckOrderSortByEnum
     * 1.默认顺序
     * 2.按交易日期升序
     * 3.按交易日期降序
     * 4.按交易类型分组排序(则按照交易时间由近至远展示且将关联的交易记录相邻展示)
     */
    private Integer sortBy;


}
