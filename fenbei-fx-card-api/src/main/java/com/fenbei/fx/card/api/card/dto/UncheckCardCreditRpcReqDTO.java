package com.fenbei.fx.card.api.card.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 国际卡额度申请退回管理表 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Data
@NoArgsConstructor
public class UncheckCardCreditRpcReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;
    /**
     * 用户id
     */
    private String employeeId;


    /**
     * 卡id
     */
    private String fxCardId;


    /**
     * 待核销订单
     */
    private List<String> orderIds;


}
