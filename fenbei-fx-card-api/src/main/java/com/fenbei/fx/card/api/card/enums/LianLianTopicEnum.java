package com.fenbei.fx.card.api.card.enums;

public enum LianLianTopicEnum {
    PRE_AUTH("order.v1.status"),
    SETTLEMENT("order.v1.settle.success"),

    REFUND("order.v1.refund"),

    REVERSE("order.v1.reverse"),

    CARD_V1_APPLY_RESULT("card.v1.apply.result")
    ;

    private final String topicKey;

    LianLianTopicEnum(String topicKey){
        this.topicKey = topicKey;
    }

    public String getTopicKey() {
        return topicKey;
    }
}
