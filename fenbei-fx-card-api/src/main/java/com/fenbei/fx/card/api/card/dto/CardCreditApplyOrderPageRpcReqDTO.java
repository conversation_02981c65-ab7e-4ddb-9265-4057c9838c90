package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分页查询额度申请单请求DTO
 */
@Data
public class CardCreditApplyOrderPageRpcReqDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 申请人ID
     */
    private String applicantId;

    /**
     * 申请单ID
     */
    private String applyOrderId;

    /**
     * 业务编号
     */
    private String meaningNo;

    /**
     * 申请状态
     */
    private Integer applyState;

    /**
     * 申请开始时间
     */
    private Date beginApplyTime;

    /**
     * 申请结束时间
     */
    private Date endApplyTime;
}