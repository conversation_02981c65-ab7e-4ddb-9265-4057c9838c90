package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分页查询额度申请单响应DTO
 */
@Data
public class CardCreditApplyOrderPageRpcRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请单ID
     */
    private String applyOrderId;

    /**
     * 业务编号
     */
    private String meaningNo;

    /**
     * 申请人ID
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请标题
     */
    private String title;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请状态
     */
    private Integer applyState;

    /**
     * 申请状态名称
     */
    private String applyStateName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 创建时间
     */
    private Date createTime;
}