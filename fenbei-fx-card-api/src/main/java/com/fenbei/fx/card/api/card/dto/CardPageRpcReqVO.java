package com.fenbei.fx.card.api.card.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡 分页 ReqVO
 *
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPageRpcReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 卡片上的姓名
     */
    private String name;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;


    private String phone ;

    private String companyName;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 用户ID
     */
    private String employeeId;

    private String address ;

    private String carPublicTimeStart;

    private String carPublicTimeEnd;

    private  Integer pageNo;

    private Integer pageSize;

    private String cardPlatform;

}
