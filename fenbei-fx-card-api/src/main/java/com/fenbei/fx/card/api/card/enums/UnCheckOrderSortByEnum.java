package com.fenbei.fx.card.api.card.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 未核销交易定单排序规则
 */
@Getter
@AllArgsConstructor
public enum UnCheckOrderSortByEnum {

    /**
     * 排序
     * 1.默认顺序
     * 2.按交易日期升序
     * 3.按交易日期降序
     * 4.按交易类型分组排序(则按照交易时间由近至远展示且将关联的交易记录相邻展示)
     */
    UNKNOW(-1, "未定义"),
    DEFAULT(1, "默认顺序"),
    TRADE_TIME_ASC(2, "按交易日期升序"),
    TRADE_TIME_DESC(3, "按交易日期降序"),
    TYPE_TRADE_TIME_DESC(4, "按交易类型分组排序(则按照交易时间由近至远展示且将关联的交易记录相邻展示)"),

    ;

    private final Integer code;

    private final String msg;


    public static boolean isTradeTimeAsc(Integer code){
        return Objects.equals(code, TRADE_TIME_ASC.code);
    }

    public static boolean isTradeTimeDesc(Integer code){
        return Objects.equals(code, TRADE_TIME_DESC.code);
    }

    public static boolean isTypeTradeTimeDesc(Integer code){
        return Objects.equals(code, TYPE_TRADE_TIME_DESC.code);
    }
}
