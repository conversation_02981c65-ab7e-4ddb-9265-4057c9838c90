package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 查询额度申请单详情响应DTO
 */
@Data
public class CardCreditApplyOrderDetailRpcRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请单ID
     */
    private String applyOrderId;

    /**
     * 申请批次号
     */
    private String applyBatchNo;

    /**
     * 业务编号
     */
    private String meaningNo;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 申请人ID
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人部门
     */
    private String applicantDepartment;

    /**
     * 申请标题
     */
    private String title;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 申请状态
     */
    private Integer applyState;

    /**
     * 申请结果描述
     */
    private String applyResultDesc;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 扣款模式
     */
    private Integer deductionMode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 发放时间
     */
    private Date issuedTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}