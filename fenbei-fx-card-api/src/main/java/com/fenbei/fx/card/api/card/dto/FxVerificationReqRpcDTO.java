package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FxVerificationReqRpcDTO extends BaseModel {

    /**
     * 每个单号关联的核销金额
     */
    @NotNull
    private List<Item> items;

    /**
     * 关联的申请单
     */
    private List<ApplyItem> applyItems;

    /**
     * 费用ID
     */
    //private Integer costId;

    /**
     * 核销单号
     */
    private String duringApplyId;

    @Data
    public static class Item extends BaseModel {

        private String bizNo;

        /**
         * 单位（美元）
         */
        private BigDecimal amount;

        /**
         * 交易类型
         */
        private Integer type;

    }

    @Data
    public static class ApplyItem extends BaseModel {

        /**
         * 申请单号
         */
        private String applyTransNo;

        /**
         * 申请单批次单号
         */
        private String applyTransBatchNo;

        /**
         * 本次申请核销金额（美元）
         */
        private BigDecimal amount;

    }

}
