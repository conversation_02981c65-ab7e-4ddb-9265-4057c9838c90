package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批量发放额度响应DTO
 */
@Data
public class CardCreditApplyOrderBatchTrySendRpcRespDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 批量发放结果详情
     */
    private List<BatchTrySendResult> results;

    @Data
    public static class BatchTrySendResult implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 申请单ID
         */
        private String applyOrderId;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 结果消息
         */
        private String message;

        /**
         * 发放状态
         */
        private Integer applyState;

        /**
         * 发放状态描述
         */
        private String applyStateDesc;

        /**
         * 申请人姓名
         */
        private String applicantName;

        /**
         * 申请金额
         */
        private BigDecimal applyAmount;

        /**
         * 币种
         */
        private String currency;
    }
}
