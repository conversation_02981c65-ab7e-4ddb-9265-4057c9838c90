package com.fenbei.fx.card.api.card;

import com.fenbei.fx.card.api.card.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 用户服务 RPC Service 接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
public interface ICardService {

    /**
     * 查余额
     */
    List<CardBalanceRpcRespDTO> getBalance(CardBalanceRpcReqDTO rpcReqDTO);


    /**
     * 用户的有权限的海外卡信息
     */
    List<UserFxcardRpcRespDTO> findUserFxcardInfos(UserFxcardRpcReqDTO rpcReqDTO);


    /**
     * 查询待审核列表
     * @return
     */
    PageDTO<CardPageRpcResVO> getPendingCardsByPage(CardPageRpcReqVO cardPageReqVO);

    /**
     * lianlian无实体卡，so无需FBT运营审核，so无Stereo审批开卡，此接口对连连无效
     * @param getApplyId
     * @return
     */
    boolean approvedCardApply (String getApplyId);

    PageDTO<CardPageRpcResVO> getAllCardApplyByPage(CardPageRpcReqVO cardPageReqVO);

    List<Map<Integer,String>> queryStatusEnum();

    /**
     * fxCardId获取卡信息
     */
    UserFxCardRpcInfoDTO findUserCardInfoByFxCardId(String fxCardId);

    /**
     * 查询企业下所有员工帐户中有金额的（余额和冻结余额）
     * @param companyId 公司ID
     * @return int
     */
    Integer getCardBalanceGreaterZeroByCompanyId(String companyId);

    /**
     * 查询公司下发总额度（不含回收额度）
     *
     * @param companyTotalAmountRpcReqDTO 企业下发总额 ReqDTO
     * @return CompanyTotalAmountRpcRespDTO 企业下发总额 ResDTO
     */
    CompanyTotalAmountRpcResDTO getCompanyDistributeTotalAmount(CompanyTotalAmountRpcReqDTO companyTotalAmountRpcReqDTO);


    /**
     * 查询员工下发总额度（不含回收额度）
     *
     * @param employeeTotalAmountRpcReqDTO 员工下发总额 ReqDTO
     * @return EmployeeTotalAmountRpcResDTO 员工下发总额 ResDTO
     */
    EmployeeTotalAmountRpcResDTO getEmployeeDistributeTotalAmount(EmployeeTotalAmountRpcReqDTO employeeTotalAmountRpcReqDTO);

    /**
     * 查询员工消费总额度（不含退款额度）
     *
     * @param consumeTotalAmountRpcReqDTO 员工消费总额 ReqDTO
     * @return ConsumeTotalAmountRpcResDTO 员工消费总额 ResDTO
     */
    ConsumeTotalAmountRpcResDTO getEmployeeConsumeTotalAmount(ConsumeTotalAmountRpcReqDTO consumeTotalAmountRpcReqDTO);

    /**
     * 冻结个人海外卡，禁止消费功能
     *
     * @param employeeFreezeCardRpcReqDTO 员工个人卡冻结 ReqDTO
     * @return EmployeeFreezeCardRpcResDTO 员工个人卡冻结 ResDTO
     */
    EmployeeFreezeCardRpcResDTO freezeEmployeeCardNotConsume(EmployeeFreezeCardRpcReqDTO employeeFreezeCardRpcReqDTO);


    CardRpcRespVO queryCardInfo(CardRpcReqVO cardRpcReqVO);
}
