package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CardRpcRespVO extends BaseModel {


    /**
     * 公司id
     */
    private String companyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 用户id（企业卡的持有人，个人卡的同cardholder里面的用户ID）
     */
    private String employeeId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private Integer activeStatus;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 卡可用余额
     */
    private BigDecimal balance;
}
