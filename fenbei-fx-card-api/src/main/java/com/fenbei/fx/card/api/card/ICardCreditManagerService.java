package com.fenbei.fx.card.api.card;

import com.fenbei.fx.card.api.card.dto.*;

import java.util.List;

/**
 * 海外卡额度管理
 * <AUTHOR>
 */
public interface ICardCreditManagerService {
    /**
     * 额度申请
     */
    CardCreditApplyRpcRespDTO apply(CardCreditApplyRpcReqDTO cardCreditApplyRpcReqDTO);

    /**
     * 额度退回
     */
    CardCreditReturnRpcRespDTO refund(CardCreditReturnRpcReqDTO cardCreditReturnRpcReqDTO);

    /**
     * 申请单额度发放详情
     */
    List<FxBankCardDistributeCreditRespDTO> distributeDetailList(FxCreditDistributeQueryReqDTO reqDTO);


    /**
     * 待核销申请单
     */
    List<ApplyCreditApplyAppRpcVO> queryUncheckApply(UncheckCardCreditRpcReqDTO rpcReqDTO);

    /**
     * 待核销申请单
     */
    UncheckCreditApplyAndStatisticRpcVO queryUncheckApplyAndStatistic(UncheckCardCreditRpcReqDTO rpcReqDTO);

    /**
     * 额度申请详情查询
     * @param companyId
     * @param bizNo
     * @return
     */
    List<ApplyCreditApplyVoucherRpcVO> applyList(String companyId, String bizNo);
}
