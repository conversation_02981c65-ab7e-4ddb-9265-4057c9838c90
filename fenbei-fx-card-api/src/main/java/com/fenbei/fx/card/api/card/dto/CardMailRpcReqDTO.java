package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-13 下午8:03
 */
@Data
public class CardMailRpcReqDTO implements Serializable {


    private String employeeName;

    private String employeePhone;

    private String cardPlatform;

    private String cardNo;

    /**
     *卡识别号
     */
    private String cardMailNo;

    private Integer forwardFlag;

    /**
     * 分贝通收件状态  1-未收件，2-已收件，3-异常
     */
    private Integer fbtReceiveStatus;

    private Integer forwardStatus;

    private String forwardNo;

    private String createTimeStart;

    private String createTimeEnd;

    private Integer signStatus;

    private Integer pageNo=1;

    private Integer pageSize=10;
}
