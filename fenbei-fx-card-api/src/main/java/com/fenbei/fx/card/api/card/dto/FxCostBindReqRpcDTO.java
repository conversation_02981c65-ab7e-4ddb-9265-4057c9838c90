package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FxCostBindReqRpcDTO extends BaseModel {

    /**
     * 交易编号
     */
    @NotNull
    private List<String> bizNos;

    @NotBlank
    private Integer costId;

}
