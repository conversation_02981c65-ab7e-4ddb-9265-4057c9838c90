package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import com.finhub.framework.validator.constant.GenderEnum;
import com.finhub.framework.validator.intarray.InEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 卡余额 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-24
 */
@Data
public class CardBalanceRpcReqDTO extends BaseModel {

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 渠道
     */
    private String platform;

}
