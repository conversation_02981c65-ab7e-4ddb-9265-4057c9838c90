package com.fenbei.fx.card.api.card.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class UncheckCreditApplyAndStatisticRpcVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;


    /**
     * 待核销申请单列表
     */
    private List<ApplyCreditApplyAppRpcVO> dataList;


    /**
     * 待核销申请单数量
     */
    private Integer totalCount = 0;

    /**
     * 选定订单的待核销金额
     */
    private BigDecimal conditionExt = new BigDecimal("0.00");


    /**
     * 币种 美元-USD
     */
    private String currency;
    /**
     * 币种 美元-$
     */
    private String currencySymbol;

}
