package com.fenbei.fx.card.api.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 生效模式
 */
@AllArgsConstructor
public enum ForwardStatusEnum {

    /**
     * 转运状态 1.未寄出 2 已寄出
     */
    NOT_SEND_OUT(1, "未寄出"),
    SEND_OUT(2, "已寄出"),
    ;

    public static ForwardStatusEnum getEnum(Integer code) {
        for (ForwardStatusEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
