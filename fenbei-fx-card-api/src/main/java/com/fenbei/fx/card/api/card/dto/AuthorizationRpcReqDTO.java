package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AuthorizationRpcReqDTO extends BaseModel {
    /**
     * 卡平台
     */
    private String cardPlatform;
    /**
     * 账户ID
     */
    private String accountId;
    /**
     * 卡ID
     */
    private String cardId;

    /**
     * 交易ID
     */
    private String transactionId;
    /**
     * 交易类型
     * Possible values: CLEARING, AUTHORIZATION
     */
    private String transactionType;
    /**
     * 交易时间
     */
    private String transactionDate;
    /**
     * 交易币种
     */
    private String transactionCurrency;
    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 商户信息
     */
    private TransactionMerchant merchant;

    /**
     * 授权码
     * retrieve the transaction: 检索交易
     */
    private String authCode;

    /**
     * 卡号
     */
    private String maskedCardNumber;

    /**
     * 检索参考号
     */
    private String retrievalRef;

    /**
     * 客户端数据
     */
    private String clientData;

    /**
     * 卡昵称
     */
    private String cardNickname;

    /**
     * 网络交易ID
     */
    private String networkTransactionId;
    /**
     * 订单
     */
    private List<AuthorizationBillingOrder> billingOrder;


}
