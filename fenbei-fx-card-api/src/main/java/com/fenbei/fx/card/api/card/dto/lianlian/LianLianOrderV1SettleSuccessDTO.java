package com.fenbei.fx.card.api.card.dto.lianlian;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LianLianOrderV1SettleSuccessDTO implements Serializable {

    private String orderNo;

    private String orderAmount;

    private String orderCurrency;

    private String settleType;

    private String settleAmount;
    /**
     * 币种参考：https://www.iso.org/iso-4217-currency-codes.html
     */
    private String settleCurrency;
    /**
     * 交易成功时间
     * 可选
     * 只有当交易成功时返回
     * 正则匹配:
     * yyyy-MM-dd HH:mm:ss
     */
    private String settleDate;
}
