package com.fenbei.fx.card.api.card.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录 stereo 分页 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
public class CardTradeInfoStereoPageRpcReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    private String companyId;

    /**
     * 持卡人
     * 示例：张三
     */
    private String cardHolderName;

    /**
     * 卡号
     * 示例：****************
     */
    private String maskedCardNumber;

    /**
     * 卡片类型
     * 示例：1-PHYSICAL、2-VIRTUAL
     */
    private Integer bankCardType;

    /**
     * 消费类型
     * 示例：11-消费 12-退款
     */
    private Integer transactionType;

    /**
     * 交易时间（大于等于）
     * 示例：2021-05-20 00:00:00
     */
    private Date transactionGeDate;

    /**
     * 交易时间（小于等于）
     * 示例：2021-05-20 23:59:59
     */
    private Date transactionLeDate;

    /**
     * 创建时间（大于等于）
     * 示例：2021-05-20 00:00:00
     */
    private Date createGeDate;

    /**
     * 创建时间（小于等于）
     * 示例：2021-05-20 23:59:59
     */
    private Date createLeDate;

    /**
     * 交易币种
     */
    private String transactionCurrency;

    /**
     * 交易金额（大于等于）
     */
    private BigDecimal transactionGeAmount;

    /**
     * 交易金额（小于等于）
     */
    private BigDecimal transactionLeAmount;

    /**
     * 核销状态 CheckstatusEnum
     */
    private Integer checkStatus;


    private  Integer pageNo = 1;

    private Integer pageSize = 10;

    private String cardPlatform;
}
