package com.fenbei.fx.card.api.card.dto;

import com.fenbei.fx.card.api.base.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易通知请求
 * <AUTHOR>
 */
@Data
public class TransactionAckRpcReqDTO extends BaseModel {
    /**
     * 通道
     */
    private String channel;
    /**
     * 原始通知ID
     */
    private String noticeId;
    /**
     * 交易类型
     * webhook中包含五种类型,详见枚举
     */
    private String transactionType;
    /**
     * 交易ID
     */
    private String transactionId;
    /**
     * 交易时间
     */
    private String transactionDate;
    /**
     * 交易币种
     */
    private String transactionCurrency;
    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;
    /**
     * 交易状态
     */
    private String status;
    /**
     * 检索参考号
     */
    private String retrievalRef;
    /**
     * 网络交易ID
     */
    private String networkTransactionId;
    /**
     * 发送日期
     */
    private String postedDate;
    /**
     * 商户信息
     */
    private TransactionMerchant merchant;
    /**
     * 商户授权码
     */
    private List<String> matchedAuthorizations;
    /**
     * 卡号
     */
    private String maskedCardNumber;
    /**
     * 交易失败原因
     */
    private String failureReason;
    /**
     * 客户端数据
     */
    private String clientData;
    /**
     * 卡昵称
     */
    private String cardNickname;
    /**
     * 卡ID
     */
    private String cardId;
    /**
     * 账单币种
     */
    private String billingCurrency;
    /**
     * 账单金额
     */
    private BigDecimal billingAmount;
    /**
     * 授权码
     * retrieve the transaction: 检索交易
     */
    private String authCode;
}
