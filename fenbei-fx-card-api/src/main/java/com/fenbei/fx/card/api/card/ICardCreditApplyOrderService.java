package com.fenbei.fx.card.api.card;

import com.fenbei.fx.card.api.card.dto.*;

/**
 * 额度申请单管理接口
 * <AUTHOR>
 */
public interface ICardCreditApplyOrderService {

    /**
     * 创建额度申请单
     * @param createReqDTO 创建请求参数
     * @return 创建结果
     */
    CardCreditApplyOrderCreateRpcRespDTO createApplyOrder(CardCreditApplyOrderCreateRpcReqDTO createReqDTO);

    /**
     * 尝试发放额度
     * @param trySendReqDTO 发放请求参数
     * @return 发放结果
     */
    CardCreditApplyOrderTrySendRpcRespDTO trySend(CardCreditApplyOrderTrySendRpcReqDTO trySendReqDTO);

    /**
     * 批量发放额度
     * @param batchTrySendReqDTO 批量发放请求参数
     * @return 批量发放结果
     */
    CardCreditApplyOrderBatchTrySendRpcRespDTO batchTrySend(CardCreditApplyOrderBatchTrySendRpcReqDTO batchTrySendReqDTO);

    /**
     * 查询额度申请单详情
     * @param queryReqDTO 查询请求参数
     * @return 申请单详情
     */
    CardCreditApplyOrderDetailRpcRespDTO getApplyOrderDetail(CardCreditApplyOrderDetailRpcReqDTO queryReqDTO);

    }
