<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardchargingnotice.CardChargingNoticeDAO">
    <resultMap id="CardChargingNoticeResultMap" type="com.fenbei.fx.card.dao.cardchargingnotice.po.CardChargingNoticePO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="event_type" property="eventType" jdbcType="TINYINT"/>
        <result column="trade_amount" property="tradeAmount" jdbcType="DECIMAL"/>
        <result column="card_status" property="cardStatus" jdbcType="TINYINT"/>
        <result column="call_status" property="callStatus" jdbcType="TINYINT"/>
        <result column="call_num" property="callNum" jdbcType="TINYINT"/>
        <result column="call_next_time" property="callNextTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`request_id`,`employee_id`,`company_id`,`fx_card_id`,`event_type`,`trade_amount`,`card_status`,`call_status`,`call_num`,`call_next_time`,`create_time`,`update_time`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_charging_notice` (`request_id`,`employee_id`,`company_id`,`fx_card_id`,`event_type`,`trade_amount`,`card_status`,`call_status`,`call_num`,`call_next_time`,`create_time`,`update_time`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_charging_notice`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_charging_notice`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_charging_notice`</sql>
    <sql id="from_sql">FROM `fx_card_charging_notice`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_charging_notice`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.requestId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.eventType,jdbcType=TINYINT},
                        #{item.tradeAmount,jdbcType=DECIMAL},
                        #{item.cardStatus,jdbcType=TINYINT},
                        #{item.callStatus,jdbcType=TINYINT},
                        #{item.callNum,jdbcType=TINYINT},
                        #{item.callNextTime,jdbcType=TIMESTAMP},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
