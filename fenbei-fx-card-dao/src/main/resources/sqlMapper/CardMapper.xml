<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.card.CardDAO">
    <resultMap id="CardResultMap" type="com.fenbei.fx.card.dao.card.po.CardPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_id" property="bankCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="bank_mch_id" property="bankMchId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="company_account_id" property="companyAccountId" jdbcType="VARCHAR"/>
        <result column="card_issue_to" property="cardIssueTo" jdbcType="TINYINT"/>
        <result column="card_form_factor" property="cardFormFactor" jdbcType="TINYINT"/>
        <result column="card_cvv" property="cardCvv" jdbcType="VARCHAR"/>
        <result column="card_pin" property="cardPin" jdbcType="VARCHAR"/>
        <result column="card_expiry_year" property="cardExpiryYear" jdbcType="VARCHAR"/>
        <result column="card_expiry_month" property="cardExpiryMonth" jdbcType="VARCHAR"/>
        <result column="name_on_card" property="nameOnCard" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="card_brand" property="cardBrand" jdbcType="VARCHAR"/>
        <result column="card_public_time" property="cardPublicTime" jdbcType="TIMESTAMP"/>
        <result column="card_status" property="cardStatus" jdbcType="TINYINT"/>
        <result column="active_status" property="activeStatus" jdbcType="TINYINT"/>
        <result column="fx_cardholder_id" property="fxCardholderId" jdbcType="VARCHAR"/>
        <result column="card_purpose" property="cardPurpose" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="card_limits" property="cardLimits" jdbcType="LONGVARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="freezen_balance" property="freezenBalance" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`bank_mch_id`,`employee_id`,`company_account_id`,`card_issue_to`,`card_form_factor`,`card_cvv`,`card_pin`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_public_time`,`card_status`,`active_status`,`fx_cardholder_id`,`card_purpose`,`currency`,`balance`,`card_limits`,`create_user_id`,`company_id`,`create_time`,`update_time`,`delete_flag`,`freezen_balance`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card` (`id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`bank_mch_id`,`employee_id`,`company_account_id`,`card_issue_to`,`card_form_factor`,`card_cvv`,`card_pin`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_public_time`,`card_status`,`active_status`,`fx_cardholder_id`,`card_purpose`,`currency`,`balance`,`card_limits`,`create_user_id`,`company_id`,`create_time`,`update_time`,`delete_flag`,`freezen_balance`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card`</sql>
    <sql id="update_table_sql">UPDATE `fx_card`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card`</sql>
    <sql id="from_sql">FROM `fx_card`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.id,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.bankCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.bankMchId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.companyAccountId,jdbcType=VARCHAR},
                        #{item.cardIssueTo,jdbcType=TINYINT},
                        #{item.cardFormFactor,jdbcType=TINYINT},
                        #{item.cardCvv,jdbcType=VARCHAR},
                        #{item.cardPin,jdbcType=VARCHAR},
                        #{item.cardExpiryYear,jdbcType=VARCHAR},
                        #{item.cardExpiryMonth,jdbcType=VARCHAR},
                        #{item.nameOnCard,jdbcType=VARCHAR},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.cardBrand,jdbcType=VARCHAR},
                        #{item.cardPublicTime,jdbcType=TIMESTAMP},
                        #{item.cardStatus,jdbcType=TINYINT},
                        #{item.activeStatus,jdbcType=TINYINT},
                        #{item.fxCardholderId,jdbcType=VARCHAR},
                        #{item.cardPurpose,jdbcType=VARCHAR},
                        #{item.currency,jdbcType=VARCHAR},
                        #{item.balance,jdbcType=DECIMAL},
                        #{item.cardLimits,jdbcType=LONGVARCHAR},
                        #{item.createUserId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                        #{item.freezenBalance,jdbcType=DECIMAL},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>

    <select id="getCardSumBalance" resultType="java.util.Map">
        select sum(balance) sumBalance, currency from `fx_card`
        <where>
            company_id = #{companyId}
            group by currency
        </where>
    </select>
</mapper>

