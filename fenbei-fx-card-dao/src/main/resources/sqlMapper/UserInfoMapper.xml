<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.userinfo.UserInfoDAO">
    <resultMap id="UserInfoResultMap" type="com.fenbei.fx.card.dao.userinfo.po.UserInfoPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="user_phone" property="userPhone" jdbcType="VARCHAR"/>
        <result column="user_email" property="userEmail" jdbcType="VARCHAR"/>
        <result column="id_card_no" property="idCardNo" jdbcType="VARCHAR"/>
        <result column="employee_location" property="employeeLocation" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="company_account_id" property="companyAccountId" jdbcType="VARCHAR"/>
        <result column="certificate_num" property="certificateNum" jdbcType="VARCHAR"/>
        <result column="office_address" property="officeAddress" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="card_brand" property="cardBrand" jdbcType="VARCHAR"/>
        <result column="bank_card_id" property="bankCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="card_public_time" property="cardPublicTime" jdbcType="TIMESTAMP"/>
        <result column="card_purpose" property="cardPurpose" jdbcType="VARCHAR"/>
        <result column="card_cvv" property="cardCvv" jdbcType="VARCHAR"/>
        <result column="name_on_card" property="nameOnCard" jdbcType="VARCHAR"/>
        <result column="card_expiry_month" property="cardExpiryMonth" jdbcType="VARCHAR"/>
        <result column="card_expiry_year" property="cardExpiryYear" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_at" property="createAt" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="update_at" property="updateAt" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_name" property="updateName" jdbcType="VARCHAR"/>
        <result column="is_del" property="isDel" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`user_id`,`user_name`,`user_phone`,`user_email`,`id_card_no`,`employee_location`,`company_id`,`company_name`,`company_account_id`,`certificate_num`,`office_address`,`fx_card_id`,`card_platform`,`card_brand`,`bank_card_id`,`bank_card_no`,`card_public_time`,`card_purpose`,`card_cvv`,`name_on_card`,`card_expiry_month`,`card_expiry_year`,`remark`,`create_at`,`create_time`,`create_by`,`create_name`,`update_at`,`update_time`,`update_by`,`update_name`,`is_del`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_user_info` (`id`,`user_id`,`user_name`,`user_phone`,`user_email`,`id_card_no`,`employee_location`,`company_id`,`company_name`,`company_account_id`,`certificate_num`,`office_address`,`fx_card_id`,`card_platform`,`card_brand`,`bank_card_id`,`bank_card_no`,`card_public_time`,`card_purpose`,`card_cvv`,`name_on_card`,`card_expiry_month`,`card_expiry_year`,`remark`,`create_at`,`create_time`,`create_by`,`create_name`,`update_at`,`update_time`,`update_by`,`update_name`,`is_del`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_user_info`</sql>
    <sql id="update_table_sql">UPDATE `fx_user_info`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_user_info`</sql>
    <sql id="from_sql">FROM `fx_user_info`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_user_info`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.id,jdbcType=VARCHAR},
                        #{item.userId,jdbcType=VARCHAR},
                        #{item.userName,jdbcType=VARCHAR},
                        #{item.userPhone,jdbcType=VARCHAR},
                        #{item.userEmail,jdbcType=VARCHAR},
                        #{item.idCardNo,jdbcType=VARCHAR},
                        #{item.employeeLocation,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.companyName,jdbcType=VARCHAR},
                        #{item.companyAccountId,jdbcType=VARCHAR},
                        #{item.certificateNum,jdbcType=VARCHAR},
                        #{item.officeAddress,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.cardBrand,jdbcType=VARCHAR},
                        #{item.bankCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.cardPublicTime,jdbcType=TIMESTAMP},
                        #{item.cardPurpose,jdbcType=VARCHAR},
                        #{item.cardCvv,jdbcType=VARCHAR},
                        #{item.nameOnCard,jdbcType=VARCHAR},
                        #{item.cardExpiryMonth,jdbcType=VARCHAR},
                        #{item.cardExpiryYear,jdbcType=VARCHAR},
                        #{item.remark,jdbcType=VARCHAR},
                        #{item.createAt,jdbcType=BIGINT},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.createBy,jdbcType=VARCHAR},
                        #{item.createName,jdbcType=VARCHAR},
                        #{item.updateAt,jdbcType=BIGINT},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.updateBy,jdbcType=VARCHAR},
                        #{item.updateName,jdbcType=VARCHAR},
                        #{item.isDel,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
