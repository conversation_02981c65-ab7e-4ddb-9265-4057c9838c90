<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardcreditapplyorder.CardCreditApplyOrderDAO">
    <resultMap id="CardCreditApplyOrderResultMap" type="com.fenbei.fx.card.dao.cardcreditapplyorder.po.CardCreditApplyOrderPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="CHAR"/>
        <result column="apply_batch_no" property="applyBatchNo" jdbcType="VARCHAR"/>
        <result column="apply_order_id" property="applyOrderId" jdbcType="VARCHAR"/>
        <result column="apply_id" property="applyId" jdbcType="VARCHAR"/>
        <result column="meaning_no" property="meaningNo" jdbcType="VARCHAR"/>
        <result column="apply_order_type" property="applyOrderType" jdbcType="INTEGER"/>
        <result column="apply_order_sub_type" property="applyOrderSubType" jdbcType="INTEGER"/>
        <result column="active_model" property="activeModel" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="apply_amount" property="applyAmount" jdbcType="DECIMAL"/>
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR"/>
        <result column="apply_reason_id" property="applyReasonId" jdbcType="INTEGER"/>
        <result column="apply_state" property="applyState" jdbcType="INTEGER"/>
        <result column="apply_result_desc" property="applyResultDesc" jdbcType="LONGVARCHAR"/>
        <result column="deduction_mode" property="deductionMode" jdbcType="INTEGER"/>
        <result column="creater_id" property="createrId" jdbcType="VARCHAR"/>
        <result column="creater_name" property="createrName" jdbcType="VARCHAR"/>
        <result column="applicant_id" property="applicantId" jdbcType="VARCHAR"/>
        <result column="applicant_name" property="applicantName" jdbcType="VARCHAR"/>
        <result column="applicant_org_id" property="applicantOrgId" jdbcType="VARCHAR"/>
        <result column="applicant_org_name" property="applicantOrgName" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="issued_id" property="issuedId" jdbcType="VARCHAR"/>
        <result column="issued_name" property="issuedName" jdbcType="VARCHAR"/>
        <result column="issued_time" property="issuedTime" jdbcType="TIMESTAMP"/>
        <result column="cost_category_id" property="costCategoryId" jdbcType="VARCHAR"/>
        <result column="cost_category_name" property="costCategoryName" jdbcType="VARCHAR"/>
        <result column="cost_attribution_opt" property="costAttributionOpt" jdbcType="INTEGER"/>
        <result column="cost_attributions" property="costAttributions" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="employee_dept" property="employeeDept" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`company_id`,`apply_batch_no`,`apply_order_id`,`apply_id`,`meaning_no`,`apply_order_type`,`apply_order_sub_type`,`active_model`,`title`,`currency`,`apply_amount`,`apply_reason`,`apply_reason_id`,`apply_state`,`apply_result_desc`,`deduction_mode`,`creater_id`,`creater_name`,`applicant_id`,`applicant_name`,`applicant_org_id`,`applicant_org_name`,`fx_card_id`,`bank_card_no`,`bank_name`,`issued_id`,`issued_name`,`issued_time`,`cost_category_id`,`cost_category_name`,`cost_attribution_opt`,`cost_attributions`,`create_time`,`update_time`,`employee_dept`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_credit_apply_order` (`company_id`,`apply_batch_no`,`apply_order_id`,`apply_id`,`meaning_no`,`apply_order_type`,`apply_order_sub_type`,`active_model`,`title`,`currency`,`apply_amount`,`apply_reason`,`apply_reason_id`,`apply_state`,`apply_result_desc`,`deduction_mode`,`creater_id`,`creater_name`,`applicant_id`,`applicant_name`,`applicant_org_id`,`applicant_org_name`,`fx_card_id`,`bank_card_no`,`bank_name`,`issued_id`,`issued_name`,`issued_time`,`cost_category_id`,`cost_category_name`,`cost_attribution_opt`,`cost_attributions`,`create_time`,`update_time`,`employee_dept`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_credit_apply_order`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_credit_apply_order`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_credit_apply_order`</sql>
    <sql id="from_sql">FROM `fx_card_credit_apply_order`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_credit_apply_order`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.companyId,jdbcType=CHAR},
                        #{item.applyBatchNo,jdbcType=VARCHAR},
                        #{item.applyOrderId,jdbcType=VARCHAR},
                        #{item.applyId,jdbcType=VARCHAR},
                        #{item.meaningNo,jdbcType=VARCHAR},
                        #{item.applyOrderType,jdbcType=INTEGER},
                        #{item.applyOrderSubType,jdbcType=INTEGER},
                        #{item.activeModel,jdbcType=INTEGER},
                        #{item.title,jdbcType=VARCHAR},
                        #{item.currency,jdbcType=VARCHAR},
                        #{item.applyAmount,jdbcType=DECIMAL},
                        #{item.applyReason,jdbcType=VARCHAR},
                        #{item.applyReasonId,jdbcType=INTEGER},
                        #{item.applyState,jdbcType=INTEGER},
                        #{item.applyResultDesc,jdbcType=LONGVARCHAR},
                        #{item.deductionMode,jdbcType=INTEGER},
                        #{item.createrId,jdbcType=VARCHAR},
                        #{item.createrName,jdbcType=VARCHAR},
                        #{item.applicantId,jdbcType=VARCHAR},
                        #{item.applicantName,jdbcType=VARCHAR},
                        #{item.applicantOrgId,jdbcType=VARCHAR},
                        #{item.applicantOrgName,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.bankName,jdbcType=VARCHAR},
                        #{item.issuedId,jdbcType=VARCHAR},
                        #{item.issuedName,jdbcType=VARCHAR},
                        #{item.issuedTime,jdbcType=TIMESTAMP},
                        #{item.costCategoryId,jdbcType=VARCHAR},
                        #{item.costCategoryName,jdbcType=VARCHAR},
                        #{item.costAttributionOpt,jdbcType=INTEGER},
                        #{item.costAttributions,jdbcType=LONGVARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.employeeDept,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
