<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardapply.CardApplyDAO">
    <resultMap id="CardApplyResultMap" type="com.fenbei.fx.card.dao.cardapply.po.CardApplyPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="apply_id" property="applyId" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_id" property="bankCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="bank_mch_id" property="bankMchId" jdbcType="VARCHAR"/>
        <result column="company_account_id" property="companyAccountId" jdbcType="VARCHAR"/>
        <result column="apply_type" property="applyType" jdbcType="TINYINT"/>
        <result column="apply_status" property="applyStatus" jdbcType="TINYINT"/>
        <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
        <result column="card_issue_to" property="cardIssueTo" jdbcType="TINYINT"/>
        <result column="card_form_factor" property="cardFormFactor" jdbcType="TINYINT"/>
        <result column="card_cvv" property="cardCvv" jdbcType="VARCHAR"/>
        <result column="card_expiry_year" property="cardExpiryYear" jdbcType="VARCHAR"/>
        <result column="card_expiry_month" property="cardExpiryMonth" jdbcType="VARCHAR"/>
        <result column="name_on_card" property="nameOnCard" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="card_brand" property="cardBrand" jdbcType="VARCHAR"/>
        <result column="card_public_time" property="cardPublicTime" jdbcType="TIMESTAMP"/>
        <result column="fx_cardholder_id" property="fxCardholderId" jdbcType="VARCHAR"/>
        <result column="nation_code" property="nationCode" jdbcType="VARCHAR"/>
        <result column="applyer_phone" property="applyerPhone" jdbcType="VARCHAR"/>
        <result column="applyer_email" property="applyerEmail" jdbcType="VARCHAR"/>
        <result column="applyer_first_name" property="applyerFirstName" jdbcType="VARCHAR"/>
        <result column="applyer_last_name" property="applyerLastName" jdbcType="VARCHAR"/>
        <result column="card_purpose" property="cardPurpose" jdbcType="VARCHAR"/>
        <result column="approve_user_id" property="approveUserId" jdbcType="VARCHAR"/>
        <result column="approve_user_name" property="approveUserName" jdbcType="VARCHAR"/>
        <result column="approve_time" property="approveTime" jdbcType="TIMESTAMP"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="card_limits" property="cardLimits" jdbcType="LONGVARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="postal_address" property="postalAddress" jdbcType="LONGVARCHAR"/>
        <result column="out_batch_no" property="outBatchNo" jdbcType="VARCHAR"/>
        <result column="logistics_company" property="logisticsCompany" jdbcType="VARCHAR"/>
        <result column="logistics_no" property="logisticsNo" jdbcType="VARCHAR"/>
        <result column="apply_order_status" property="applyOrderStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`apply_id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`bank_mch_id`,`company_account_id`,`apply_type`,`apply_status`,`refuse_reason`,`card_issue_to`,`card_form_factor`,`card_cvv`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_public_time`,`fx_cardholder_id`,`nation_code`,`applyer_phone`,`applyer_email`,`applyer_first_name`,`applyer_last_name`,`card_purpose`,`approve_user_id`,`approve_user_name`,`approve_time`,`currency`,`card_limits`,`create_user_id`,`company_id`,`create_time`,`update_time`,`delete_flag`,`postal_address`,`out_batch_no`,`logistics_company`,`logistics_no`,`apply_order_status`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_apply` (`id`,`apply_id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`bank_mch_id`,`company_account_id`,`apply_type`,`apply_status`,`refuse_reason`,`card_issue_to`,`card_form_factor`,`card_cvv`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_public_time`,`fx_cardholder_id`,`nation_code`,`applyer_phone`,`applyer_email`,`applyer_first_name`,`applyer_last_name`,`card_purpose`,`approve_user_id`,`approve_user_name`,`approve_time`,`currency`,`card_limits`,`create_user_id`,`company_id`,`create_time`,`update_time`,`delete_flag`,`postal_address`,`out_batch_no`,`logistics_company`,`logistics_no`,`apply_order_status`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_apply`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_apply`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_apply`</sql>
    <sql id="from_sql">FROM `fx_card_apply`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_apply`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.id,jdbcType=VARCHAR},
                        #{item.applyId,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.bankCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.bankMchId,jdbcType=VARCHAR},
                        #{item.companyAccountId,jdbcType=VARCHAR},
                        #{item.applyType,jdbcType=TINYINT},
                        #{item.applyStatus,jdbcType=TINYINT},
                        #{item.refuseReason,jdbcType=VARCHAR},
                        #{item.cardIssueTo,jdbcType=TINYINT},
                        #{item.cardFormFactor,jdbcType=TINYINT},
                        #{item.cardCvv,jdbcType=VARCHAR},
                        #{item.cardExpiryYear,jdbcType=VARCHAR},
                        #{item.cardExpiryMonth,jdbcType=VARCHAR},
                        #{item.nameOnCard,jdbcType=VARCHAR},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.cardBrand,jdbcType=VARCHAR},
                        #{item.cardPublicTime,jdbcType=TIMESTAMP},
                        #{item.fxCardholderId,jdbcType=VARCHAR},
                        #{item.nationCode,jdbcType=VARCHAR},
                        #{item.applyerPhone,jdbcType=VARCHAR},
                        #{item.applyerEmail,jdbcType=VARCHAR},
                        #{item.applyerFirstName,jdbcType=VARCHAR},
                        #{item.applyerLastName,jdbcType=VARCHAR},
                        #{item.cardPurpose,jdbcType=VARCHAR},
                        #{item.approveUserId,jdbcType=VARCHAR},
                        #{item.approveUserName,jdbcType=VARCHAR},
                        #{item.approveTime,jdbcType=TIMESTAMP},
                        #{item.currency,jdbcType=VARCHAR},
                        #{item.cardLimits,jdbcType=LONGVARCHAR},
                        #{item.createUserId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                        #{item.postalAddress,jdbcType=LONGVARCHAR},
                        #{item.outBatchNo,jdbcType=VARCHAR},
                        #{item.logisticsCompany,jdbcType=VARCHAR},
                        #{item.logisticsNo,jdbcType=VARCHAR},
                        #{item.applyOrderStatus,jdbcType=VARCHAR},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
