<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardauthorize.CardAuthorizeDAO">
    <resultMap id="CardAuthorizeResultMap" type="com.fenbei.fx.card.dao.cardauthorize.po.CardAuthorizePO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="trade_currency" property="tradeCurrency" jdbcType="VARCHAR"/>
        <result column="trade_amount" property="tradeAmount" jdbcType="DECIMAL"/>
        <result column="trade_name" property="tradeName" jdbcType="VARCHAR"/>
        <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>
        <result column="trade_address" property="tradeAddress" jdbcType="VARCHAR"/>
        <result column="source_data" property="sourceData" jdbcType="LONGVARCHAR"/>
        <result column="auth_status" property="authStatus" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="trade_id" property="tradeId" jdbcType="VARCHAR"/>
        <result column="trade_type" property="tradeType" jdbcType="VARCHAR"/>
        <result column="sub_trade_id" property="subTradeId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`card_platform`,`trade_currency`,`trade_amount`,`trade_name`,`trade_time`,`trade_address`,`source_data`,`auth_status`,`create_time`,`update_time`,`trade_id`,`trade_type`,`sub_trade_id`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_authorize` (`card_platform`,`trade_currency`,`trade_amount`,`trade_name`,`trade_time`,`trade_address`,`source_data`,`auth_status`,`create_time`,`update_time`,`trade_id`,`trade_type`,`sub_trade_id`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_authorize`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_authorize`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_authorize`</sql>
    <sql id="from_sql">FROM `fx_card_authorize`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_authorize`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.tradeCurrency,jdbcType=VARCHAR},
                        #{item.tradeAmount,jdbcType=DECIMAL},
                        #{item.tradeName,jdbcType=VARCHAR},
                        #{item.tradeTime,jdbcType=TIMESTAMP},
                        #{item.tradeAddress,jdbcType=VARCHAR},
                        #{item.sourceData,jdbcType=LONGVARCHAR},
                        #{item.authStatus,jdbcType=TINYINT},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.tradeId,jdbcType=VARCHAR},
                        #{item.tradeType,jdbcType=VARCHAR},
                        #{item.subTradeId,jdbcType=VARCHAR},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
