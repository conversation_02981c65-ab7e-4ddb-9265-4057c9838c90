<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardcreditmanager.CardCreditManagerDAO">
    <resultMap id="CardCreditManagerResultMap" type="com.fenbei.fx.card.dao.cardcreditmanager.po.CardCreditManagerPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_id" property="bankCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="company_account_id" property="companyAccountId" jdbcType="VARCHAR"/>
        <result column="apply_type" property="applyType" jdbcType="TINYINT"/>
        <result column="apply_status" property="applyStatus" jdbcType="TINYINT"/>
        <result column="apply_trans_no" property="applyTransNo" jdbcType="VARCHAR"/>
        <result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
        <result column="apply_trans_batch_no" property="applyTransBatchNo" jdbcType="VARCHAR"/>
        <result column="ori_apply_trans_no" property="oriApplyTransNo" jdbcType="VARCHAR"/>
        <result column="apply_meaning_no" property="applyMeaningNo" jdbcType="VARCHAR"/>
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR"/>
        <result column="apply_title" property="applyTitle" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="writen_off_amount" property="writenOffAmount" jdbcType="DECIMAL"/>
        <result column="unchecked_amount" property="uncheckedAmount" jdbcType="DECIMAL"/>
        <result column="writing_off_amount" property="writingOffAmount" jdbcType="DECIMAL"/>
        <result column="unwrite_off_amount" property="unwriteOffAmount" jdbcType="DECIMAL"/>
        <result column="avalible_amount" property="avalibleAmount" jdbcType="DECIMAL"/>
        <result column="returned_amount" property="returnedAmount" jdbcType="DECIMAL"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="operation_type" property="operationType" jdbcType="TINYINT"/>
        <result column="operation_user_id" property="operationUserId" jdbcType="VARCHAR"/>
        <result column="operation_user_name" property="operationUserName" jdbcType="VARCHAR"/>
        <result column="operation_user_dept" property="operationUserDept" jdbcType="VARCHAR"/>
        <result column="card_model" property="cardModel" jdbcType="TINYINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="cost_type" property="costType" jdbcType="VARCHAR"/>
        <result column="cost_attribution" property="costAttribution" jdbcType="VARCHAR"/>
        <result column="usd_cny_exchange_rate" property="usdCnyExchangeRate" jdbcType="DECIMAL"/>
        <result column="cny_amount" property="cnyAmount" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="apply_reason_desc" property="applyReasonDesc" jdbcType="VARCHAR"/>
        <result column="cost_type_name" property="costTypeName" jdbcType="VARCHAR"/>
        <result column="apply_order_type" property="applyOrderType" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`company_id`,`employee_id`,`company_account_id`,`apply_type`,`apply_status`,`apply_trans_no`,`biz_no`,`apply_trans_batch_no`,`ori_apply_trans_no`,`apply_meaning_no`,`apply_reason`,`apply_title`,`currency`,`amount`,`writen_off_amount`,`unchecked_amount`,`writing_off_amount`,`unwrite_off_amount`,`avalible_amount`,`returned_amount`,`card_platform`,`operation_type`,`operation_user_id`,`operation_user_name`,`operation_user_dept`,`card_model`,`update_time`,`delete_flag`,`cost_type`,`cost_attribution`,`usd_cny_exchange_rate`,`cny_amount`,`create_time`,`apply_reason_desc`,`cost_type_name`,`apply_order_type`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_credit_manager` (`id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`company_id`,`employee_id`,`company_account_id`,`apply_type`,`apply_status`,`apply_trans_no`,`biz_no`,`apply_trans_batch_no`,`ori_apply_trans_no`,`apply_meaning_no`,`apply_reason`,`apply_title`,`currency`,`amount`,`writen_off_amount`,`unchecked_amount`,`writing_off_amount`,`unwrite_off_amount`,`avalible_amount`,`returned_amount`,`card_platform`,`operation_type`,`operation_user_id`,`operation_user_name`,`operation_user_dept`,`card_model`,`update_time`,`delete_flag`,`cost_type`,`cost_attribution`,`usd_cny_exchange_rate`,`cny_amount`,`create_time`,`apply_reason_desc`,`cost_type_name`,`apply_order_type`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_credit_manager`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_credit_manager`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_credit_manager`</sql>
    <sql id="from_sql">FROM `fx_card_credit_manager`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_credit_manager`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.id,jdbcType=BIGINT},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.bankCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.companyAccountId,jdbcType=VARCHAR},
                        #{item.applyType,jdbcType=TINYINT},
                        #{item.applyStatus,jdbcType=TINYINT},
                        #{item.applyTransNo,jdbcType=VARCHAR},
                        #{item.bizNo,jdbcType=VARCHAR},
                        #{item.applyTransBatchNo,jdbcType=VARCHAR},
                        #{item.oriApplyTransNo,jdbcType=VARCHAR},
                        #{item.applyMeaningNo,jdbcType=VARCHAR},
                        #{item.applyReason,jdbcType=VARCHAR},
                        #{item.applyTitle,jdbcType=VARCHAR},
                        #{item.currency,jdbcType=VARCHAR},
                        #{item.amount,jdbcType=DECIMAL},
                        #{item.writenOffAmount,jdbcType=DECIMAL},
                        #{item.uncheckedAmount,jdbcType=DECIMAL},
                        #{item.writingOffAmount,jdbcType=DECIMAL},
                        #{item.unwriteOffAmount,jdbcType=DECIMAL},
                        #{item.avalibleAmount,jdbcType=DECIMAL},
                        #{item.returnedAmount,jdbcType=DECIMAL},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.operationType,jdbcType=TINYINT},
                        #{item.operationUserId,jdbcType=VARCHAR},
                        #{item.operationUserName,jdbcType=VARCHAR},
                        #{item.operationUserDept,jdbcType=VARCHAR},
                        #{item.cardModel,jdbcType=TINYINT},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                        #{item.costType,jdbcType=VARCHAR},
                        #{item.costAttribution,jdbcType=VARCHAR},
                        #{item.usdCnyExchangeRate,jdbcType=DECIMAL},
                        #{item.cnyAmount,jdbcType=DECIMAL},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.applyReasonDesc,jdbcType=VARCHAR},
                        #{item.costTypeName,jdbcType=VARCHAR},
                        #{item.applyOrderType,jdbcType=INTEGER},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
