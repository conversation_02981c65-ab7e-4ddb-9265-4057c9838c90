<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardholderoperflow.CardholderOperFlowDAO">
    <resultMap id="CardholderOperFlowResultMap" type="com.fenbei.fx.card.dao.cardholderoperflow.po.CardholderOperFlowPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="oper_flow_id" property="operFlowId" jdbcType="VARCHAR"/>
        <result column="apply_id" property="applyId" jdbcType="VARCHAR"/>
        <result column="fx_cardholder_id" property="fxCardholderId" jdbcType="VARCHAR"/>
        <result column="bank_cardholder_id" property="bankCardholderId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="operator_id" property="operatorId" jdbcType="VARCHAR"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
        <result column="operate_type" property="operateType" jdbcType="TINYINT"/>
        <result column="operate_desc" property="operateDesc" jdbcType="VARCHAR"/>
        <result column="pre_snapshot" property="preSnapshot" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`oper_flow_id`,`apply_id`,`fx_cardholder_id`,`bank_cardholder_id`,`company_id`,`operator_id`,`operator_name`,`operate_type`,`operate_desc`,`pre_snapshot`,`create_time`,`update_time`,`delete_flag`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_cardholder_oper_flow` (`oper_flow_id`,`apply_id`,`fx_cardholder_id`,`bank_cardholder_id`,`company_id`,`operator_id`,`operator_name`,`operate_type`,`operate_desc`,`pre_snapshot`,`create_time`,`update_time`,`delete_flag`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_cardholder_oper_flow`</sql>
    <sql id="update_table_sql">UPDATE `fx_cardholder_oper_flow`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_cardholder_oper_flow`</sql>
    <sql id="from_sql">FROM `fx_cardholder_oper_flow`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_cardholder_oper_flow`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.operFlowId,jdbcType=VARCHAR},
                        #{item.applyId,jdbcType=VARCHAR},
                        #{item.fxCardholderId,jdbcType=VARCHAR},
                        #{item.bankCardholderId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.operatorId,jdbcType=VARCHAR},
                        #{item.operatorName,jdbcType=VARCHAR},
                        #{item.operateType,jdbcType=TINYINT},
                        #{item.operateDesc,jdbcType=VARCHAR},
                        #{item.preSnapshot,jdbcType=LONGVARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
