<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardholderapply.CardholderApplyDAO">
    <resultMap id="CardholderApplyResultMap" type="com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="apply_id" property="applyId" jdbcType="VARCHAR"/>
        <result column="fx_cardholder_id" property="fxCardholderId" jdbcType="VARCHAR"/>
        <result column="bank_cardholder_id" property="bankCardholderId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="apply_type" property="applyType" jdbcType="TINYINT"/>
        <result column="apply_status" property="applyStatus" jdbcType="TINYINT"/>
        <result column="refuse_reason" property="refuseReason" jdbcType="VARCHAR"/>
        <result column="first_name" property="firstName" jdbcType="VARCHAR"/>
        <result column="last_name" property="lastName" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="birth" property="birth" jdbcType="DATE"/>
        <result column="nation_code" property="nationCode" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="identification_country" property="identificationCountry" jdbcType="VARCHAR"/>
        <result column="identification_type" property="identificationType" jdbcType="TINYINT"/>
        <result column="identification_number" property="identificationNumber" jdbcType="VARCHAR"/>
        <result column="identification_expiry_date" property="identificationExpiryDate" jdbcType="DATE"/>
        <result column="identification_expiry_type" property="identificationExpiryType" jdbcType="TINYINT"/>
        <result column="address" property="address" jdbcType="LONGVARCHAR"/>
        <result column="postal_address" property="postalAddress" jdbcType="LONGVARCHAR"/>
        <result column="approve_user_id" property="approveUserId" jdbcType="VARCHAR"/>
        <result column="approve_user_name" property="approveUserName" jdbcType="VARCHAR"/>
        <result column="approve_time" property="approveTime" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`apply_id`,`fx_cardholder_id`,`bank_cardholder_id`,`employee_id`,`employee_name`,`company_id`,`card_platform`,`apply_type`,`apply_status`,`refuse_reason`,`first_name`,`last_name`,`name`,`email`,`birth`,`nation_code`,`phone`,`identification_country`,`identification_type`,`identification_number`,`identification_expiry_date`,`identification_expiry_type`,`address`,`postal_address`,`approve_user_id`,`approve_user_name`,`approve_time`,`create_user_id`,`create_time`,`update_time`,`delete_flag`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_cardholder_apply` (`id`,`apply_id`,`fx_cardholder_id`,`bank_cardholder_id`,`employee_id`,`employee_name`,`company_id`,`card_platform`,`apply_type`,`apply_status`,`refuse_reason`,`first_name`,`last_name`,`name`,`email`,`birth`,`nation_code`,`phone`,`identification_country`,`identification_type`,`identification_number`,`identification_expiry_date`,`identification_expiry_type`,`address`,`postal_address`,`approve_user_id`,`approve_user_name`,`approve_time`,`create_user_id`,`create_time`,`update_time`,`delete_flag`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_cardholder_apply`</sql>
    <sql id="update_table_sql">UPDATE `fx_cardholder_apply`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_cardholder_apply`</sql>
    <sql id="from_sql">FROM `fx_cardholder_apply`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_cardholder_apply`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.id,jdbcType=VARCHAR},
                        #{item.applyId,jdbcType=VARCHAR},
                        #{item.fxCardholderId,jdbcType=VARCHAR},
                        #{item.bankCardholderId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.employeeName,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.applyType,jdbcType=TINYINT},
                        #{item.applyStatus,jdbcType=TINYINT},
                        #{item.refuseReason,jdbcType=VARCHAR},
                        #{item.firstName,jdbcType=VARCHAR},
                        #{item.lastName,jdbcType=VARCHAR},
                        #{item.name,jdbcType=VARCHAR},
                        #{item.email,jdbcType=VARCHAR},
                        #{item.birth,jdbcType=DATE},
                        #{item.nationCode,jdbcType=VARCHAR},
                        #{item.phone,jdbcType=VARCHAR},
                        #{item.identificationCountry,jdbcType=VARCHAR},
                        #{item.identificationType,jdbcType=TINYINT},
                        #{item.identificationNumber,jdbcType=VARCHAR},
                        #{item.identificationExpiryDate,jdbcType=DATE},
                        #{item.identificationExpiryType,jdbcType=TINYINT},
                        #{item.address,jdbcType=LONGVARCHAR},
                        #{item.postalAddress,jdbcType=LONGVARCHAR},
                        #{item.approveUserId,jdbcType=VARCHAR},
                        #{item.approveUserName,jdbcType=VARCHAR},
                        #{item.approveTime,jdbcType=TIMESTAMP},
                        #{item.createUserId,jdbcType=VARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
