<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardorder.CardOrderDAO">
    <resultMap id="CardOrderResultMap" type="com.fenbei.fx.card.dao.cardorder.po.CardOrderPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="name_on_card" property="nameOnCard" jdbcType="VARCHAR"/>
        <result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
        <result column="ori_biz_no" property="oriBizNo" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="trade_currency" property="tradeCurrency" jdbcType="VARCHAR"/>
        <result column="trade_amount" property="tradeAmount" jdbcType="DECIMAL"/>
        <result column="trade_name" property="tradeName" jdbcType="VARCHAR"/>
        <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>
        <result column="trade_address" property="tradeAddress" jdbcType="VARCHAR"/>
        <result column="trade_remark" property="tradeRemark" jdbcType="VARCHAR"/>
        <result column="check_status" property="checkStatus" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="bill_trade_currency" property="billTradeCurrency" jdbcType="VARCHAR"/>
        <result column="bill_trade_amount" property="billTradeAmount" jdbcType="DECIMAL"/>
        <result column="trade_id" property="tradeId" jdbcType="VARCHAR"/>
        <result column="sub_trade_id" property="subTradeId" jdbcType="VARCHAR"/>
        <result column="checked_amount" property="checkedAmount" jdbcType="DECIMAL"/>
        <result column="unchecked_amount" property="uncheckedAmount" jdbcType="DECIMAL"/>
        <result column="checking_amount" property="checkingAmount" jdbcType="DECIMAL"/>
        <result column="need_not_check_amount" property="needNotCheckAmount" jdbcType="DECIMAL"/>
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="apply_bind" property="applyBind" jdbcType="INTEGER"/>
        <result column="cost_id" property="costId" jdbcType="INTEGER"/>
        <result column="masked_card_number" property="maskedCardNumber" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="card_form_factor" property="cardFormFactor" jdbcType="TINYINT"/>
        <result column="trade_cny_exchange_rate" property="tradeCnyExchangeRate" jdbcType="DECIMAL"/>
        <result column="cny_trade_amount" property="cnyTradeAmount" jdbcType="DECIMAL"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="order_show" property="orderShow" jdbcType="INTEGER"/>
        <result column="bill_trade_cny_exchange_rate" property="billTradeCnyExchangeRate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`company_id`,`employee_id`,`name_on_card`,`biz_no`,`ori_biz_no`,`type`,`trade_currency`,`trade_amount`,`trade_name`,`trade_time`,`trade_address`,`trade_remark`,`check_status`,`create_time`,`update_time`,`bill_trade_currency`,`bill_trade_amount`,`trade_id`,`sub_trade_id`,`checked_amount`,`unchecked_amount`,`checking_amount`,`refund_amount`,`need_not_check_amount`,`apply_bind`,`cost_id`,`masked_card_number`,`bank_card_no`,`card_form_factor`,`trade_cny_exchange_rate`,`cny_trade_amount`,`card_platform`,`order_show`,`bill_trade_cny_exchange_rate`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_order` (`fx_card_id`,`company_id`,`employee_id`,`name_on_card`,`biz_no`,`ori_biz_no`,`type`,`trade_currency`,`trade_amount`,`trade_name`,`trade_time`,`trade_address`,`trade_remark`,`check_status`,`create_time`,`update_time`,`bill_trade_currency`,`bill_trade_amount`,`trade_id`,`sub_trade_id`,`checked_amount`,`unchecked_amount`,`checking_amount`,`refund_amount`,`need_not_check_amount`,`apply_bind`,`cost_id`,`masked_card_number`,`bank_card_no`,`card_form_factor`,`trade_cny_exchange_rate`,`cny_trade_amount`,`card_platform`,`order_show`,`bill_trade_cny_exchange_rate`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_order`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_order`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_order`</sql>
    <sql id="from_sql">FROM `fx_card_order`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_order`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.nameOnCard,jdbcType=VARCHAR},
                        #{item.bizNo,jdbcType=VARCHAR},
                        #{item.oriBizNo,jdbcType=VARCHAR},
                        #{item.type,jdbcType=INTEGER},
                        #{item.tradeCurrency,jdbcType=VARCHAR},
                        #{item.tradeAmount,jdbcType=DECIMAL},
                        #{item.tradeName,jdbcType=VARCHAR},
                        #{item.tradeTime,jdbcType=TIMESTAMP},
                        #{item.tradeAddress,jdbcType=VARCHAR},
                        #{item.tradeRemark,jdbcType=VARCHAR},
                        #{item.checkStatus,jdbcType=TINYINT},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.billTradeCurrency,jdbcType=VARCHAR},
                        #{item.billTradeAmount,jdbcType=DECIMAL},
                        #{item.tradeId,jdbcType=VARCHAR},
                        #{item.subTradeId,jdbcType=VARCHAR},
                        #{item.checkedAmount,jdbcType=DECIMAL},
                        #{item.uncheckedAmount,jdbcType=DECIMAL},
                        #{item.checkingAmount,jdbcType=DECIMAL},
                        #{item.refundAmount,jdbcType=DECIMAL},
                        #{item.needNotCheckAmount,jdbcType=DECIMAL},
                        #{item.applyBind,jdbcType=INTEGER},
                        #{item.costId,jdbcType=INTEGER},
                        #{item.maskedCardNumber,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.cardFormFactor,jdbcType=TINYINT},
                        #{item.tradeCnyExchangeRate,jdbcType=DECIMAL},
                        #{item.cnyTradeAmount,jdbcType=DECIMAL},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.orderShow,jdbcType=INTEGER},
                        #{item.billTradeCnyExchangeRate,jdbcType=DECIMAL},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
