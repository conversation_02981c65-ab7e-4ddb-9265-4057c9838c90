<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.bankcardflow.BankCardFlowDAO">
    <resultMap id="BankCardFlowResultMap" type="com.fenbei.fx.card.dao.bankcardflow.po.BankCardFlowPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="biz_no" property="bizNo" jdbcType="VARCHAR"/>
        <result column="operation_type" property="operationType" jdbcType="INTEGER"/>
        <result column="current_amount" property="currentAmount" jdbcType="DECIMAL"/>
        <result column="operation_amount" property="operationAmount" jdbcType="DECIMAL"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="card_model" property="cardModel" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="current_freezen_amount" property="currentFreezenAmount" jdbcType="DECIMAL"/>
        <result column="operation_freezen_amount" property="operationFreezenAmount" jdbcType="DECIMAL"/>
        <result column="freezen_balance" property="freezenBalance" jdbcType="DECIMAL"/>
        <result column="ori_biz_no" property="oriBizNo" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`company_id`,`employee_id`,`biz_no`,`operation_type`,`current_amount`,`operation_amount`,`balance`,`card_model`,`create_time`,`update_time`,`current_freezen_amount`,`operation_freezen_amount`,`freezen_balance`,`ori_biz_no`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_bank_card_flow` (`fx_card_id`,`company_id`,`employee_id`,`biz_no`,`operation_type`,`current_amount`,`operation_amount`,`balance`,`card_model`,`create_time`,`update_time`,`current_freezen_amount`,`operation_freezen_amount`,`freezen_balance`,`ori_biz_no`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_bank_card_flow`</sql>
    <sql id="update_table_sql">UPDATE `fx_bank_card_flow`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_bank_card_flow`</sql>
    <sql id="from_sql">FROM `fx_bank_card_flow`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_bank_card_flow`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.bizNo,jdbcType=VARCHAR},
                        #{item.operationType,jdbcType=INTEGER},
                        #{item.currentAmount,jdbcType=DECIMAL},
                        #{item.operationAmount,jdbcType=DECIMAL},
                        #{item.balance,jdbcType=DECIMAL},
                        #{item.cardModel,jdbcType=INTEGER},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.currentFreezenAmount,jdbcType=DECIMAL},
                        #{item.operationFreezenAmount,jdbcType=DECIMAL},
                        #{item.freezenBalance,jdbcType=DECIMAL},
                        #{item.oriBizNo,jdbcType=VARCHAR},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
