<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardmail.CardMailDAO">
    <resultMap id="CardMailResultMap" type="com.fenbei.fx.card.dao.cardmail.po.CardMailPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_id" property="bankCardId" jdbcType="VARCHAR"/>
        <result column="bank_card_no" property="bankCardNo" jdbcType="VARCHAR"/>
        <result column="card_mail_no" property="cardMailNo" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="employee_name" property="employeeName" jdbcType="VARCHAR"/>
        <result column="employee_phone" property="employeePhone" jdbcType="VARCHAR"/>
        <result column="company_account_id" property="companyAccountId" jdbcType="VARCHAR"/>
        <result column="card_owner_type" property="cardOwnerType" jdbcType="TINYINT"/>
        <result column="card_form_factor" property="cardFormFactor" jdbcType="TINYINT"/>
        <result column="card_cvv" property="cardCvv" jdbcType="VARCHAR"/>
        <result column="card_expiry_year" property="cardExpiryYear" jdbcType="VARCHAR"/>
        <result column="card_expiry_month" property="cardExpiryMonth" jdbcType="VARCHAR"/>
        <result column="name_on_card" property="nameOnCard" jdbcType="VARCHAR"/>
        <result column="card_platform" property="cardPlatform" jdbcType="VARCHAR"/>
        <result column="card_brand" property="cardBrand" jdbcType="VARCHAR"/>
        <result column="card_issuance_time" property="cardIssuanceTime" jdbcType="TIMESTAMP"/>
        <result column="fx_cardholder_id" property="fxCardholderId" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="fbt_receive_status" property="fbtReceiveStatus" jdbcType="TINYINT"/>
        <result column="forward_status" property="forwardStatus" jdbcType="TINYINT"/>
        <result column="sign_status" property="signStatus" jdbcType="TINYINT"/>
        <result column="forward_flag" property="forwardFlag" jdbcType="TINYINT"/>
        <result column="forward_no" property="forwardNo" jdbcType="VARCHAR"/>
        <result column="forward_supplier" property="forwardSupplier" jdbcType="VARCHAR"/>
        <result column="ax_post_address" property="axPostAddress" jdbcType="VARCHAR"/>
        <result column="original_address" property="originalAddress" jdbcType="VARCHAR"/>
        <result column="changed_address" property="changedAddress" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`bank_card_id`,`bank_card_no`,`card_mail_no`,`employee_id`,`employee_name`,`employee_phone`,`company_account_id`,`card_owner_type`,`card_form_factor`,`card_cvv`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_issuance_time`,`fx_cardholder_id`,`create_user_id`,`company_id`,`fbt_receive_status`,`forward_status`,`sign_status`,`forward_flag`,`forward_no`,`forward_supplier`,`ax_post_address`,`original_address`,`changed_address`,`remark`,`create_time`,`update_time`,`delete_flag`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_mail` (`fx_card_id`,`bank_card_id`,`bank_card_no`,`card_mail_no`,`employee_id`,`employee_name`,`employee_phone`,`company_account_id`,`card_owner_type`,`card_form_factor`,`card_cvv`,`card_expiry_year`,`card_expiry_month`,`name_on_card`,`card_platform`,`card_brand`,`card_issuance_time`,`fx_cardholder_id`,`create_user_id`,`company_id`,`fbt_receive_status`,`forward_status`,`sign_status`,`forward_flag`,`forward_no`,`forward_supplier`,`ax_post_address`,`original_address`,`changed_address`,`remark`,`create_time`,`update_time`,`delete_flag`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_mail`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_mail`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_mail`</sql>
    <sql id="from_sql">FROM `fx_card_mail`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_mail`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.bankCardId,jdbcType=VARCHAR},
                        #{item.bankCardNo,jdbcType=VARCHAR},
                        #{item.cardMailNo,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.employeeName,jdbcType=VARCHAR},
                        #{item.employeePhone,jdbcType=VARCHAR},
                        #{item.companyAccountId,jdbcType=VARCHAR},
                        #{item.cardOwnerType,jdbcType=TINYINT},
                        #{item.cardFormFactor,jdbcType=TINYINT},
                        #{item.cardCvv,jdbcType=VARCHAR},
                        #{item.cardExpiryYear,jdbcType=VARCHAR},
                        #{item.cardExpiryMonth,jdbcType=VARCHAR},
                        #{item.nameOnCard,jdbcType=VARCHAR},
                        #{item.cardPlatform,jdbcType=VARCHAR},
                        #{item.cardBrand,jdbcType=VARCHAR},
                        #{item.cardIssuanceTime,jdbcType=TIMESTAMP},
                        #{item.fxCardholderId,jdbcType=VARCHAR},
                        #{item.createUserId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.fbtReceiveStatus,jdbcType=TINYINT},
                        #{item.forwardStatus,jdbcType=TINYINT},
                        #{item.signStatus,jdbcType=TINYINT},
                        #{item.forwardFlag,jdbcType=TINYINT},
                        #{item.forwardNo,jdbcType=VARCHAR},
                        #{item.forwardSupplier,jdbcType=VARCHAR},
                        #{item.axPostAddress,jdbcType=VARCHAR},
                        #{item.originalAddress,jdbcType=VARCHAR},
                        #{item.changedAddress,jdbcType=VARCHAR},
                        #{item.remark,jdbcType=VARCHAR},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
