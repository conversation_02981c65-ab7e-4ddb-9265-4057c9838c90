<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardcreditmanagerrelation.CardCreditManagerRelationDAO">
    <resultMap id="CardCreditManagerRelationResultMap" type="com.fenbei.fx.card.dao.cardcreditmanagerrelation.po.CardCreditManagerRelationPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="record_id" property="recordId" jdbcType="VARCHAR"/>
        <result column="apply_trans_no" property="applyTransNo" jdbcType="VARCHAR"/>
        <result column="apply_title" property="applyTitle" jdbcType="VARCHAR"/>
        <result column="apply_amount" property="applyAmount" jdbcType="DECIMAL"/>
        <result column="unchecked_amount" property="uncheckedAmount" jdbcType="DECIMAL"/>
        <result column="apply_time" property="applyTime" jdbcType="TIMESTAMP"/>
        <result column="relation_amount" property="relationAmount" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`fx_card_id`,`company_id`,`employee_id`,`record_id`,`apply_trans_no`,`apply_title`,`apply_amount`,`unchecked_amount`,`apply_time`,`relation_amount`,`create_time`,`update_time`,`delete_flag`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_credit_manager_relation` (`fx_card_id`,`company_id`,`employee_id`,`record_id`,`apply_trans_no`,`apply_title`,`apply_amount`,`unchecked_amount`,`apply_time`,`relation_amount`,`create_time`,`update_time`,`delete_flag`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_credit_manager_relation`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_credit_manager_relation`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_credit_manager_relation`</sql>
    <sql id="from_sql">FROM `fx_card_credit_manager_relation`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_credit_manager_relation`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.recordId,jdbcType=VARCHAR},
                        #{item.applyTransNo,jdbcType=VARCHAR},
                        #{item.applyTitle,jdbcType=VARCHAR},
                        #{item.applyAmount,jdbcType=DECIMAL},
                        #{item.uncheckedAmount,jdbcType=DECIMAL},
                        #{item.applyTime,jdbcType=TIMESTAMP},
                        #{item.relationAmount,jdbcType=DECIMAL},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                        #{item.deleteFlag,jdbcType=TINYINT},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
