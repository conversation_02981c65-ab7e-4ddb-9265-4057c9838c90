<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbei.fx.card.dao.cardverificationflow.CardVerificationFlowDAO">
    <resultMap id="CardVerificationFlowResultMap" type="com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="VARCHAR"/>
        <result column="employee_id" property="employeeId" jdbcType="VARCHAR"/>
        <result column="fx_card_id" property="fxCardId" jdbcType="VARCHAR"/>
        <result column="credit_id" property="creditId" jdbcType="VARCHAR"/>
        <result column="verification_id" property="verificationId" jdbcType="VARCHAR"/>
        <result column="check_amount" property="checkAmount" jdbcType="DECIMAL"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="select_column_list">SELECT `id`,`company_id`,`employee_id`,`fx_card_id`,`credit_id`,`verification_id`,`check_amount`,`type`,`create_time`,`update_time`</sql>
    <sql id="select_not_del">AND is_del = 0</sql>
    <sql id="order_by_sql">ORDER BY id DESC</sql>
    <sql id="insert_into_sql">INSERT INTO `fx_card_verification_flow` (`company_id`,`employee_id`,`fx_card_id`,`credit_id`,`verification_id`,`check_amount`,`type`,`create_time`,`update_time`)</sql>
    <sql id="delete_from_sql">UPDATE `fx_card_verification_flow`</sql>
    <sql id="update_table_sql">UPDATE `fx_card_verification_flow`</sql>
    <sql id="select_count_sql">SELECT COUNT(1) FROM `fx_card_verification_flow`</sql>
    <sql id="from_sql">FROM `fx_card_verification_flow`</sql>
    <sql id="insert_table_sql">INSERT INTO `fx_card_verification_flow`</sql>
    <sql id="limit_1_sql">LIMIT 1</sql>

    <insert id="insertBatchAllColumn" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        <include refid="insert_into_sql"/>
        <trim prefix="VALUES" suffixOverrides=",">
            <if test="list != null">
                <foreach collection="list" item="item" index="index" separator=",">
                    <trim prefix="(" suffix=")" suffixOverrides=",">
                        #{item.companyId,jdbcType=VARCHAR},
                        #{item.employeeId,jdbcType=VARCHAR},
                        #{item.fxCardId,jdbcType=VARCHAR},
                        #{item.creditId,jdbcType=VARCHAR},
                        #{item.verificationId,jdbcType=VARCHAR},
                        #{item.checkAmount,jdbcType=DECIMAL},
                        #{item.type,jdbcType=INTEGER},
                        #{item.createTime,jdbcType=TIMESTAMP},
                        #{item.updateTime,jdbcType=TIMESTAMP},
                    </trim>
                </foreach>
            </if>
        </trim>
    </insert>
</mapper>
