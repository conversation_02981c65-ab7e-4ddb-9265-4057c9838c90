package com.fenbei.fx.card.dao.cardchargingnotice.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.finhub.framework.mybatis.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡计费通知表 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_charging_notice", resultMap = "CardChargingNoticeResultMap")
public class CardChargingNoticePO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_charging_notice";

    public static final String DB_COL_REQUEST_ID = "request_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_EVENT_TYPE = "event_type";

    public static final String DB_COL_TRADE_AMOUNT = "trade_amount";

    public static final String DB_COL_CARD_STATUS = "card_status";

    public static final String DB_COL_CALL_STATUS = "call_status";

    public static final String DB_COL_CALL_NUM = "call_num";

    public static final String DB_COL_CALL_NEXT_TIME = "call_next_time";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";


    /**
     * 请求单号
     */
    private String requestId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 事件类型 1 虚拟卡开卡 2 实体卡开卡 3 消费
     */
    private Integer eventType;

    /**
     * 交易金额 单位分
     */
    private BigDecimal tradeAmount;

    /**
     * 卡状态
     */
    private Integer cardStatus;

    /**
     * 通知状态 0 未通知 1 通知成功 2 通知失败
     */
    private Integer callStatus;

    /**
     * 通知次数
     */
    private Integer callNum;

    /**
     * 下次通知时间
     */
    private Date callNextTime;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

}
