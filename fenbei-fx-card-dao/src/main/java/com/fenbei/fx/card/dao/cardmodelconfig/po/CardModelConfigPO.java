package com.fenbei.fx.card.dao.cardmodelconfig.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 国际卡使用模式配置 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_model_config", resultMap = "CardModelConfigResultMap")
public class CardModelConfigPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_model_config";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_MODEL_TYPE = "model_type";

    public static final String DB_COL_ACTIVE_MODEL = "active_model";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";


    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    private Integer modelType;

    /**
     * 国际卡企业生效模式: 1.普通模式,2.备用金模式
     */
    private Integer activeModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
