package com.fenbei.fx.card.dao.cardorder.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡订单 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_order", resultMap = "CardOrderResultMap")
public class CardOrderPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_order";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_BIZ_NO = "biz_no";

    public static final String DB_COL_ORI_BIZ_NO = "ori_biz_no";

    public static final String DB_COL_TYPE = "type";

    public static final String DB_COL_TRADE_CURRENCY = "trade_currency";

    public static final String DB_COL_TRADE_AMOUNT = "trade_amount";

    public static final String DB_COL_BILL_TRADE_CURRENCY = "bill_trade_currency";

    public static final String DB_COL_BILL_TRADE_AMOUNT = "bill_trade_amount";

    public static final String DB_COL_TRADE_NAME = "trade_name";

    public static final String DB_COL_TRADE_TIME = "trade_time";

    public static final String DB_COL_TRADE_ADDRESS = "trade_address";

    public static final String DB_COL_TRADE_REMARK = "trade_remark";

    public static final String DB_COL_CHECK_STATUS = "check_status";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_BASE_TRADE_CURRENCY = "base_trade_currency";

    public static final String DB_BASE_TRADE_AMOUNT = "base_trade_amount";

    public static final String DB_BASE_TRADE_ID = "trade_id";

    public static final String DB_BASE_SUB_TRADE_ID = "sub_trade_id";

    public static final String DB_BASE_CHECKED_AMOUNT = "checked_amount";

    public static final String DB_BASE_UNCHECKED_AMOUNT = "unchecked_amount";

    public static final String DB_BASE_CHECKING_AMOUNT = "checking_amount";

    public static final String DB_BASE_NEED_NOT_CHECK_AMOUNT = "need_not_check_amount";

    public static final String DB_BASE_REFUND_AMOUNT = "refund_amount";

    public static final String DB_BASE_APPLY_BIND = "apply_bind";

    public static final String DB_BASE_COST_ID = "cost_id";

    public static final String DB_BASE_MASKED_CARD_NUMBER = "masked_card_number";

    public static final String DB_BASE_TRADE_CNY_EXCHANGE_RATE = "trade_cny_exchange_rate";

    public static final String DB_BASE_CNY_TRADE_AMOUNT = "cny_trade_amount";

    public static final String DB_BASE_CARD_PLATFORM = "card_platform";

    public static final String DB_BASE_ORDER_SHOW = "order_show";

    public static final String DB_COL_CARD_FORM_FACTOR = "card_form_factor";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_NAME_ON_CARD = "name_on_card";

    public static final String DB_COL_BILL_TRADE_CNY_EXCHANGE_RATE = "bill_trade_cny_exchange_rate";


    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 交易单号
     */
    private String bizNo;

    /**
     * 退款时代表原业务单号
     */
    private String oriBizNo;

    /**
     * 11消费,12退款
     */
    private Integer type;

    /**
     * 币种 美元-USD
     */
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    private String tradeName;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 交易地
     */
    private String tradeAddress;

    /**
     * 交易备注
     */
    private String tradeRemark;

    /**
     * 核销状态
     */
    private Integer checkStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 折算币种 美元-USD
     *
     */
    private String billTradeCurrency;

    /**
     * 折算交易金额 单位：分
     */
    private BigDecimal billTradeAmount;
    /**
     * 通道的交易ID
     */
    private String tradeId;

    /**
     * 通道网络交易ID
     */
    private String subTradeId;
    /**
     * 已核销金额 单位 分
     */
    private BigDecimal checkedAmount;
    /**
     * 未核销金额
     */
    private BigDecimal uncheckedAmount;
    /**
     * 核销中金额
     */
    private BigDecimal checkingAmount;

    /**
     * 无需核销金额
     */
    private BigDecimal needNotCheckAmount;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 是否已绑定费用 1 未绑定 2已绑定
     */
    private Integer applyBind;

    private Integer costId;

    /**
     * 卡号
     */
    private String maskedCardNumber;

    /**
     * 交易币种兑人民币汇率
     */
    private BigDecimal tradeCnyExchangeRate;

    /**
     * 人民币金额
     */
    private BigDecimal cnyTradeAmount;

    /**
     * 渠道信息
     */
    private String cardPlatform;
    /**
     * 订单展示状态
     */
    private Integer orderShow;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;
    /**
     * 折算币种兑换人民币汇率
     */
    private BigDecimal billTradeCnyExchangeRate;
}
