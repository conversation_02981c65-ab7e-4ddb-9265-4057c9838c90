package com.fenbei.fx.card.dao.cardholder.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 持卡人 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_cardholder", resultMap = "CardholderResultMap")
public class CardholderPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_cardholder";

    public static final String DB_COL_FX_CARDHOLDER_ID = "fx_cardholder_id";

    public static final String DB_COL_BANK_CARDHOLDER_ID = "bank_cardholder_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_EMPLOYEE_NAME = "employee_name";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_FIRST_NAME = "first_name";

    public static final String DB_COL_LAST_NAME = "last_name";

    public static final String DB_COL_NAME = "name";

    public static final String DB_COL_EMAIL = "email";

    public static final String DB_COL_BIRTH = "birth";

    public static final String DB_COL_NATION_CODE = "nation_code";

    public static final String DB_COL_PHONE = "phone";

    public static final String DB_COL_HOLDER_STATUS = "holder_status";

    public static final String DB_COL_IDENTIFICATION_COUNTRY = "identification_country";

    public static final String DB_COL_IDENTIFICATION_TYPE = "identification_type";

    public static final String DB_COL_IDENTIFICATION_NUMBER = "identification_number";

    public static final String DB_COL_IDENTIFICATION_EXPIRY_DATE = "identification_expiry_date";

    public static final String DB_COL_IDENTIFICATION_EXPIRY_TYPE = "identification_expiry_type";

    public static final String DB_COL_ADDRESS = "address";

    public static final String DB_COL_POSTAL_ADDRESS = "postal_address";

    public static final String DB_COL_CREATE_USER_ID = "create_user_id";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";


    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 渠道方持卡人id
     */
    private String bankCardholderId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 员工id
     */
    private String employeeName;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 申请人名
     */
    private String firstName;

    /**
     * 申请人姓
     */
    private String lastName;

    /**
     * 姓名
     */
    private String name;

    /**
     * email地址
     */
    private String email;

    /**
     * 出生日期，格式为YYY-MM-DD
     */
    private Date birth;

    /**
     * 国家区号
     */
    private String nationCode;

    /**
     * 持卡人手机号
     */
    private String phone;

    /**
     * 持卡人状态：1.生效中 2.已禁用 3.已失效
     */
    private Integer holderStatus;

    /**
     * 证件的国家:US
     */
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    private Integer identificationType;

    /**
     * 证件号
     */
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    private Date identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围，2-长期有效
     */
    private Integer identificationExpiryType;

    /**
     * 地址详情：city，country，postcode，state，detail
     */
    private String address;

    /**
     * 邮寄地址详情：city，country，postcode，state，detail
     */
    private String postalAddress;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
