package com.fenbei.fx.card.dao.userinfo.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 用户信息 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_user_info", resultMap = "UserInfoResultMap")
public class UserInfoPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_user_info";

    public static final String DB_COL_USER_ID = "user_id";

    public static final String DB_COL_USER_NAME = "user_name";

    public static final String DB_COL_USER_PHONE = "user_phone";

    public static final String DB_COL_USER_EMAIL = "user_email";

    public static final String DB_COL_ID_CARD_NO = "id_card_no";

    public static final String DB_COL_EMPLOYEE_LOCATION = "employee_location";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_COMPANY_NAME = "company_name";

    public static final String DB_COL_COMPANY_ACCOUNT_ID = "company_account_id";

    public static final String DB_COL_CERTIFICATE_NUM = "certificate_num";

    public static final String DB_COL_OFFICE_ADDRESS = "office_address";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_CARD_BRAND = "card_brand";

    public static final String DB_COL_BANK_CARD_ID = "bank_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_CARD_PUBLIC_TIME = "card_public_time";

    public static final String DB_COL_CARD_PURPOSE = "card_purpose";

    public static final String DB_COL_CARD_CVV = "card_cvv";

    public static final String DB_COL_NAME_ON_CARD = "name_on_card";

    public static final String DB_COL_CARD_EXPIRY_MONTH = "card_expiry_month";

    public static final String DB_COL_CARD_EXPIRY_YEAR = "card_expiry_year";

    public static final String DB_COL_REMARK = "remark";


    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 常驻地
     */
    private String employeeLocation;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业账户ID
     */
    private String companyAccountId;

    /**
     * 纳税人识别号
     */
    private String certificateNum;

    /**
     * 办公地址
     */
    private String officeAddress;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 发卡渠道
     */
    private String cardPlatform;

    /**
     * 发卡品牌
     */
    private String cardBrand;

    /**
     * 银行卡ID
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 发卡用途
     */
    private String cardPurpose;

    /**
     * 卡cvv码
     */
    private String cardCvv;

    /**
     * 卡片上姓名
     */
    private String nameOnCard;

    /**
     * 卡到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡到期年份
     */
    private String cardExpiryYear;

    /**
     * 备注
     */
    private String remark;

}
