package com.fenbei.fx.card.dao.card;

import com.finhub.framework.mybatis.dao.BaseDAO;

import com.fenbei.fx.card.dao.card.po.CardPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 国际卡 DAO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardDAO extends BaseDAO<CardPO> {

    /**
     * 获取卡可用余额合计
     * @param companyId
     * @return
     */
    List<Map<String, Object>> getCardSumBalance(@Param("companyId") String companyId);

}
