package com.fenbei.fx.card.dao.card.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 国际卡 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card", resultMap = "CardResultMap")
public class CardPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_BANK_CARD_ID = "bank_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_BANK_MCH_ID = "bank_mch_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_COMPANY_ACCOUNT_ID = "company_account_id";

    public static final String DB_COL_CARD_ISSUE_TO = "card_issue_to";

    public static final String DB_COL_CARD_FORM_FACTOR = "card_form_factor";

    public static final String DB_COL_CARD_CVV = "card_cvv";

    public static final String DB_COL_CARD_EXPIRY_YEAR = "card_expiry_year";

    public static final String DB_COL_CARD_EXPIRY_MONTH = "card_expiry_month";

    public static final String DB_COL_NAME_ON_CARD = "name_on_card";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_CARD_BRAND = "card_brand";

    public static final String DB_COL_CARD_PUBLIC_TIME = "card_public_time";

    public static final String DB_COL_CARD_STATUS = "card_status";

    public static final String DB_COL_ACTIVE_STATUS = "active_status";

    public static final String DB_COL_FX_CARDHOLDER_ID = "fx_cardholder_id";

    public static final String DB_COL_CARD_PURPOSE = "card_purpose";

    public static final String DB_COL_CURRENCY = "currency";

    public static final String DB_COL_BALANCE = "balance";

    public static final String DB_COL_CARD_LIMITS = "card_limits";

    public static final String DB_COL_CREATE_USER_ID = "create_user_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";

    public static final String DB_COL_FREEZEN_BALANCE = "freezen_balance";

    public static final String DB_COL_CARD_PIN = "card_pin";


    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 企业商户ID
     */
    private String bankMchId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private Integer activeStatus;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 卡可用余额
     */
    private BigDecimal balance;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

    /**
     * 冻结金额
     */
    private BigDecimal freezenBalance;

    /**
     * 实体卡支付密码
     */
    private String cardPin;

}
