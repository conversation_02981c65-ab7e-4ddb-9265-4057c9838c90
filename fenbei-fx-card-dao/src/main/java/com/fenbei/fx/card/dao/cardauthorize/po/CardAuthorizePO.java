package com.fenbei.fx.card.dao.cardauthorize.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡授权表 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_authorize", resultMap = "CardAuthorizeResultMap")
public class CardAuthorizePO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_authorize";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_TRADE_CURRENCY = "trade_currency";

    public static final String DB_COL_TRADE_AMOUNT = "trade_amount";

    public static final String DB_COL_TRADE_NAME = "trade_name";

    public static final String DB_COL_TRADE_TIME = "trade_time";

    public static final String DB_COL_TRADE_ADDRESS = "trade_address";

    public static final String DB_COL_SOURCE_DATA = "source_data";

    public static final String DB_COL_AUTH_STATUS = "auth_status";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_COL_TRADE_ID = "trade_id";

    public static final String DB_COL_TRADE_TYPE = "trade_type";

    public static final String DB_COL_SUB_TRADE_ID = "sub_trade_id";


    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 币种 美元-USD
     */
    private String tradeCurrency;

    /**
     * 交易金额 单位：分
     */
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    private String tradeName;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 交易地
     */
    private String tradeAddress;

    /**
     * 原始请求
     */
    private String sourceData;

    /**
     * 授权状态
     */
    private Integer authStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String tradeId;
    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 通道网络交易ID
     */
    private String subTradeId;
}
