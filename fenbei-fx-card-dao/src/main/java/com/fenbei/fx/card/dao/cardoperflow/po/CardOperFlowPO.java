package com.fenbei.fx.card.dao.cardoperflow.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 卡操作流水 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_oper_flow", resultMap = "CardOperFlowResultMap")
public class CardOperFlowPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_oper_flow";

    public static final String DB_COL_OPER_FLOW_ID = "oper_flow_id";

    public static final String DB_COL_APPLY_ID = "apply_id";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_BANK_CARD_ID = "bank_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_OPERATOR_ID = "operator_id";

    public static final String DB_COL_OPERATOR_NAME = "operator_name";

    public static final String DB_COL_OPERATE_TYPE = "operate_type";

    public static final String DB_COL_OPERATE_DESC = "operate_desc";

    public static final String DB_COL_PRE_SNAPSHOT = "pre_snapshot";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";


    /**
     * 操作记录id
     */
    private String operFlowId;

    /**
     * 操作申请id
     */
    private String applyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作类型 1.创建 2.更新
     */
    private Integer operateType;

    /**
     * 操作描述
     */
    private String operateDesc;

    /**
     * 操作前快照
     */
    private String preSnapshot;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
