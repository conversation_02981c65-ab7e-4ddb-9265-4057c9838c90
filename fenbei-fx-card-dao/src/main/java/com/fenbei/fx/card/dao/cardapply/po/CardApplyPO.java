package com.fenbei.fx.card.dao.cardapply.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 国际卡操作申请 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_apply", resultMap = "CardApplyResultMap")
public class CardApplyPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_apply";

    public static final String DB_COL_APPLY_ID = "apply_id";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_BANK_CARD_ID = "bank_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_BANK_MCH_ID = "bank_mch_id";

    public static final String DB_COL_COMPANY_ACCOUNT_ID = "company_account_id";

    public static final String DB_COL_APPLY_TYPE = "apply_type";

    public static final String DB_COL_APPLY_STATUS = "apply_status";

    public static final String DB_COL_REFUSE_REASON = "refuse_reason";

    public static final String DB_COL_CARD_ISSUE_TO = "card_issue_to";

    public static final String DB_COL_CARD_FORM_FACTOR = "card_form_factor";

    public static final String DB_COL_CARD_CVV = "card_cvv";

    public static final String DB_COL_CARD_EXPIRY_YEAR = "card_expiry_year";

    public static final String DB_COL_CARD_EXPIRY_MONTH = "card_expiry_month";

    public static final String DB_COL_NAME_ON_CARD = "name_on_card";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_CARD_BRAND = "card_brand";

    public static final String DB_COL_CARD_PUBLIC_TIME = "card_public_time";

    public static final String DB_COL_FX_CARDHOLDER_ID = "fx_cardholder_id";

    public static final String DB_COL_NATION_CODE = "nation_code";

    public static final String DB_COL_APPLYER_PHONE = "applyer_phone";

    public static final String DB_COL_APPLYER_EMAIL = "applyer_email";

    public static final String DB_COL_APPLYER_FIRST_NAME = "applyer_first_name";

    public static final String DB_COL_APPLYER_LAST_NAME = "applyer_last_name";

    public static final String DB_COL_CARD_PURPOSE = "card_purpose";

    public static final String DB_COL_APPROVE_USER_ID = "approve_user_id";

    public static final String DB_COL_APPROVE_USER_NAME = "approve_user_name";

    public static final String DB_COL_APPROVE_TIME = "approve_time";

    public static final String DB_COL_CURRENCY = "currency";

    public static final String DB_COL_CARD_LIMITS = "card_limits";

    public static final String DB_COL_CREATE_USER_ID = "create_user_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";

    public static final String DB_COL_POSTAL_ADDRESS = "postal_address";

    public static final String DB_COL_OUT_BATCH_NO = "out_batch_no";

    public static final String DB_COL_LOGISTICS_COMPANY = "logistics_company";

    public static final String DB_COL_LOGISTICS_NO = "logistics_no";

    public static final String DB_COL_APPLY_ORDER_STATUS = "apply_order_status";


    /**
     * 操作申请id
     */
    private String applyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 企业商户ID
     */
    private String bankMchId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 申请类型 1.创建 2.更新
     */
    private Integer applyType;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，4.银行处理中 5.银行失败 6.银行成功'
     */
    private Integer applyStatus;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 国家区号
     */
    private String nationCode;

    /**
     * 申请人手机号+区号
     */
    private String applyerPhone;

    /**
     * 申请人邮箱
     */
    private String applyerEmail;

    /**
     * 申请人名
     */
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    private String applyerLastName;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 审批人
     */
    private String approveUserId;

    /**
     * 审批人姓名
     */
    private String approveUserName;

    /**
     * 审批通过时间
     */
    private Date approveTime;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;


    /**
     * 邮寄地址详情：city，country，postcode，state，detail
     */
    private String postalAddress;

    private String outBatchNo;

    /**
     * 物流公司编码
     */
    private String logisticsCompany;
    /**
     * 物流公司订单
     */
    private String logisticsNo;
    /**
     * 订单状态
     */
    private String applyOrderStatus;

}
