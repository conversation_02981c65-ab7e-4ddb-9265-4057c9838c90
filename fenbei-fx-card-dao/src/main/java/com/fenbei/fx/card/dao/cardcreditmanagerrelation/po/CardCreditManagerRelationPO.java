package com.fenbei.fx.card.dao.cardcreditmanagerrelation.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.finhub.framework.mybatis.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 关联申请单记录 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_credit_manager_relation", resultMap = "CardCreditManagerRelationResultMap")
public class CardCreditManagerRelationPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_credit_manager_relation";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_RECORD_ID = "record_id";

    public static final String DB_COL_APPLY_TRANS_NO = "apply_trans_no";

    public static final String DB_COL_APPLY_TITLE = "apply_title";

    public static final String DB_COL_APPLY_AMOUNT = "apply_amount";

    public static final String DB_COL_UNCHECKED_AMOUNT = "unchecked_amount";

    public static final String DB_COL_APPLY_TIME = "apply_time";

    public static final String DB_COL_RELATION_AMOUNT = "relation_amount";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";


    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 额度管理biz_no
     */
    private String recordId;

    /**
     * 申请单ID
     */
    private String applyTransNo;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 可核销金额
     */
    private BigDecimal uncheckedAmount;

    /**
     * 提交时间
     */
    private Date applyTime;

    /**
     * 关联金额
     */
    private BigDecimal relationAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
