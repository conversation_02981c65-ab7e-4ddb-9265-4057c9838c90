package com.fenbei.fx.card.dao.cardcreditapplyorder.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 国际卡额度发放单 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_credit_apply_order", resultMap = "CardCreditApplyOrderResultMap")
public class CardCreditApplyOrderPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_credit_apply_order";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_APPLY_BATCH_NO = "apply_batch_no";

    public static final String DB_COL_APPLY_ORDER_ID = "apply_order_id";

    public static final String DB_COL_APPLY_ID = "apply_id";

    public static final String DB_COL_MEANING_NO = "meaning_no";

    public static final String DB_COL_APPLY_ORDER_TYPE = "apply_order_type";

    public static final String DB_COL_APPLY_ORDER_SUB_TYPE = "apply_order_sub_type";

    public static final String DB_COL_ACTIVE_MODEL = "active_model";

    public static final String DB_COL_TITLE = "title";

    public static final String DB_COL_CURRENCY = "currency";

    public static final String DB_COL_APPLY_AMOUNT = "apply_amount";

    public static final String DB_COL_APPLY_REASON = "apply_reason";

    public static final String DB_COL_APPLY_REASON_ID = "apply_reason_id";

    public static final String DB_COL_APPLY_STATE = "apply_state";

    public static final String DB_COL_APPLY_RESULT_DESC = "apply_result_desc";

    public static final String DB_COL_DEDUCTION_MODE = "deduction_mode";

    public static final String DB_COL_CREATER_ID = "creater_id";

    public static final String DB_COL_CREATER_NAME = "creater_name";

    public static final String DB_COL_APPLICANT_ID = "applicant_id";

    public static final String DB_COL_APPLICANT_NAME = "applicant_name";

    public static final String DB_COL_APPLICANT_ORG_ID = "applicant_org_id";

    public static final String DB_COL_APPLICANT_ORG_NAME = "applicant_org_name";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_BANK_NAME = "bank_name";

    public static final String DB_COL_ISSUED_ID = "issued_id";

    public static final String DB_COL_ISSUED_NAME = "issued_name";

    public static final String DB_COL_ISSUED_TIME = "issued_time";

    public static final String DB_COL_COST_CATEGORY_ID = "cost_category_id";

    public static final String DB_COL_COST_CATEGORY_NAME = "cost_category_name";

    public static final String DB_COL_COST_ATTRIBUTION_OPT = "cost_attribution_opt";

    public static final String DB_COL_COST_ATTRIBUTIONS = "cost_attributions";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_COL_EMPLOYEE_DEPT = "employee_dept";


    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 批量创建的批次号
     */
    private String applyBatchNo;

    /**
     * 单据主键ID
     */
    private String applyOrderId;

    /**
     * 申请单ID编号
     */
    private String applyId;

    /**
     * 单据编号
     */
    private String meaningNo;

    /**
     * 审批单类型  40海外卡发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 43:海外卡发放单(小类别)
     */
    private Integer applyOrderSubType;

    /**
     * 1: 普通模式2: 备用金模式;
     */
    private Integer activeModel;

    /**
     * 标题
     */
    private String title;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 申请总金额(单位：分)
     */
    private BigDecimal applyAmount;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请事由id
     */
    private Integer applyReasonId;

    /**
     * -1:制单失败 0:制单中 1.制单成功(待发放) 2.发放中 3.下发额度成功 4.下发额度失败
     */
    private Integer applyState;

    /**
     * 制单和下发结果描述
     */
    private String applyResultDesc;

    /**
     * 1核销扣;2申请扣
     */
    private Integer deductionMode;

    /**
     * 制单人ID
     */
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;

    /**
     * 申请人id
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人直属部门ID
     */
    private String applicantOrgId;

    /**
     * 申请人直属部门名称
     */
    private String applicantOrgName;

    /**
     * 申请人卡id
     */
    private String fxCardId;

    /**
     * 申请人银行卡号
     */
    private String bankCardNo;

    /**
     * 卡归属银行
     */
    private String bankName;

    /**
     * 发放人ID
     */
    private String issuedId;

    /**
     * 发放人名称
     */
    private String issuedName;

    /**
     * 发放时间
     */
    private Date issuedTime;

    /**
     * 费用ID
     */
    private String costCategoryId;

    /**
     * 费用类别名称
     */
    private String costCategoryName;

    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;

    /**
     * 费用归属
     */
    private String costAttributions;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * true: 员工所属部门 false: 不是
     */
    private Integer employeeDept;

}
