package com.fenbei.fx.card.dao.cardmail.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.finhub.framework.mybatis.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实体卡邮寄管理表 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_mail", resultMap = "CardMailResultMap")
public class CardMailPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_mail";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_BANK_CARD_ID = "bank_card_id";

    public static final String DB_COL_BANK_CARD_NO = "bank_card_no";

    public static final String DB_COL_CARD_MAIL_NO = "card_mail_no";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_EMPLOYEE_NAME = "employee_name";

    public static final String DB_COL_EMPLOYEE_PHONE = "employee_phone";

    public static final String DB_COL_COMPANY_ACCOUNT_ID = "company_account_id";

    public static final String DB_COL_CARD_OWNER_TYPE = "card_owner_type";

    public static final String DB_COL_CARD_FORM_FACTOR = "card_form_factor";

    public static final String DB_COL_CARD_CVV = "card_cvv";

    public static final String DB_COL_CARD_EXPIRY_YEAR = "card_expiry_year";

    public static final String DB_COL_CARD_EXPIRY_MONTH = "card_expiry_month";

    public static final String DB_COL_NAME_ON_CARD = "name_on_card";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_CARD_BRAND = "card_brand";

    public static final String DB_COL_CARD_ISSUANCE_TIME = "card_issuance_time";

    public static final String DB_COL_FX_CARDHOLDER_ID = "fx_cardholder_id";

    public static final String DB_COL_CREATE_USER_ID = "create_user_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_FBT_RECEIVE_STATUS = "fbt_receive_status";

    public static final String DB_COL_FORWARD_STATUS = "forward_status";

    public static final String DB_COL_SIGN_STATUS = "sign_status";

    public static final String DB_COL_FORWARD_FLAG = "forward_flag";

    public static final String DB_COL_FORWARD_NO = "forward_no";

    public static final String DB_COL_FORWARD_SUPPLIER = "forward_supplier";

    public static final String DB_COL_ORIGINAL_ADDRESS = "original_address";

    public static final String DB_COL_CHANGED_ADDRESS = "changed_address";

    public static final String DB_COL_REMARK = "remark";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_COL_DELETE_FLAG = "delete_flag";


    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 卡邮寄编号（line2）
     */
    private String cardMailNo;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 员工手机
     */
    private String employeePhone;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 卡归属类型：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardOwnerType;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardIssuanceTime;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 分贝通收件状态：1-未收件，2-已收件，3-异常
     */
    private Integer fbtReceiveStatus;

    /**
     * 转运状态：1-未寄出，2-已寄出
     */
    private Integer forwardStatus;

    /**
     * 签收状态：1-未签收，2-已签收，3-异常
     */
    private Integer signStatus;

    /**
     * 是否转寄：0-不，1-是
     */
    private Integer forwardFlag;

    /**
     * 转寄单号
     */
    private String forwardNo;

    /**
     * 转供应商
     */
    private String forwardSupplier;

    /**
     * 发卡方实际邮寄地址地址
     */
    private String axPostAddress;

    /**
     * 原收获地址
     */
    private String originalAddress;

    /**
     * 变更后收货地址
     */
    private String changedAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
