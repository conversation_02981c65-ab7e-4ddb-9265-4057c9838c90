package com.fenbei.fx.card.dao.cardwrongpaidflow.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 错花还款流水表 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_wrong_paid_flow", resultMap = "CardWrongPaidFlowResultMap")
public class CardWrongPaidFlowPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_wrong_paid_flow";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_CARD_PLATFORM = "card_platform";

    public static final String DB_COL_BIZ_NO = "biz_no";

    public static final String DB_COL_ORDER_BIZ_NO = "order_biz_no";

    public static final String DB_COL_TRADE_CURRENCY = "trade_currency";

    public static final String DB_COL_OPERATION_AMOUNT = "operation_amount";

    public static final String DB_COL_OPERATION_TIME = "operation_time";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";


    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 交易单号
     */
    private String bizNo;

    /**
     * 关联的交易单号
     */
    private String orderBizNo;

    /**
     * 币种
     */
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
