package com.fenbei.fx.card.dao.cardverificationflow.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Date;

/**
 * 核销记录表 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_card_verification_flow", resultMap = "CardVerificationFlowResultMap")
public class CardVerificationFlowPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_card_verification_flow";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_CREDIT_ID = "credit_id";

    public static final String DB_COL_VERIFICATION_ID = "verification_id";

    public static final String DB_COL_CHECK_AMOUNT = "check_amount";

    public static final String DB_COL_TYPE = "type";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";


    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 用户分贝通卡id
     */
    private String fxCardId;

    /**
     * 关联额度申请单ID
     */
    private String creditId;

    /**
     * 核销单单号
     */
    private String verificationId;

    /**
     * 本次核销金额
     */
    private BigDecimal checkAmount;

    /**
     * 核销状态： 1：核销  2：撤回
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
