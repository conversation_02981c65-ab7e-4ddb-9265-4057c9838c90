package com.fenbei.fx.card.dao.bankcardflow.po;

import com.finhub.framework.mybatis.po.BasePO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 PO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fx_bank_card_flow", resultMap = "BankCardFlowResultMap")
public class BankCardFlowPO extends BasePO {

    private static final long serialVersionUID = 5409185459234711691L;

    public static final String DB_TABLE_NAME = "fx_bank_card_flow";

    public static final String DB_COL_FX_CARD_ID = "fx_card_id";

    public static final String DB_COL_COMPANY_ID = "company_id";

    public static final String DB_COL_EMPLOYEE_ID = "employee_id";

    public static final String DB_COL_BIZ_NO = "biz_no";

    public static final String DB_COL_OPERATION_TYPE = "operation_type";

    public static final String DB_COL_CURRENT_AMOUNT = "current_amount";

    public static final String DB_COL_OPERATION_AMOUNT = "operation_amount";

    public static final String DB_COL_BALANCE = "balance";

    public static final String DB_COL_CARD_MODEL = "card_model";

    public static final String DB_COL_CREATE_TIME = "create_time";

    public static final String DB_COL_UPDATE_TIME = "update_time";

    public static final String DB_COL_CURRENT_FREEZEN_AMOUNT = "current_freezen_amount";

    public static final String DB_COL_OPERATION_FREEZEN_AMOUNT = "operation_freezen_amount";

    public static final String DB_COL_FREEZEN_BALANCE = "freezen_balance";

    public static final String DB_COL_ORI_BIZ_NO ="ori_biz_no";


    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     *
     */
    private String bizNo;

    /**
     * 4申请额度,5退还额度,11消费,12退款,16冲正,41还款,51企业回收额度,52系统回收额度,53还款退回
     */
    private Integer operationType;

    /**
     * 当前额度
     */
    private BigDecimal currentAmount;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 卡的余额
     */
    private BigDecimal balance;

    /**
     * 1普通模式 2备用金模式
     */
    private Integer cardModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 当前额度
     */
    private BigDecimal currentFreezenAmount;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationFreezenAmount;

    /**
     * 卡的余额
     */
    private BigDecimal freezenBalance;
    /**
     * 原始交易编号
     */
    private String oriBizNo;
}
