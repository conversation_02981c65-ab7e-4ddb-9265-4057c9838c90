package com.fenbei.fx.card.constants;

/**
 * <AUTHOR>
 * @date 2023-04-19 6:42 下午
 */
public class RedisKeyConstant {

    // 开卡申请
    public static final String CREATE_CARD_APPLY_LOCK = "create:card:apply:lock_";

    // 开卡审批
    public static final String CREATE_CARD_APPROVE_LOCK = "create:card:approve:lock_";

    // 开卡审批
    public static final String CREATE_CARD_STEREO_APPROVE_LOCK = "create:card:stereo_approve:lock_";

    // 持卡人申请创建
    public static final String APPLY_REDIS_KEY_PRE = "cardholder_apply#";

    // 持卡人状态修改
    public static final String APPLY_MODIFY_REDIS_KEY_PRE = "cardholder_apply_modify#";

    // 交易通知
    public static final String TRADE_REDIS_KEY_PRE = "webhook_trade#";

    // 额度下发申请
    public static final String CREATE_CARD_CREDIT_ORDER_LOCK = "create:card:credit:order:lock_";

    public static final String CREATE_CARD_TRY_BATCH_ORDER_LOCK = "create:card:try:batch:order:lock_";

}
