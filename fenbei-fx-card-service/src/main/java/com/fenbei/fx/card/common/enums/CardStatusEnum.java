package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.service.card.dto.CardCanOperationDTO;
import com.google.common.collect.Lists;
import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Created by FBT on 2023/4/18.
 */
@AllArgsConstructor
public enum CardStatusEnum {

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     *
     * operateType :  4：生效中 5：已失效
     */
    WAITCHECK(7, CardShowStatusEnum.WAITCHECK.getStatus(),"","","待审核",2,false,false,true,true,false,true,true),
    PENDING(8, CardShowStatusEnum.PENDING.getStatus(),"PENDING","APPLY","审核中",2,false,false,false,false,true,false,true),
    ACTIVE(1, CardShowStatusEnum.ACTIVE.getStatus(),"ACTIVE","NORMAL","生效中",1,true,false,true,false,true,false,true),
    DISABLE(2, CardShowStatusEnum.DISABLE.getStatus(),"INACTIVE","","已禁用",1,false,true,false,false,false,false,true),
    LOSS(3, CardShowStatusEnum.DISABLE.getStatus(),"LOST","LOST","挂失",1,false,false,false,false,false,false,true),
    STOLEN(4, CardShowStatusEnum.DISABLE.getStatus(),"STOLEN","STOLEN","被盗",1,false,false,false,false,false,false,true),
    FAILED(9, CardShowStatusEnum.OPER_FAILED.getStatus(),"FAILED","","创建失败",2,false,false,true,true,false,false,true),
    LOGOUT(5, CardShowStatusEnum.DISABLE.getStatus(),"CLOSED","CANCEL","已注销",1,false,false,false,false,false,false,true),
    FREEZE(6, CardShowStatusEnum.DISABLE.getStatus(),"BLOCKED","CLOSED","冻结",1,false,true,true,false,true,false,true),
    REFUSE(10, CardShowStatusEnum.OPER_FAILED.getStatus(),"","","审核拒绝",2,false,false,true,true,false,false,true),
    STEREO_PENDING(11, CardShowStatusEnum.PENDING.getStatus(),"PENDING","APPLY","审核中",2,false,false,false,false,true,false,true),
    EXPIRED(12, CardShowStatusEnum.EXPIRED.getStatus(),"EXPIRED","EXPIRED","已过期",2,false,false,false,false,true,false,true),

    WAIT_ACTIVE(13, CardShowStatusEnum.WAIT_ACTIVE.getStatus(),"UNACTIVATED","UNACTIVATED","待激活",1,true,false,true,false,true,false,true),

    ;




    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("待审核", "card.status.waitcheck");
        I18N_KEY_MAP.put("审核中", "card.status.pending");
        I18N_KEY_MAP.put("生效中", "card.status.active");
        I18N_KEY_MAP.put("已禁用", "card.status.disable");
        I18N_KEY_MAP.put("挂失", "card.status.loss");
        I18N_KEY_MAP.put("被盗", "card.status.stolen");
        I18N_KEY_MAP.put("创建失败", "card.status.failed");
        I18N_KEY_MAP.put("已注销", "card.status.logout");
        I18N_KEY_MAP.put("冻结", "card.status.freeze");
        I18N_KEY_MAP.put("审核拒绝", "card.status.refuse");
        I18N_KEY_MAP.put("已过期", "card.status.expired");
    }


    public static CardCanOperationDTO getCarCanOperateStatus(Integer status){
        CardCanOperationDTO req = new CardCanOperationDTO();
        for (CardStatusEnum item : values()) {
            if (item.getStatus() == status) {
                req.setCheckFlag(item.checkFlag);
                req.setDeleteFlag(item.deleteFlag);
                req.setDetailFlag(item.detailFlag);
                req.setFreezeFlag(item.freezeFlag);
                req.setLogoutFlag(item.logoutFlag);
                req.setOpenFlag(item.openFlag);
                req.setUpdateFlag(item.updateFlag);
                req.setType(item.type);
                req.setStatusStr(item.getName());
                return req;
            }
        }
        return req;
    }


    public static CardStatusEnum getAirWallexEnum(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (CardStatusEnum item : values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item;
            }
        }
        return null;
    }

    public static CardStatusEnum getLianLianCodeEnum(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (CardStatusEnum item : values()) {
            if (item.getLianLianCode().equalsIgnoreCase(code)) {
                return item;
            }
        }
        return null;
    }

    public static CardStatusEnum getLianLianCodeEnumByShowStatus(Integer showStatus){
        if(ObjUtils.isEmpty(showStatus)){
            return null;
        }
        List<CardStatusEnum> canLianLianDealStatus = canLianLianDealStatus();
        for (CardStatusEnum item : values()) {
            if (item.getShowStatus().equals(showStatus)) {
                return item;
            }
        }
        return null;
    }


    /**
     * 请求连连接口支持的状态
     * @return
     */
    public static List<CardStatusEnum> canLianLianDealStatus(){
        List<CardStatusEnum> canLianLianDealStatus = new ArrayList<>();
        canLianLianDealStatus.add(PENDING);
        canLianLianDealStatus.add(ACTIVE);
        canLianLianDealStatus.add(LOGOUT);
        canLianLianDealStatus.add(FREEZE);
        canLianLianDealStatus.add(EXPIRED);
        return canLianLianDealStatus;
    }


    /**
     * 是否是连连接口支持的状态
     * @param showStatus
     * @return
     */
    public static boolean isLianLianDealStatus(Integer showStatus){
        if(ObjUtils.isEmpty(showStatus)){
            return false;
        }
        List<CardStatusEnum> canLianLianDealStatus = canLianLianDealStatus();
        for (CardStatusEnum item : values()) {
            if (item.getShowStatus().equals(showStatus)) {
                return true;
            }
        }
        return false;
    }





    public static CardStatusEnum getPlatformCodeEnum(String platform,String code){
        if(StringUtils.isBlank(platform)||StringUtils.isBlank(code)){
            return null;
        }
        if(CardPlatformEnum.isLianLian(code)){
            return getLianLianCodeEnum(code);
        }
        if(CardPlatformEnum.isAirwallex(code)){
            return getAirWallexEnum(code);
        }
        return null;
    }




    public static List<Integer> getApplyStatus(Integer status){
        List<Integer> statusList = new ArrayList<>();
        if (CardShowStatusEnum.ACTIVE.getStatus() == status){
            statusList.add(ACTIVE.getStatus());
        }else if (Objects.equals(status, CardApplyStatusEnum.WAIT_ACTIVE.getCardStatus())){
            statusList.add(WAIT_ACTIVE.getStatus());
        }else {
            statusList.add(DISABLE.getStatus());
            statusList.add(LOSS.getStatus());
            statusList.add(STOLEN.getStatus());
            statusList.add(LOGOUT.getStatus());
            statusList.add(FREEZE.getStatus());
            statusList.add(EXPIRED.getStatus());
        }
        return statusList;
    }

    public static Integer getShowStatus(Integer status){
        for (CardStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item.getShowStatus();
            }
        }

        return null;
    }

    public static List<Integer> chargingStatus = Lists.newArrayList(ACTIVE.getStatus(),DISABLE.getStatus(),FREEZE.getStatus());
    public static Boolean validContinueCharging(Integer status) {
        return chargingStatus.contains(status);
    }

    public static String getShowStatusStr(Integer status){
        for (CardStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item.getName();
            }
        }

        return null;
    }

    public static CardStatusEnum getEnumByStatus(Integer status){
        for (CardStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item;
            }
        }
        return null;
    }


    private  Integer status;

    private Integer showStatus;

    //AwCode
    private String code;


    //连连 Code LianLianCardStatusEnum
    private String lianLianCode;

    private  String name;

    private Integer type;

    private  boolean freezeFlag;

    private  boolean openFlag;

    private  boolean updateFlag;

    private  boolean checkFlag;

    private  boolean logoutFlag;

    private  boolean deleteFlag;

    private  boolean detailFlag;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isFreezeFlag() {
        return freezeFlag;
    }

    public void setFreezeFlag(boolean freezeFlag) {
        this.freezeFlag = freezeFlag;
    }

    public boolean isOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(boolean openFlag) {
        this.openFlag = openFlag;
    }

    public boolean isUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(boolean updateFlag) {
        this.updateFlag = updateFlag;
    }

    public boolean isCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(boolean checkFlag) {
        this.checkFlag = checkFlag;
    }

    public boolean isLogoutFlag() {
        return logoutFlag;
    }

    public void setLogoutFlag(boolean logoutFlag) {
        this.logoutFlag = logoutFlag;
    }

    public boolean isDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public boolean isDetailFlag() {
        return detailFlag;
    }

    public void setDetailFlag(boolean detailFlag) {
        this.detailFlag = detailFlag;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }

    public String getLianLianCode() {
        return lianLianCode;
    }

    public void setLianLianCode(String lianLianCode) {
        this.lianLianCode = lianLianCode;
    }
}
