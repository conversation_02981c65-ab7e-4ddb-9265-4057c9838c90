package com.fenbei.fx.card.service.redis;

import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.EncodeUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPubSub;

import javax.annotation.Resource;
import java.io.*;
import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * User: zhangzhijun
 * Date: 2017/5/12 下午6:21
 * Desc:
 */
@Component
public class RedisDao {

    private static final Logger logger = LoggerFactory.getLogger(RedisDao.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Resource(name = "redisTemplate")
    private ValueOperations<String, String> valueOperations;

    /**
     * 删除key
     *
     * @param key
     */
    public void deleteKey(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 根据key获取值
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    /**
     * 将值放入缓存并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) -1为无期限
     * @return true成功 false 失败
     */
    public void set(String key, String value, long time) {
        if (time > 0) {
            redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(key, value);
        }
    }


    /**
     * 批量删除匹配的keys值
     */
    public void deleteBatchKey(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 获取redis操作模板类
     *
     * @return
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

    /**
     * value结构操作
     *
     * @return
     */
    public ValueOperations<String, String> getValueOperations() {
        return valueOperations;
    }

    /**
     * hash结构操作
     *
     * @return
     */
    public HashOperations<String, String, Object> getHashOperations() {
        return redisTemplate.opsForHash();
    }

    /**
     * list结构操作
     *
     * @return
     */
    public ListOperations<String, Object> getListOperations() {
        return redisTemplate.opsForList();
    }

    /**
     * set结构操作
     *
     * @return
     */
    public SetOperations<String, Object> getSetOperations() {
        return redisTemplate.opsForSet();
    }

    /**
     * zset结构操作
     *
     * @return
     */
    public ZSetOperations<String, Object> getZSetOperations() {
        return redisTemplate.opsForZSet();
    }

    /**
     * 转化成 hyperloop（play框架）中redis存储的字符串
     * 格式为：string-Base64(value)
     *
     * @param value
     * @return
     */
    public String toPlayRedisString(String value) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            DataOutputStream dos = new DataOutputStream(baos);
            dos.writeUTF(value);
            try {
                return "string-" + EncodeUtils.encodeBase64(baos.toByteArray());
            } finally {
                dos.close();
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return value;
        }
    }

    /**
     * 获取 hyperloop（play框架）中redis存储的字符串
     * 格式为：string-Base64(value)
     *
     * @param value
     * @return
     */
    public String getPlayRedisString(String value) {
        try {
            String[] valueAry = StringUtils.split(value, "-");
            if (valueAry == null
                || valueAry.length != 2
                || ObjUtils.isEmpty(valueAry[1])) {
                return value;
            }
            byte[] bytes = EncodeUtils.decodeBase64_byte(valueAry[1]);
            DataInputStream dis = new DataInputStream(new ByteArrayInputStream(bytes));
            try {
                return dis.readUTF();
            } finally {
                dis.close();
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
            return value;
        }
    }


    /**
     * 发布消息到指定的频道
     * 并且插入队列数据
     *
     * @param key
     * @param value
     * @param message
     */
    public void pushAndPublish(final String key, Object value, final String message) {
        pushList(key, value);
        publish(key, message);
    }

    /**
     * 发布消息到指定的频道
     * 并且插入队列数据
     *
     * @param key
     * @param value
     * @param message
     */
    public void pushAllAndPublish(final String key, Collection value, final String message) {
        pushListAll(key, value);
        publish(key, message);
    }


    /**
     * 发布消息到指定的频道
     *
     * @param channel
     * @param message
     */
    public void publish(final String channel, final String message) {
        redisTemplate.execute(new RedisCallback<T>() {
            @Override
            public T doInRedis(final RedisConnection connection)
                throws DataAccessException {
                ((Jedis) connection.getNativeConnection()).publish(channel, message);
                return null;
            }
        });
    }

    /**
     * 订阅给定的一个或多个频道的信息
     *
     * @param jedisPubSub
     * @param channels
     */
    public void subscribe(final JedisPubSub jedisPubSub, final String... channels) {
        redisTemplate.execute(new RedisCallback<T>() {
            @Override
            public T doInRedis(RedisConnection connection) throws DataAccessException {
                ((Jedis) connection.getNativeConnection()).subscribe(jedisPubSub, channels);
                return null;
            }
        });
    }

    /**
     * 压入队列 先进先出
     *
     * @param key
     * @param val
     */
    public void pushList(String key, Object val) {
        redisTemplate.opsForList().leftPush(key, val);
    }

    /**
     * 压入队列 先进先出
     *
     * @param key
     * @param val
     */
    public void pushListAll(String key, Object... val) {
        redisTemplate.opsForList().leftPushAll(key, val);
    }

    /**
     * 压入队列 先进先出
     *
     * @param key
     * @param val
     */
    public void pushListAll(String key, Collection val) {
        redisTemplate.opsForList().leftPushAll(key, val);
    }

    /**
     * 删除队列
     *
     * @param key
     */
    public void deleteListAll(String key) {
        redisTemplate.opsForList().getOperations().delete(key);
    }

    /**
     * 队列取出 先进先出
     *
     * @param key
     * @return
     */
    public Object popList(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }


    /**
     * 获取过期时间
     *
     * @param key
     * @return
     */
    public Long getExpire(String key, TimeUnit timeUnit) {
        return redisTemplate.getExpire(key, timeUnit);
    }

    /**
     * 获取过期时间
     *
     * @param key
     * @return
     */
    public Boolean setExpire(String key, long time, TimeUnit timeUnit) {
        return redisTemplate.expire(key, time, timeUnit);
    }
}
