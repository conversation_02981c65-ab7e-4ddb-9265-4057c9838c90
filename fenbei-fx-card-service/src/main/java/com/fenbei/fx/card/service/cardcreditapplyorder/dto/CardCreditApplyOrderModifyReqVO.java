package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import com.fenbei.fx.card.api.card.dto.EmployeeBankInfoReq;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 国际卡额度发放单 修改 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderModifyReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 单据主键ID
     */
    private String applyOrderId;

    /**
     * 单据编号
     */
    private String meaningNo;

    /**
     * 审批单类型  40海外卡发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 43:海外卡发放单(小类别)
     */
    private Integer applyOrderSubType;

    /**
     * 1: 普通模式
        2: 备用金模式;
     */
    private Integer activeModel;

    /**
     * 标题
     */
    private String title;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 申请总金额(单位：元)
     */
    private BigDecimal applyAmount;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请事由id
     */
    private Integer applyReasonId;

    private List<EmployeeBankInfoReq> userMap;


    /**
     * 制单人ID
     */
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;

    /**
     * 申请人id
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人直属部门ID
     */
    private String applicantOrgId;

    /**
     * 申请人直属部门名称
     */
    private String applicantOrgName;

    /**
     * 发放人ID
     */
    private String issuedId;

    /**
     * 发放人名称
     */
    private String issuedName;

    /**
     * 费用类别id
     */
    private String costCategoryId;
    /**
     * 费用类别名称
     */
    private String costCategoryName;

    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;


    /**
     * 费用归属
     */
    private CostInfo costInfo;

    /**
     * 0:费用为其它； 1：费用为员工所属部门
     */
    private boolean employeeDept;

}
