package com.fenbei.fx.card.service.cardauthorize.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardauthorize.po.CardAuthorizePO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import com.fenbei.fx.card.service.cardauthorize.converter.CardAuthorizeConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡授权表 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardAuthorizeDO extends BaseDO<CardAuthorizeDTO, CardAuthorizePO, CardAuthorizeConverter> {

    public static CardAuthorizeDO me() {
        return SpringUtil.getBean(CardAuthorizeDO.class);
    }

    public void checkCardAuthorizeAddReqDTO(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        if (Func.isEmpty(cardAuthorizeAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardAuthorizeAddReqDTOList(final List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList) {
        if (Func.isEmpty(cardAuthorizeAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardAuthorizeModifyReqDTO(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        if (Func.isEmpty(cardAuthorizeModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardAuthorizeRemoveReqDTO(final CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO) {
        if (Func.isEmpty(cardAuthorizeRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardAuthorizeDTO buildListParamsDTO(final CardAuthorizeListReqDTO cardAuthorizeListReqDTO) {
        return converter.convertToCardAuthorizeDTO(cardAuthorizeListReqDTO);
    }

    public CardAuthorizeDTO buildPageParamsDTO(final CardAuthorizePageReqDTO cardAuthorizePageReqDTO) {
        return converter.convertToCardAuthorizeDTO(cardAuthorizePageReqDTO);
    }

    public CardAuthorizeDTO buildAddCardAuthorizeDTO(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        return converter.convertToCardAuthorizeDTO(cardAuthorizeAddReqDTO);
    }

    public List<CardAuthorizeDTO> buildAddBatchCardAuthorizeDTOList(final List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList) {
        return converter.convertToCardAuthorizeDTOList(cardAuthorizeAddReqDTOList);
    }

    public CardAuthorizeDTO buildModifyCardAuthorizeDTO(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        return converter.convertToCardAuthorizeDTO(cardAuthorizeModifyReqDTO);
    }

    public CardAuthorizeDTO buildRemoveCardAuthorizeDTO(final CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO) {
        return converter.convertToCardAuthorizeDTO(cardAuthorizeRemoveReqDTO);
    }

    public List<CardAuthorizeListResDTO> transferCardAuthorizeListResDTOList(final List<CardAuthorizeDTO> cardAuthorizeDTOList) {
        if (Func.isEmpty(cardAuthorizeDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardAuthorizeListResDTOList(cardAuthorizeDTOList);
    }

    public CardAuthorizeListResDTO transferCardAuthorizeListResDTO(final CardAuthorizeDTO cardAuthorizeDTO) {
        if (Func.isEmpty(cardAuthorizeDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardAuthorizeListResDTO(cardAuthorizeDTO);
    }

    public Page<CardAuthorizePageResDTO> transferCardAuthorizePageResDTOPage(final Page<CardAuthorizeDTO> cardAuthorizeDTOPage) {
        if (Func.isEmpty(cardAuthorizeDTOPage) || Func.isEmpty(cardAuthorizeDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardAuthorizePageResDTOPage(cardAuthorizeDTOPage);
    }

    public CardAuthorizeShowResDTO transferCardAuthorizeShowResDTO(final CardAuthorizeDTO cardAuthorizeDTO) {
        if (Func.isEmpty(cardAuthorizeDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardAuthorizeShowResDTO(cardAuthorizeDTO);
    }

    public List<CardAuthorizeShowResDTO> transferCardAuthorizeShowResDTOList(final List<CardAuthorizeDTO> cardAuthorizeDTOList) {
        if (Func.isEmpty(cardAuthorizeDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardAuthorizeShowResDTOList(cardAuthorizeDTOList);
    }
}
