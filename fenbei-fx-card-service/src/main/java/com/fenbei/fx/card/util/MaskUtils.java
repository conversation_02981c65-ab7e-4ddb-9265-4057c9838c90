package com.fenbei.fx.card.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.CollectionUtils;

import java.beans.PropertyDescriptor;
import java.util.*;

/**
 * 海外卡号蒙面
 */
public class MaskUtils {
    /**
     * 卡号大于4位，后四位不蒙面
     * 小于等于4位全蒙面
     * @param cardNum 卡号
     * @param len 剩余长度
     * @return
     */
    public static String leftData(String cardNum, int len) {
        if (StringUtils.isBlank(cardNum)) {
            return "";
        }
        if(cardNum.length()<=len){
            return other4(cardNum);
        }
        return StringUtils.leftPad(StringUtils.right(cardNum, len), StringUtils.length(cardNum), "*");
    }

    public static String other4(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        int len = 0;
        String strStart = StringUtils.left(str, len);
        return StringUtils.rightPad(strStart, StringUtils.length(str), "*");
    }

    public static void main(String[] args) {
        String cardNum="1234567891111";
        leftData(cardNum,5);
    }

}
