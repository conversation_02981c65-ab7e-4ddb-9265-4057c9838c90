package com.fenbei.fx.card.service.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.service.webservice.HyperloopHttpService;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.service.webservice.dto.CommonPushDto;
import com.fenbei.fx.card.service.webservice.dto.PushAlertDto;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.eventbus.util.EventBus;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.eventbus.event.common.IEvent;
import com.luastar.swift.base.net.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 推送服务
 **/
@Slf4j
@Service
public class MessageServiceImpl<T> implements MessageService<T> {


    @Autowired
    private HyperloopHttpService hyperloopHttpService;

    @Value("${host.harmony}")
    private String harmonyHost;

    /**
     * 推送到友盟
     *
     * @param dto
     */
    @Override
    public void pushAlertMsg(PushAlertDto dto) throws IOException {
        FinhubLogger.info("发送友盟信息：{}", dto);
        Object o = hyperloopHttpService.push(dto).execute();
        FinhubLogger.info(o.toString());
    }
    /**
     * 推送Event,到kafka
     *
     * @param iEvent
     */
    @Override
    public void pushEventMsg(IEvent iEvent) {
        try {
            FinhubLogger.info("发送event消息：{}", iEvent);
            EventBus.publish(iEvent);
        } catch (Exception e) {
            FinhubLogger.error("pushEventMsg Exception", e);
        }
    }


    @Override
    public void pushSMS(SmsContract msgBody) throws IOException {
        FinhubLogger.info("发送短信信息：{}", msgBody.toString());
        HttpClientUtils.postBody(harmonyHost + "/v1/sms", JSONObject.toJSONString(msgBody));
    }

    @Override
    public boolean pushMsgAll(T entity, CommonPushDto pushDTO) {
        if (pushDTO.isPushAlert()) {
            PushAlertDto pushAlertDto = pushDTO.getPushAlertDto();
            try {
                pushAlertMsg(pushAlertDto);
            } catch (IOException e) {
                FinhubLogger.error("pushAlertMsg Error", e);
            }
        }

        if (pushDTO.isPushEvent()) {
            pushEventMsg(pushDTO.getIEvent());
        }
        if (pushDTO.isPushSms()) {
            try {
                pushSMS(pushDTO.getMsgBody());
            } catch (IOException e) {
                FinhubLogger.error("pushSMS Error", e);
            }
        }
        return true;
    }

    /**
     * 发送邮件
     *
     * @param emailContract
     */
    @Override
    public void sendEmail(EmailContract emailContract) {
        CheckUtils.checkNull(emailContract, "发送邮件参数不能为空!");
        CheckUtils.checkEmpty(emailContract.getToList(), "收件箱不能为空！");
        try {
            String mailUrl = harmonyHost + "/harmony/mail";
            Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("customerId", Optional.ofNullable(emailContract.getCustomerId()).orElse("saas"));
            bodyMap.put("serverId", emailContract.getServerId());
            bodyMap.put("toList", emailContract.getToList());
            bodyMap.put("ccList", emailContract.getCcList());
            bodyMap.put("bccList", emailContract.getBccList());
            bodyMap.put("subject", emailContract.getSubject());
            bodyMap.put("text", emailContract.getText());
            if (StringUtils.isNotEmpty(emailContract.getTemplateId())) {
                Map<String, Object> htmlMap = new LinkedHashMap<>();
                htmlMap.put("templateId", emailContract.getTemplateId());
                htmlMap.put("data", emailContract.getData());
                bodyMap.put("html", htmlMap);
            }
            String emailBody = JSON.toJSONString(bodyMap);
            FinhubLogger.info("发送邮件url:{},内容：{}",mailUrl, emailBody);
            String result = HttpClientUtils.postBody(mailUrl, emailBody);
            FinhubLogger.info("发送邮件url:{},结果：{}",mailUrl,  result);
        } catch (Exception e) {
            FinhubLogger.error("发送邮件异常：" + e.getMessage(), e);
        }
    }

}
