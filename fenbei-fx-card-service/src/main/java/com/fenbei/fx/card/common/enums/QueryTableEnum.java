package com.fenbei.fx.card.common.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum QueryTableEnum {
    CARD_TABLE("card","卡表"),
    CARD_APPLY_TABLE  ("cardApply","卡申请表"),

    ;


    private String table;

    private String name;


    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
