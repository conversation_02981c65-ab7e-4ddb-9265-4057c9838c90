package com.fenbei.fx.card.common.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.i18n.SimpleLocaleContext;
import org.springframework.util.StringUtils;

import java.util.Locale;

@Slf4j
@Activate(group = {CommonConstants.PROVIDER}, order = 1)
public class DubboProviderMessageMLSFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String lang = RpcContext.getContext().getAttachment("lang");
        String fromLang = RpcContext.getContext().getAttachment("from_lang");
        //log.info("lang is {}", lang);
        Locale locale;
        if (StringUtils.hasLength(lang) || StringUtils.hasLength(fromLang)) {
            if ("en_US".equals(lang) || "en_us".equals(lang)||"en_US".equals(fromLang) || "en_us".equals(fromLang)) {
                locale = Locale.US;
            } else if ("zh_CN".equals(lang) || "zh_cn".equals(lang) || "zh_CN".equals(fromLang) || "zh_cn".equals(fromLang)) {
                locale = Locale.SIMPLIFIED_CHINESE;
            } else {
                locale = new Locale(lang);
            }
        } else {
            locale = Locale.SIMPLIFIED_CHINESE;
        }
        //log.info("locale is {}", locale);
        try {
            LocaleContextHolder.setLocaleContext(locale == null ? new SimpleLocaleContext(Locale.SIMPLIFIED_CHINESE) : new SimpleLocaleContext(locale), true);
        } catch (IllegalArgumentException ex) {
            log.debug("LocaleContextHolder.setLocaleContext [" + locale.getDisplayName() + "]: " + ex.getMessage());
        }
        Result result = invoker.invoke(invocation);
        LocaleContextHolder.resetLocaleContext();
        //log.info("移除线程语言环境ThreadLocal");
        return result;
    }
}
