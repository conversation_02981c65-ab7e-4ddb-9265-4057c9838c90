package com.fenbei.fx.card.service.cardwrongpaidflow.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 错花还款流水表 列表 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardWrongPaidFlowListResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private Long id;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 交易单号
     */
    private String bizNo;

    /**
     * 关联的交易单号
     */
    private String orderBizNo;

    /**
     * 币种
     */
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
