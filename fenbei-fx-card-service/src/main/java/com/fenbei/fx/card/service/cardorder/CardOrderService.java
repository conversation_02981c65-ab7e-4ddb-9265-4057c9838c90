package com.fenbei.fx.card.service.cardorder;

import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.usercard.dto.UserCardTradeInfosDTO;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;

import java.math.BigDecimal;
import java.util.List;

/**
 * 国际卡订单 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
public interface CardOrderService extends BaseService<CardOrderDTO> {

    static CardOrderService me() {
        return SpringUtil.getBean(CardOrderService.class);
    }

    /**
     * 列表
     *
     * @param cardOrderListReqDTO 入参DTO
     * @return
     */
    List<CardOrderListResDTO> list(CardOrderListReqDTO cardOrderListReqDTO);

    /**
     * First查询
     *
     * @param cardOrderListReqDTO 入参DTO
     * @return
     */
    CardOrderListResDTO listOne(CardOrderListReqDTO cardOrderListReqDTO);

    /**
     * Web 分页
     *
     * @param cardTradeInfoWebPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardTradeInfoWebPageResDTO> pagination(CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO, Integer current, Integer size);

    /**
     * App 分页
     *
     * @param cardTradeInfoAppPageReqDTO 入参DTO
     * @param current 当前页
     * @param size 每页大小
     * @return
     */
    Page<CardTradeInfoAppPageResDTO> pagination(CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO, int current, int size);

    /**
     * 新增
     *
     * @param cardOrderAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardOrderAddReqDTO cardOrderAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardOrderAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardOrderAddReqDTO cardOrderAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardOrderAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardOrderAddReqDTO> cardOrderAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardOrderShowResDTO show(String id);

    /**
     * 交易记录 App 详情
     *
     * @param bizNo 交易单号
     * @param transactionType 交易类型
     * @return
     */
    CardTradeInfoAppShowResDTO appShow(String bizNo, Integer transactionType);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardOrderShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardOrderModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardOrderModifyReqDTO cardOrderModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardOrderModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardOrderModifyReqDTO cardOrderModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardOrderRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardOrderRemoveReqDTO cardOrderRemoveReqDTO);

    /**
     * 根据卡信息查询最近交易
     * @param fxCardId 卡ID
     * @return UserCardTradeInfosDTO
     */
    UserCardTradeInfosDTO latestTradeInfo(String fxCardId);

    CardOrderDTO findByTradeId(String bizNo, Integer tradeType);


    List<CardOrderDTO> findListByTradeId(String tradeId, Integer tradeType);

    CardOrderDTO findByTradeIdAndSub(String tradeId, String subTradeId, Integer tradeType);

    /**
     * 根据卡信息查询最近交易
     * @param employeeId 卡ID
     * @return UserCardTradeInfosDTO
     */
    UserCardTradeInfosDTO latestTradeInfoByEmployeeId(String employeeId);

    Boolean appRemark(CardTradeInfoAppRemarkReqDTO cardTradeInfoAppRemarkReqDTO);

    /**
     * 核销设置费用绑定状态
     * @param bizNos
     */
    void costBind(List<String> bizNos , Integer costId);

    /**
     * 核销解除费用绑定状态
     * @param bizNos
     */
    void costUnBind(List<String> bizNos, Integer costId );

    /***
     * 审核同步核销中金额
     * @param initDTO
     * @return
     */
    boolean applyInit(FxVerificationReqDTO initDTO);

    /***
     * 审核拒绝/撤回  回滚金额
     * @param delDTO
     */
    public boolean applyDel(FxVerificationReqDTO delDTO);

    /***
     * 审核撤回
     * @param delDTO
     * @return
     */
    public boolean applyDisCardDel(FxVerificationReqDTO delDTO);

    /***
     * 审核通过
     * @param doneDTO
     * @return
     */
    public boolean applyDoneNew(FxVerificationReqDTO doneDTO);

    /**
     * 汇率调整
     * @param id
     * @param rate
     */
    void updateExchangeRate(String id, BigDecimal rate);


    /**
     * 汇率调整
     * @param id id
     */
    void updateOrderShow(String id);

    void updateHistoryValue(String id);
}
