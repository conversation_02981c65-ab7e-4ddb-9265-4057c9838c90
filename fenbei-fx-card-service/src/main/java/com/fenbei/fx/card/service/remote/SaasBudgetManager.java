package com.fenbei.fx.card.service.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyReqDTO;
import com.fenbei.fx.card.service.remote.dto.SaasBugetDto;
import com.fenbei.fx.card.util.ValidateCommonUtils;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saasplus.api.model.dto.apply.RpcCommonResult;
import com.fenbeitong.saasplus.api.model.dto.budget.ApplyOrderReturnAmount;
import com.fenbeitong.saasplus.api.model.dto.finance.BatchDeductAndReleaseReq;
import com.fenbeitong.saasplus.api.model.dto.finance.BatchReturnReq;
import com.fenbeitong.saasplus.api.service.finance.IOrderCostService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Component
public class SaasBudgetManager {

    @Value("${api.saas.host}")
    public String saasHost;


    @DubboReference
    protected IOrderCostService iOrderCostService ;


    /**
     * 扣除预算
     * @param  cardCreditApplyRpcReqDTO 额度申请参数
     * @param bizNo 业务单号
     */
    public void budgetConsume(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO, String bizNo) {
        FinhubLogger.debug("【扣SaaS管控企业预算金额参数】{}", JsonUtils.toJson(cardCreditApplyRpcReqDTO));
        //调用管控预算接口，先扣管控，再扣企业
        if (BigDecimalUtils.hasNoPrice(cardCreditApplyRpcReqDTO.getApplyCreditAmount())) {
            throw new FxCardException(GlobalCoreResponseCode.BUDGET_ERROR);
        }
        SaasBugetDto budgetDto = SaasBugetDto.builder()
            .companyId(cardCreditApplyRpcReqDTO.getCompanyId())
            .employeeId(cardCreditApplyRpcReqDTO.getEmployeeId())
            .orderAmount(BigDecimalUtils.fen2yuan(cardCreditApplyRpcReqDTO.getApplyCreditAmount()))
            .bizNo(bizNo)
            .orderType(CategoryTypeEnum.BANK_INDIVIDUAL.getCode())
            .costAttributionCategory(cardCreditApplyRpcReqDTO.getAttributions().get(0).getCost_attribution_category())
            .costAttributionId(cardCreditApplyRpcReqDTO.getAttributions().get(0).getCost_attribution_id())
            .costAttributionTime(DateUtils.format(new Date()))
            .costAttributionList(cardCreditApplyRpcReqDTO.getAttributions())
            .budgetCostAttrType(cardCreditApplyRpcReqDTO.getBudgetOpt())
            .costAttributionScope(cardCreditApplyRpcReqDTO.getCostAttributionOpt())
            .build();
        saasBudget(budgetDto);
    }


    /**
     * :调用接口saas实现,主要为虚拟卡额度申请和退还使用
     * @param  saasBugetDto 预算请求参数
     */
    public void saasBudget(SaasBugetDto saasBugetDto) {
        ValidateCommonUtils.validate(saasBugetDto);
        String requestBody = JsonUtils.toJson(saasBugetDto);
        FinhubLogger.info("【分贝通虚拟卡】【修改管控预算参数】：{}", requestBody);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        String url = saasHost + "/budget/consume/v2/save";
        String result = HttpClientUtils.postBody(url, requestBody, headerMap);
        FinhubLogger.info("【分贝通虚拟卡】 requestBody:{},【修改管控预算返回结果】：{}", requestBody, result);
        if (ObjUtils.isEmpty(result)) {
            throw new FxCardException(GlobalCoreResponseCode.BUDGET_ERROR);
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object code = jsonObject.get("code");
        if (ObjUtils.isEmpty(code) || 0 != Integer.parseInt(code.toString())) {
            throw new FxCardException(GlobalCoreResponseCode.BUDGET_ERROR);
        }
    }


    /**
     * 新预算扣减预算
     * @param  list 单据上实际需要占用的预算金额
     */

    public void toBatchBudgetDeductNew(List<BatchDeductAndReleaseReq> list) {
        FinhubLogger.info("新预算模式batchDeductAndRelease()--list:{}", JSON.toJSONString(list));
        try {
            if (ObjUtils.isEmpty(list)) {
                return;
            }
            //预算是先预占用，后实占用，然后业务有需求在审批中修改单据金额，这时候需要 实占比预占减少部分金额，需释放掉预算。
            RpcCommonResult rpcCommonResult = iOrderCostService.batchDeductAndRelease(list);
            FinhubLogger.info("新预算模式batchDeductAndRelease()--list:{},result:{}", JSON.toJSONString(list),JSON.toJSONString(rpcCommonResult));
        } catch (Exception e) {
            FinhubLogger.error("新预算模式异常", e);
        }

    }

    public void toBatchBudgetReturnNew(List<BatchReturnReq> batchReturnReqs) {
        FinhubLogger.info("新预算模式toBatchBudgetReturnNew()--list:{}", JSON.toJSONString(batchReturnReqs));
        try {
            if (ObjUtils.isEmpty(batchReturnReqs)) {
                return;
            }
            RpcCommonResult rpcCommonResult = iOrderCostService.batchReturn(batchReturnReqs);
            FinhubLogger.info("新预算模式batchDeductAndRelease()--list:{},result:{}", JSON.toJSONString(batchReturnReqs),JSON.toJSONString(rpcCommonResult));
        } catch (Exception e) {
            FinhubLogger.error("新预算模式异常", e);
        }

    }



}
