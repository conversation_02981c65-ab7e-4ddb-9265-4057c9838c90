package com.fenbei.fx.card.service.cardwrongpaidflow.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardwrongpaidflow.po.CardWrongPaidFlowPO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowAddReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 错花还款流水表 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardWrongPaidFlowConverter extends BaseConverter<CardWrongPaidFlowDTO, CardWrongPaidFlowPO> {

    static CardWrongPaidFlowConverter me() {
        return SpringUtil.getBean(CardWrongPaidFlowConverter.class);
    }

    CardWrongPaidFlowDTO convertToCardWrongPaidFlowDTO(CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO);

    CardWrongPaidFlowDTO convertToCardWrongPaidFlowDTO(CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO);

    CardWrongPaidFlowDTO convertToCardWrongPaidFlowDTO(CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO);

    CardWrongPaidFlowDTO convertToCardWrongPaidFlowDTO(CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO);

    CardWrongPaidFlowDTO convertToCardWrongPaidFlowDTO(CardWrongPaidFlowPageReqDTO cardWrongPaidFlowPageReqDTO);

    CardWrongPaidFlowShowResDTO convertToCardWrongPaidFlowShowResDTO(CardWrongPaidFlowDTO cardWrongPaidFlowDTO);

    List<CardWrongPaidFlowShowResDTO> convertToCardWrongPaidFlowShowResDTOList(List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList);

    CardWrongPaidFlowListResDTO convertToCardWrongPaidFlowListResDTO(CardWrongPaidFlowDTO cardWrongPaidFlowDTO);

    List<CardWrongPaidFlowListResDTO> convertToCardWrongPaidFlowListResDTOList(List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList);

    List<CardWrongPaidFlowDTO> convertToCardWrongPaidFlowDTOList(List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList);

    CardWrongPaidFlowPageResDTO convertToCardWrongPaidFlowPageResDTO(CardWrongPaidFlowDTO cardWrongPaidFlowDTO);

    List<CardWrongPaidFlowPageResDTO> convertToCardWrongPaidFlowPageResDTOList(List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList);

    Page<CardWrongPaidFlowPageResDTO> convertToCardWrongPaidFlowPageResDTOPage(Page<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOPage);
}
