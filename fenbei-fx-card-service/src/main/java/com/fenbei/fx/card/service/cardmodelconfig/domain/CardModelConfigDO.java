package com.fenbei.fx.card.service.cardmodelconfig.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmodelconfig.po.CardModelConfigPO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigShowResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.converter.CardModelConfigConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡使用模式配置 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardModelConfigDO extends BaseDO<CardModelConfigDTO, CardModelConfigPO, CardModelConfigConverter> {

    public static CardModelConfigDO me() {
        return SpringUtil.getBean(CardModelConfigDO.class);
    }

    public void checkCardModelConfigAddReqDTO(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        if (Func.isEmpty(cardModelConfigAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardModelConfigAddReqDTOList(final List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList) {
        if (Func.isEmpty(cardModelConfigAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardModelConfigModifyReqDTO(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        if (Func.isEmpty(cardModelConfigModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardModelConfigRemoveReqDTO(final CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO) {
        if (Func.isEmpty(cardModelConfigRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardModelConfigDTO buildListParamsDTO(final CardModelConfigListReqDTO cardModelConfigListReqDTO) {
        return converter.convertToCardModelConfigDTO(cardModelConfigListReqDTO);
    }

    public CardModelConfigDTO buildPageParamsDTO(final CardModelConfigPageReqDTO cardModelConfigPageReqDTO) {
        return converter.convertToCardModelConfigDTO(cardModelConfigPageReqDTO);
    }

    public CardModelConfigDTO buildAddCardModelConfigDTO(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        return converter.convertToCardModelConfigDTO(cardModelConfigAddReqDTO);
    }

    public List<CardModelConfigDTO> buildAddBatchCardModelConfigDTOList(final List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList) {
        return converter.convertToCardModelConfigDTOList(cardModelConfigAddReqDTOList);
    }

    public CardModelConfigDTO buildModifyCardModelConfigDTO(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        return converter.convertToCardModelConfigDTO(cardModelConfigModifyReqDTO);
    }

    public CardModelConfigDTO buildRemoveCardModelConfigDTO(final CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO) {
        return converter.convertToCardModelConfigDTO(cardModelConfigRemoveReqDTO);
    }

    public List<CardModelConfigListResDTO> transferCardModelConfigListResDTOList(final List<CardModelConfigDTO> cardModelConfigDTOList) {
        if (Func.isEmpty(cardModelConfigDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardModelConfigListResDTOList(cardModelConfigDTOList);
    }

    public CardModelConfigListResDTO transferCardModelConfigListResDTO(final CardModelConfigDTO cardModelConfigDTO) {

        return converter.convertToCardModelConfigListResDTO(cardModelConfigDTO);
    }

    public Page<CardModelConfigPageResDTO> transferCardModelConfigPageResDTOPage(final Page<CardModelConfigDTO> cardModelConfigDTOPage) {
        if (Func.isEmpty(cardModelConfigDTOPage) || Func.isEmpty(cardModelConfigDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardModelConfigPageResDTOPage(cardModelConfigDTOPage);
    }

    public CardModelConfigShowResDTO transferCardModelConfigShowResDTO(final CardModelConfigDTO cardModelConfigDTO) {
        if (Func.isEmpty(cardModelConfigDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardModelConfigShowResDTO(cardModelConfigDTO);
    }

    public List<CardModelConfigShowResDTO> transferCardModelConfigShowResDTOList(final List<CardModelConfigDTO> cardModelConfigDTOList) {
        if (Func.isEmpty(cardModelConfigDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardModelConfigShowResDTOList(cardModelConfigDTOList);
    }
}
