package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import com.fenbei.fx.card.api.card.dto.EmployeeBankInfoReq;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 国际卡额度发放单 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderAddReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 公司ID
     */
    @NotBlank
    private String companyId;

    private String applyOrderId;

    /**
     * 申请单ID编号
     */
    private String applyId;

    /**
     * 单据编号
     */
    private String meaningNo;

    /**
     * 审批单类型  40虚拟卡发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 40:虚拟卡发放单(小类别)
     */
    private Integer applyOrderSubType;

    /**
     * 1: 普通模式
     * 2: 备用金模式
     */
    private Integer activeModel;

    /**
     * 标题
     */
    private String title;

    /**
     * 申请总金额(单位：元)
     */
    @NotNull
    @Min(0)
    private BigDecimal applyAmount;

    /**
     * 币种
     */
    private String currency ;


    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请事由id
     */
    private Integer applyReasonId;

    /**
     * 发放状态 1.待发放 2.已发放 3.发放失败 4.发放中
     */
    private Integer applyState;

    /**
     * 申请人id
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;


    private List<EmployeeBankInfoReq> userMap;

    /**
     * true: 员工所属部门 false: 不是
     */
    private boolean employeeDept;

    /**
     * 制单人ID
     */
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;



    /**
     * 发放人ID
     */
    private String issuedId;

    /**
     * 发放人名称
     */
    private String issuedName;




    //自定义档案类型ID
    private String recordId;
    //自定义档案类型名称
    private String recordName;

    private Integer deductionMode;


    /**
     * 费用类别id
     */
    private String costCategoryId;
    /**
     * 费用类别名称
     */
    private String costCategoryName;

    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;


    /**
     * 费用归属
     */
    private CostInfo costInfo;

    /**
     * 费用归属扩展数据
     */
    private CostInfoExtDTO costInfoExtDTO;


}
