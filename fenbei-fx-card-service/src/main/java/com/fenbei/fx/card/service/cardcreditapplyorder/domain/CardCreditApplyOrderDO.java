package com.fenbei.fx.card.service.cardcreditapplyorder.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditapplyorder.po.CardCreditApplyOrderPO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderAddReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderShowResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.converter.CardCreditApplyOrderConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡额度发放单 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-25
 */
@Slf4j
@Component
public class CardCreditApplyOrderDO extends BaseDO<CardCreditApplyOrderDTO, CardCreditApplyOrderPO, CardCreditApplyOrderConverter> {

    public static CardCreditApplyOrderDO me() {
        return SpringUtil.getBean(CardCreditApplyOrderDO.class);
    }

    public void checkCardCreditApplyOrderAddReqDTO(final CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        if (Func.isEmpty(cardCreditApplyOrderAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditApplyOrderAddReqDTOList(final List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList) {
        if (Func.isEmpty(cardCreditApplyOrderAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardCreditApplyOrderModifyReqDTO(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        if (Func.isEmpty(cardCreditApplyOrderModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditApplyOrderRemoveReqDTO(final CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO) {
        if (Func.isEmpty(cardCreditApplyOrderRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardCreditApplyOrderDTO buildListParamsDTO(final CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO) {
        return converter.convertToCardCreditApplyOrderDTO(cardCreditApplyOrderListReqDTO);
    }

    public CardCreditApplyOrderDTO buildPageParamsDTO(final CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO) {
        return converter.convertToCardCreditApplyOrderDTO(cardCreditApplyOrderPageReqDTO);
    }

    public CardCreditApplyOrderDTO buildAddCardCreditApplyOrderDTO(final CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        return converter.convertToCardCreditApplyOrderDTO(cardCreditApplyOrderAddReqDTO);
    }

    public List<CardCreditApplyOrderDTO> buildAddBatchCardCreditApplyOrderDTOList(final List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList) {
        return converter.convertToCardCreditApplyOrderDTOList(cardCreditApplyOrderAddReqDTOList);
    }

    public CardCreditApplyOrderDTO buildModifyCardCreditApplyOrderDTO(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        return converter.convertToCardCreditApplyOrderDTO(cardCreditApplyOrderModifyReqDTO);
    }

    public CardCreditApplyOrderDTO buildRemoveCardCreditApplyOrderDTO(final CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO) {
        return converter.convertToCardCreditApplyOrderDTO(cardCreditApplyOrderRemoveReqDTO);
    }

    public List<CardCreditApplyOrderListResDTO> transferCardCreditApplyOrderListResDTOList(final List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList) {
        if (Func.isEmpty(cardCreditApplyOrderDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardCreditApplyOrderListResDTOList(cardCreditApplyOrderDTOList);
    }

    public CardCreditApplyOrderListResDTO transferCardCreditApplyOrderListResDTO(final CardCreditApplyOrderDTO cardCreditApplyOrderDTO) {
        if (Func.isEmpty(cardCreditApplyOrderDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardCreditApplyOrderListResDTO(cardCreditApplyOrderDTO);
    }

    public Page<CardCreditApplyOrderPageResDTO> transferCardCreditApplyOrderPageResDTOPage(final Page<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOPage) {
        if (Func.isEmpty(cardCreditApplyOrderDTOPage) || Func.isEmpty(cardCreditApplyOrderDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardCreditApplyOrderPageResDTOPage(cardCreditApplyOrderDTOPage);
    }

    public CardCreditApplyOrderShowResDTO transferCardCreditApplyOrderShowResDTO(final CardCreditApplyOrderDTO cardCreditApplyOrderDTO) {
        if (Func.isEmpty(cardCreditApplyOrderDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardCreditApplyOrderShowResDTO(cardCreditApplyOrderDTO);
    }

    public List<CardCreditApplyOrderShowResDTO> transferCardCreditApplyOrderShowResDTOList(final List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList) {
        if (Func.isEmpty(cardCreditApplyOrderDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardCreditApplyOrderShowResDTOList(cardCreditApplyOrderDTOList);
    }
}
