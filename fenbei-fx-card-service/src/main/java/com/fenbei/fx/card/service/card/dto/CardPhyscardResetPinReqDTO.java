package com.fenbei.fx.card.service.card.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 卡激活-修改密码 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-07 14:48:23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPhyscardResetPinReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;
    /**
     * 卡ID
     */
    private String fxCardId;


    /**
     * 设置密码
     */
    @NotNull
    private String newPin;

    /**
     * 验证码
     */
    @NotNull
    private String verifyCode;

    @NotNull
    private String token;
}



