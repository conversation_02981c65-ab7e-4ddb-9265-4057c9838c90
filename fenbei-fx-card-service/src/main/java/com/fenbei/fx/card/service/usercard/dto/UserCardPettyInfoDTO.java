package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class UserCardPettyInfoDTO implements Serializable {
    /**
     * 是否有卡
     *  true : 员工已申请虚拟卡;
     *  false:员工未申请虚拟卡（如果员工已申请虚拟卡（hasCard=true）&& 备用金列表为空，则展示申请按钮，不然不展示）
     */

    private Boolean hasCard;

    private Boolean hasProxyAuth = false;
    /**
     * applyInfoList 第一条记录的bizNo
     */
    private String pettyId;
    /**
     * 企业备用金模式
     */
    private Integer companyMode;
    /**
     * 备用金模式
     *  1:普通模式
     *  2:备用金模式
     */
    private Integer cardModel;
    /**
     * 备用金总额
     */
    private BigDecimal totalBalance;
    /**
     * 备用金总额
     */
    private String totalBalanceShow;
    /**
     * 总申请金额
     */
    private BigDecimal totalApplyAmount;
    /**
     * 总申请金额
     */
    private String totalApplyAmountShow;

    /**
     * 是否可追加申请
     */
    private Boolean applyAppendFlag = false;
    /**
     * 是否可额度申请
     */
    private Boolean applyFlag = true;
    /**
     * 不可以申请时候的文案
     */
    private String applyDesc;

    /**
     * 额度申请单模版id
     */
    private String formId;

    private List<FxHomePagePayApplyDetail> applyInfoList;
}
