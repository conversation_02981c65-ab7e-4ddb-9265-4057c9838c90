package com.fenbei.fx.card.service.card.dto;

import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import com.luastar.swift.base.utils.EncodeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 国际卡 详情 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardShowResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;


    private Integer applyType;

    /**
     * 申请人名
     */
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    private String applyerLastName;

    /**
     * id
     */
    private String id;

    /**
     * 操作申请id
     */
    private String applyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    private String maskBankCardNo;

    /**
     * 企业商户ID
     */
    private String bankMchId;
    /**
     * 用户id（企业卡的持有人，个人卡的同cardholder里面的用户ID）
     */
    private String employeeId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 实体卡支付密码
     */
    private String cardPin;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatformName;
    /**
     * 发卡渠道 图标
     */
    private String cardPlatformIcon;


    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡的品牌 图标
     */
    private String cardBrandIcon;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功
     */
    private Integer applyStatus;

    private String cardStatusStr;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private Integer activeStatus;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 币种符号 $
     */
    private String currencySymbol;

    /**
     * 币种 美元
     */
    private String currencyName;

    /**
     * 卡可用余额
     */
    private BigDecimal balance;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    List<BaseAirwallexRpcDTO.Limit> showLimits ;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

    /**
     * 申请人手机号+区号
     */
    private String applyerPhone;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    private CardCanOperationDTO operationDTO;

    private String cardHolderName;

    /**
     * 区号
     */
    private String nationCode;

    private String phone;

    private String address;

    private String postalAddress;

    /**
     * 用户地址
     */
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;


    /**
     * 冻结金额
     */
    private BigDecimal freezenBalance;


}
