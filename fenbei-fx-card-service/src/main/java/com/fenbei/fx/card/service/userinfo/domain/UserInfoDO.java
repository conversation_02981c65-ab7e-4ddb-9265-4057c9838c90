package com.fenbei.fx.card.service.userinfo.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.userinfo.po.UserInfoPO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoAddReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoModifyReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoRemoveReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoShowResDTO;
import com.fenbei.fx.card.service.userinfo.converter.UserInfoConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户信息 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Slf4j
@Component
public class UserInfoDO extends BaseDO<UserInfoDTO, UserInfoPO, UserInfoConverter> {

    public static UserInfoDO me() {
        return SpringUtil.getBean(UserInfoDO.class);
    }

    public void checkUserInfoAddReqDTO(final UserInfoAddReqDTO userInfoAddReqDTO) {
        if (Func.isEmpty(userInfoAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkUserInfoAddReqDTOList(final List<UserInfoAddReqDTO> userInfoAddReqDTOList) {
        if (Func.isEmpty(userInfoAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkUserInfoModifyReqDTO(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        if (Func.isEmpty(userInfoModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkUserInfoRemoveReqDTO(final UserInfoRemoveReqDTO userInfoRemoveReqDTO) {
        if (Func.isEmpty(userInfoRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public UserInfoDTO buildListParamsDTO(final UserInfoListReqDTO userInfoListReqDTO) {
        return converter.convertToUserInfoDTO(userInfoListReqDTO);
    }

    public UserInfoDTO buildPageParamsDTO(final UserInfoPageReqDTO userInfoPageReqDTO) {
        return converter.convertToUserInfoDTO(userInfoPageReqDTO);
    }

    public UserInfoDTO buildAddUserInfoDTO(final UserInfoAddReqDTO userInfoAddReqDTO) {
        return converter.convertToUserInfoDTO(userInfoAddReqDTO);
    }

    public List<UserInfoDTO> buildAddBatchUserInfoDTOList(final List<UserInfoAddReqDTO> userInfoAddReqDTOList) {
        return converter.convertToUserInfoDTOList(userInfoAddReqDTOList);
    }

    public UserInfoDTO buildModifyUserInfoDTO(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        return converter.convertToUserInfoDTO(userInfoModifyReqDTO);
    }

    public UserInfoDTO buildRemoveUserInfoDTO(final UserInfoRemoveReqDTO userInfoRemoveReqDTO) {
        return converter.convertToUserInfoDTO(userInfoRemoveReqDTO);
    }

    public List<UserInfoListResDTO> transferUserInfoListResDTOList(final List<UserInfoDTO> userInfoDTOList) {
        if (Func.isEmpty(userInfoDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToUserInfoListResDTOList(userInfoDTOList);
    }

    public UserInfoListResDTO transferUserInfoListResDTO(final UserInfoDTO userInfoDTO) {
        if (Func.isEmpty(userInfoDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToUserInfoListResDTO(userInfoDTO);
    }

    public Page<UserInfoPageResDTO> transferUserInfoPageResDTOPage(final Page<UserInfoDTO> userInfoDTOPage) {
        if (Func.isEmpty(userInfoDTOPage) || Func.isEmpty(userInfoDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToUserInfoPageResDTOPage(userInfoDTOPage);
    }

    public UserInfoShowResDTO transferUserInfoShowResDTO(final UserInfoDTO userInfoDTO) {
        if (Func.isEmpty(userInfoDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToUserInfoShowResDTO(userInfoDTO);
    }

    public List<UserInfoShowResDTO> transferUserInfoShowResDTOList(final List<UserInfoDTO> userInfoDTOList) {
        if (Func.isEmpty(userInfoDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToUserInfoShowResDTOList(userInfoDTOList);
    }
}
