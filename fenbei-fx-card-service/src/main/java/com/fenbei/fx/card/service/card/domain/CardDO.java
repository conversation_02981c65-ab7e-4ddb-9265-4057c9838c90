package com.fenbei.fx.card.service.card.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.service.card.dto.CardAddReqDTO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardListReqDTO;
import com.fenbei.fx.card.service.card.dto.CardListResDTO;
import com.fenbei.fx.card.service.card.dto.CardModifyReqDTO;
import com.fenbei.fx.card.service.card.dto.CardPageReqDTO;
import com.fenbei.fx.card.service.card.dto.CardPageResDTO;
import com.fenbei.fx.card.service.card.dto.CardRemoveReqDTO;
import com.fenbei.fx.card.service.card.dto.CardShowResDTO;
import com.fenbei.fx.card.service.card.converter.CardConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-26
 */
@Slf4j
@Component
public class CardDO extends BaseDO<CardDTO, CardPO, CardConverter> {

    public static CardDO me() {
        return SpringUtil.getBean(CardDO.class);
    }

    public void checkCardAddReqDTO(final CardAddReqDTO cardAddReqDTO) {
        if (Func.isEmpty(cardAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardAddReqDTOList(final List<CardAddReqDTO> cardAddReqDTOList) {
        if (Func.isEmpty(cardAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardModifyReqDTO(final CardModifyReqDTO cardModifyReqDTO) {
        if (Func.isEmpty(cardModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardRemoveReqDTO(final CardRemoveReqDTO cardRemoveReqDTO) {
        if (Func.isEmpty(cardRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardDTO buildListParamsDTO(final CardListReqDTO cardListReqDTO) {
        return converter.convertToCardDTO(cardListReqDTO);
    }

    public CardDTO buildPageParamsDTO(final CardPageReqDTO cardPageReqDTO) {
        return converter.convertToCardDTO(cardPageReqDTO);
    }

    public CardDTO buildAddCardDTO(final CardAddReqDTO cardAddReqDTO) {
        return converter.convertToCardDTO(cardAddReqDTO);
    }

    public List<CardDTO> buildAddBatchCardDTOList(final List<CardAddReqDTO> cardAddReqDTOList) {
        return converter.convertToCardDTOList(cardAddReqDTOList);
    }

    public CardDTO buildModifyCardDTO(final CardModifyReqDTO cardModifyReqDTO) {
        return converter.convertToCardDTO(cardModifyReqDTO);
    }

    public CardDTO buildRemoveCardDTO(final CardRemoveReqDTO cardRemoveReqDTO) {
        return converter.convertToCardDTO(cardRemoveReqDTO);
    }

    public List<CardListResDTO> transferCardListResDTOList(final List<CardDTO> cardDTOList) {
        if (Func.isEmpty(cardDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardListResDTOList(cardDTOList);
    }

    public CardListResDTO transferCardListResDTO(final CardDTO cardDTO) {
        if (Func.isEmpty(cardDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardListResDTO(cardDTO);
    }

    public Page<CardPageResDTO> transferCardPageResDTOPage(final Page<CardDTO> cardDTOPage) {
        if (Func.isEmpty(cardDTOPage) || Func.isEmpty(cardDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardPageResDTOPage(cardDTOPage);
    }

    public CardShowResDTO transferCardShowResDTO(final CardDTO cardDTO) {
        if (Func.isEmpty(cardDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardShowResDTO(cardDTO);
    }

    public List<CardShowResDTO> transferCardShowResDTOList(final List<CardDTO> cardDTOList) {
        if (Func.isEmpty(cardDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardShowResDTOList(cardDTOList);
    }
}
