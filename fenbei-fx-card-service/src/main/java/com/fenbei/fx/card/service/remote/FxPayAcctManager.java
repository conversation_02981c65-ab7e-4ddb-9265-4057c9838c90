package com.fenbei.fx.card.service.remote;

import com.alibaba.fastjson.JSON;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.fxpay.api.enums.FxAccountStatusEnum;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.FxCompanyAccountSubType;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.interfaces.IFxCardFundChangingService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.FundChangingReq;
import com.fenbeitong.fxpay.api.vo.FundChangingRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CompanyAcctInfoReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *  调用fx-pay的一下方法合并
 */
@Component
public class FxPayAcctManager {
    @DubboReference
    ICompanyAcctService iCompanyAcctService;
    @DubboReference
    IFxCardFundChangingService iFxCardFundChangingService;

    /**
     * 查询企业默认的美元账户
     * @param companyId 企业ID
     * @return 备用金账户信息
     */
    public CompanyAcctRes queryAcct4Petty(String companyId,FxAcctChannelEnum channel){
        CompanyAcctInfoReq companyAcctInfoReq = new CompanyAcctInfoReq();
        companyAcctInfoReq.setCompanyId(companyId);
        companyAcctInfoReq.setChannel(channel);
        companyAcctInfoReq.setCurrency(CurrencyEnum.USD);
        companyAcctInfoReq.setAccountSubType(FxCompanyAccountSubType.PETTY);
        return queryAcct(companyAcctInfoReq);
    }
    public CompanyAcctRes queryAcct(CompanyAcctInfoReq companyAcctInfoReq){
        ResponseVo<List<CompanyAcctRes>> responseVo = iCompanyAcctService.queryCompanyAcctInfo(companyAcctInfoReq);
        if (!responseVo.isSuccess()) {
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_SYSTEM_ERROR);
        }
        //1.2 企业账户状态
        List<CompanyAcctRes> companyAcctResList = responseVo.getData();
        if (CollectionUtils.isEmpty(companyAcctResList) || companyAcctResList.get(0).getStatus() == FxAccountStatusEnum.INVALID.getStatus()) {
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_NO_EFFECTIVE_ACCT);
        }
        return companyAcctResList.get(0);
    }

    /**
     * 回收
     * @param fundChangingReq
     */
    public void recoverCredit(FundChangingReq fundChangingReq){
        FinhubLogger.info("recoverCredit req:{}",JSON.toJSONString(fundChangingReq));
        ResponseVo<FundChangingRes> responseVoOfFundChangingRes = iFxCardFundChangingService.recoverCredit(fundChangingReq);
        FinhubLogger.info("recoverCredit rsp:{}",JSON.toJSONString(responseVoOfFundChangingRes));
        if (!responseVoOfFundChangingRes.isSuccess()){
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_RETURN_FAILED_OF_ACCT_ERROR);
        }
    }

    public boolean grantCredit(FundChangingReq fundChangingReq){
        try {
            ResponseVo<FundChangingRes> responseVoOfFundChangingRes = iFxCardFundChangingService.grantCredit(fundChangingReq);
            if (!responseVoOfFundChangingRes.isSuccess() || responseVoOfFundChangingRes.getData().getAccountFlowId() == null) {
                throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_SYSTEM_ERROR);
            }
        }catch (Exception e){
            FinhubLogger.error("grantCredit error:" + JSON.toJSONString(fundChangingReq),e);
            return false;
        }
        return true;
    }
}
