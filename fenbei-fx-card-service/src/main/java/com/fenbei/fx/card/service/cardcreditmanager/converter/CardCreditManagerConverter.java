package com.fenbei.fx.card.service.cardcreditmanager.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditmanager.po.CardCreditManagerPO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡额度申请退回管理表 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-15
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardCreditManagerConverter extends BaseConverter<CardCreditManagerDTO, CardCreditManagerPO> {

    static CardCreditManagerConverter me() {
        return SpringUtil.getBean(CardCreditManagerConverter.class);
    }

    CardCreditManagerDTO convertToCardCreditManagerDTO(CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO);

    CardCreditManagerDTO convertToCardCreditManagerDTO(CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO);

    CardCreditManagerDTO convertToCardCreditManagerDTO(CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO);

    CardCreditManagerDTO convertToCardCreditManagerDTO(CardCreditManagerListReqDTO cardCreditManagerListReqDTO);

    CardCreditManagerDTO convertToCardCreditManagerDTO(CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO);

    CardCreditManagerShowResDTO convertToCardCreditManagerShowResDTO(CardCreditManagerDTO cardCreditManagerDTO);

    List<CardCreditManagerShowResDTO> convertToCardCreditManagerShowResDTOList(List<CardCreditManagerDTO> cardCreditManagerDTOList);

    CardCreditManagerListResDTO convertToCardCreditManagerListResDTO(CardCreditManagerDTO cardCreditManagerDTO);

    List<CardCreditManagerListResDTO> convertToCardCreditManagerListResDTOList(List<CardCreditManagerDTO> cardCreditManagerDTOList);

    List<CardCreditManagerDTO> convertToCardCreditManagerDTOList(List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList);

    CardCreditManagerPageResDTO convertToCardCreditManagerPageResDTO(CardCreditManagerDTO cardCreditManagerDTO);

    List<CardCreditManagerPageResDTO> convertToCardCreditManagerPageResDTOList(List<CardCreditManagerDTO> cardCreditManagerDTOList);

    Page<CardCreditManagerPageResDTO> convertToCardCreditManagerPageResDTOPage(Page<CardCreditManagerDTO> cardCreditManagerDTOPage);
}
