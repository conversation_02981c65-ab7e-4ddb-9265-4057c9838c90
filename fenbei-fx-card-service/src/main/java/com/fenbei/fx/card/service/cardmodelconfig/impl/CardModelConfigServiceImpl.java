package com.fenbei.fx.card.service.cardmodelconfig.impl;

import com.fenbei.fx.card.common.enums.ActiveModelEnum;
import com.fenbei.fx.card.common.enums.ModelTypeEnum;
import com.fenbei.fx.card.dao.cardemployeemodelconfig.po.CardEmployeeModelConfigPO;
import com.fenbei.fx.card.dao.cardmodelconfig.po.CardModelConfigPO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.manager.CardEmployeeModelConfigManager;
import com.fenbei.fx.card.service.cardmodelconfig.CardModelConfigService;
import com.fenbei.fx.card.service.cardmodelconfig.dto.*;
import com.fenbei.fx.card.service.cardmodelconfig.manager.CardModelConfigManager;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡使用模式配置 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Service
public class CardModelConfigServiceImpl extends BaseServiceImpl<CardModelConfigManager, CardModelConfigPO, CardModelConfigDTO> implements CardModelConfigService {

    @Resource
    private CardEmployeeModelConfigManager cardEmployeeModelConfigManager;


    @Override
    public List<CardModelConfigListResDTO> list(final CardModelConfigListReqDTO cardModelConfigListReqDTO) {
        return manager.list(cardModelConfigListReqDTO);
    }

    @Override
    public CardModelConfigListResDTO listOne(final CardModelConfigListReqDTO cardModelConfigListReqDTO) {
        return manager.listOne(cardModelConfigListReqDTO);
    }

    @Override
    public Page<CardModelConfigPageResDTO> pagination(final CardModelConfigPageReqDTO cardModelConfigPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardModelConfigPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        return manager.add(cardModelConfigAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        return manager.addAllColumn(cardModelConfigAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList) {
        return manager.addBatchAllColumn(cardModelConfigAddReqDTOList);
    }

    @Override
    public CardModelConfigShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardModelConfigShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        return manager.modify(cardModelConfigModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        return manager.modifyAllColumn(cardModelConfigModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO) {
        return manager.removeByParams(cardModelConfigRemoveReqDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        //企业生效模式
        if(cardModelConfigAddReqDTO.getModelType().equals(ModelTypeEnum.COMPANY_UNIFY.getCode())){
            //查询公司是否存在配置
            CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
            cardModelConfigListReqDTO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
            CardModelConfigListResDTO cardModelConfigListResDTO = this.listOne(cardModelConfigListReqDTO);
            if(Objects.nonNull(cardModelConfigListResDTO)){
                CardModelConfigPO cardModelConfigPO = new CardModelConfigPO();
                cardModelConfigPO.setId(cardModelConfigListResDTO.getId());
                cardModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                cardModelConfigPO.setModelType(cardModelConfigAddReqDTO.getModelType());
                manager.updateById(cardModelConfigPO);
            }else{
                //生成cardConfig
                CardModelConfigPO cardModelConfigPO = new CardModelConfigPO();
                cardModelConfigPO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
                cardModelConfigPO.setModelType(cardModelConfigAddReqDTO.getModelType());
                cardModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                manager.save(cardModelConfigPO);
            }
        }else{
            //获取公司配置，看是否是员工模式
            CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
            cardModelConfigListReqDTO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
            CardModelConfigListResDTO cardModelConfigListResDTO = this.listOne(cardModelConfigListReqDTO);
            if(Objects.nonNull(cardModelConfigListResDTO)){
                CardModelConfigPO cardModelConfigPO = new CardModelConfigPO();
                cardModelConfigPO.setId(cardModelConfigListResDTO.getId());
                cardModelConfigPO.setModelType(cardModelConfigAddReqDTO.getModelType());
//                cardModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                manager.updateById(cardModelConfigPO);
            }else{
                CardModelConfigPO cardModelConfigPO = new CardModelConfigPO();
                cardModelConfigPO.setModelType(cardModelConfigAddReqDTO.getModelType());
                cardModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                cardModelConfigPO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
                manager.save(cardModelConfigPO);
            }
            if(StringUtils.isNotBlank(cardModelConfigAddReqDTO.getEmployeeId())) {
                //公司id + 员工id 获取员工配置
                CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = new CardEmployeeModelConfigListReqDTO();
                cardEmployeeModelConfigListReqDTO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
                cardEmployeeModelConfigListReqDTO.setEmployeeId(cardModelConfigAddReqDTO.getEmployeeId());
                CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO = cardEmployeeModelConfigManager.listOne(cardEmployeeModelConfigListReqDTO);
                if (Objects.nonNull(cardEmployeeModelConfigListResDTO)) {
                    CardEmployeeModelConfigPO cardEmployeeModelConfigPO = new CardEmployeeModelConfigPO();
                    cardEmployeeModelConfigPO.setId(cardEmployeeModelConfigListResDTO.getId());
                    cardEmployeeModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                    cardEmployeeModelConfigManager.updateById(cardEmployeeModelConfigPO);
                } else {
                    CardEmployeeModelConfigPO cardEmployeeModelConfigPO = new CardEmployeeModelConfigPO();
                    cardEmployeeModelConfigPO.setCompanyId(cardModelConfigAddReqDTO.getCompanyId());
                    cardEmployeeModelConfigPO.setEmployeeId(cardModelConfigAddReqDTO.getEmployeeId());
                    cardEmployeeModelConfigPO.setActiveModel(cardModelConfigAddReqDTO.getActiveModel());
                    cardEmployeeModelConfigManager.save(cardEmployeeModelConfigPO);
                }
            }
        }
        return true;
    }

    @Override
    public EmployeeModelConfigDTO getEmployeeModelConfigByEmployeeId(String companyId,String employeeId) {
        EmployeeModelConfigDTO employeeModelConfigDTO = new EmployeeModelConfigDTO();
        employeeModelConfigDTO.setCompanyId(companyId);
        employeeModelConfigDTO.setEmployeeId(employeeId);
        CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
        cardModelConfigListReqDTO.setCompanyId(companyId);
        CardModelConfigListResDTO cardModelConfigListResDTO = manager.listOne(cardModelConfigListReqDTO);
        if(Objects.nonNull(cardModelConfigListResDTO)){
            if(cardModelConfigListResDTO.getModelType().equals(ModelTypeEnum.COMPANY_UNIFY.getCode())){
                employeeModelConfigDTO.setModelType(cardModelConfigListResDTO.getModelType());
                employeeModelConfigDTO.setActiveModel(cardModelConfigListResDTO.getActiveModel());
            }else{
                //查询个人配置
                CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = new CardEmployeeModelConfigListReqDTO();
                cardEmployeeModelConfigListReqDTO.setCompanyId(companyId);
                cardEmployeeModelConfigListReqDTO.setEmployeeId(employeeId);
                CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO = cardEmployeeModelConfigManager.listOne(cardEmployeeModelConfigListReqDTO);
                if(Objects.nonNull(cardEmployeeModelConfigListResDTO)) {
                   employeeModelConfigDTO.setModelType(ModelTypeEnum.PERSON_CONFIG.getCode());
                   employeeModelConfigDTO.setActiveModel(cardEmployeeModelConfigListResDTO.getActiveModel());
                }else{
                   employeeModelConfigDTO.setModelType(ModelTypeEnum.PERSON_CONFIG.getCode());
                   employeeModelConfigDTO.setActiveModel(cardModelConfigListResDTO.getActiveModel());

                }
            }
        }else{
            //默认为企业配置 普通模式
            employeeModelConfigDTO.setModelType(ModelTypeEnum.COMPANY_UNIFY.getCode());
            employeeModelConfigDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
        }
        return employeeModelConfigDTO;
    }

    @Override
    public Boolean employeeModelDefaultChange(Integer activeModel) {
        String company_id = UserAuthHolder.getCurrentUser().getCompany_id();
        CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
        cardModelConfigListReqDTO.setCompanyId(company_id);
        CardModelConfigListResDTO cardModelConfigListResDTO = manager.listOne(cardModelConfigListReqDTO);
        CardModelConfigPO cardModelConfigPO = new CardModelConfigPO();
        cardModelConfigPO.setId(cardModelConfigListResDTO.getId());
        cardModelConfigPO.setActiveModel(activeModel);
        manager.updateById(cardModelConfigPO);
        return true;
    }

    @Override
    public Integer employeeModelDefault() {
        String company_id = UserAuthHolder.getCurrentUser().getCompany_id();
        CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
        cardModelConfigListReqDTO.setCompanyId(company_id);
        CardModelConfigListResDTO cardModelConfigListResDTO = manager.listOne(cardModelConfigListReqDTO);
        if(Objects.nonNull(cardModelConfigListResDTO)){
            return cardModelConfigListResDTO.getActiveModel();
        }
        return ActiveModelEnum.NORMAL.getCode();
    }

    @Override
    public List<EmployeeModelConfigDTO> companyHaveModel() {
        List<EmployeeModelConfigDTO> employeeModelConfigDTOList = new ArrayList<>();
        EmployeeModelConfigDTO employeeModelConfigDTO;
        String company_id = UserAuthHolder.getCurrentUser().getCompany_id();
        CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
        cardModelConfigListReqDTO.setCompanyId(company_id);
        CardModelConfigListResDTO cardModelConfigListResDTO = manager.listOne(cardModelConfigListReqDTO);
        if(Objects.nonNull(cardModelConfigListResDTO)){
            if(cardModelConfigListResDTO.getModelType().equals(ModelTypeEnum.COMPANY_UNIFY.getCode())){
                employeeModelConfigDTO = new EmployeeModelConfigDTO();
                employeeModelConfigDTO.setModelType(cardModelConfigListResDTO.getModelType());
                employeeModelConfigDTO.setActiveModel(cardModelConfigListResDTO.getActiveModel());
                employeeModelConfigDTO.setModelName(ActiveModelEnum.getEnum(cardModelConfigListResDTO.getActiveModel()).getName());
                employeeModelConfigDTOList.add(employeeModelConfigDTO);
            }else{
                employeeModelConfigDTO = new EmployeeModelConfigDTO();
                employeeModelConfigDTO.setModelType(ModelTypeEnum.PERSON_CONFIG.getCode());
                employeeModelConfigDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
                employeeModelConfigDTO.setModelName(ActiveModelEnum.NORMAL.getName());
                employeeModelConfigDTOList.add(employeeModelConfigDTO);
                EmployeeModelConfigDTO employeeModelConfigDTOPetty = new EmployeeModelConfigDTO();
                employeeModelConfigDTOPetty.setModelType(ModelTypeEnum.PERSON_CONFIG.getCode());
                employeeModelConfigDTOPetty.setActiveModel(ActiveModelEnum.PETTY.getCode());
                employeeModelConfigDTOPetty.setModelName(ActiveModelEnum.PETTY.getName());
                employeeModelConfigDTOList.add(employeeModelConfigDTOPetty);
            }
        }else{
            employeeModelConfigDTO = new EmployeeModelConfigDTO();
            //默认为企业配置 普通模式
            employeeModelConfigDTO.setModelType(ModelTypeEnum.COMPANY_UNIFY.getCode());
            employeeModelConfigDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
            employeeModelConfigDTO.setModelName(ActiveModelEnum.NORMAL.getName());
            employeeModelConfigDTOList.add(employeeModelConfigDTO);
        }

        return employeeModelConfigDTOList;
    }

}
