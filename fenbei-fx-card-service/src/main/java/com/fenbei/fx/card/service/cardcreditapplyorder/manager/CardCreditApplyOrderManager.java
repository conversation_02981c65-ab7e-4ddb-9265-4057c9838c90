package com.fenbei.fx.card.service.cardcreditapplyorder.manager;

import com.fenbei.fx.card.common.enums.ApplyOrderStateEnum;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.util.BigDecimalUtils;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditapplyorder.CardCreditApplyOrderDAO;
import com.fenbei.fx.card.dao.cardcreditapplyorder.po.CardCreditApplyOrderPO;
import com.fenbei.fx.card.service.cardcreditapplyorder.domain.CardCreditApplyOrderDO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderAddReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderShowResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.converter.CardCreditApplyOrderConverter;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 国际卡额度发放单 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-11
 */
@Slf4j
@Component
public class CardCreditApplyOrderManager extends BaseManagerImpl<CardCreditApplyOrderDAO, CardCreditApplyOrderPO, CardCreditApplyOrderDTO, CardCreditApplyOrderConverter> {

    public static CardCreditApplyOrderManager me() {
        return SpringUtil.getBean(CardCreditApplyOrderManager.class);
    }


    public CardCreditApplyOrderShowResDTO showByApplyOrderId(String companyId, String applyOrderId) {

        if (StringUtils.isAnyBlank(companyId, applyOrderId)){
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "公司ID和发放单ID不能为空");
        }
        QueryWrapper<CardCreditApplyOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_ORDER_ID, applyOrderId);
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = super.findOne(queryWrapper);
        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderShowResDTO(cardCreditApplyOrderDTO);
    }

    public CardCreditApplyOrderDTO findDTOByMeaningNo(String meaningNo) {

        if (StringUtils.isBlank(meaningNo)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardCreditApplyOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_MEANING_NO, meaningNo);
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = super.findOne(queryWrapper);
        return cardCreditApplyOrderDTO;
    }


    public CardCreditApplyOrderDTO findDTOByApplyOrderId(String applyOrderId) {

        if (StringUtils.isBlank(applyOrderId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardCreditApplyOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_ORDER_ID, applyOrderId);
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = super.findOne(queryWrapper);
        return cardCreditApplyOrderDTO;
    }

    public List<CardCreditApplyOrderListResDTO> list(final CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO) {
        CardCreditApplyOrderDTO paramsDTO = CardCreditApplyOrderDO.me().buildListParamsDTO(cardCreditApplyOrderListReqDTO);

        List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList = super.findList(paramsDTO);

        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderListResDTOList(cardCreditApplyOrderDTOList);
    }

    public CardCreditApplyOrderListResDTO listOne(final CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO) {
        CardCreditApplyOrderDTO paramsDTO = CardCreditApplyOrderDO.me().buildListParamsDTO(cardCreditApplyOrderListReqDTO);

        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = super.findOne(paramsDTO);

        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderListResDTO(cardCreditApplyOrderDTO);
    }

    public Page<CardCreditApplyOrderPageResDTO> pagination(final CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO, final Integer current, final Integer size) {
        CardCreditApplyOrderDTO paramsDTO = CardCreditApplyOrderDO.me().buildPageParamsDTO(cardCreditApplyOrderPageReqDTO);

        Page<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOPage = super.findPage(paramsDTO, current, size);

        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderPageResDTOPage(cardCreditApplyOrderDTOPage);
    }

    public Page<CardCreditApplyOrderPageResDTO> paginationByCondition(final CardCreditApplyOrderPageReqDTO pageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardCreditApplyOrderPO> creditApplyOrderPOQueryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotBlank(pageReqDTO.getCompanyId())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_COMPANY_ID, pageReqDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getEmployeeId())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLICANT_ID, pageReqDTO.getEmployeeId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplicantName())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLICANT_NAME, pageReqDTO.getApplicantName());
        }
        if (Objects.nonNull(pageReqDTO.getApplyOrderType())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_ORDER_TYPE, pageReqDTO.getApplyOrderType());
        }
        if (Objects.nonNull(pageReqDTO.getApplyState())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_STATE, pageReqDTO.getApplyState());
        }
        if (Objects.nonNull(pageReqDTO.getApplyAmount())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_AMOUNT, BigDecimalUtils.yuanToFen(pageReqDTO.getApplyAmount()));
        }
        if (StringUtils.isNotBlank(pageReqDTO.getFxCardId())) {
            creditApplyOrderPOQueryWrapper.eq(CardCreditApplyOrderPO.DB_COL_FX_CARD_ID, pageReqDTO.getFxCardId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyOrderId())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_APPLY_ORDER_ID, pageReqDTO.getApplyOrderId());
        }

        if (StringUtils.isNotBlank(pageReqDTO.getApplyId())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_APPLY_ID, pageReqDTO.getApplyId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getMeaningNo())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_MEANING_NO, pageReqDTO.getMeaningNo());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyReason())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_APPLY_REASON, pageReqDTO.getApplyReason());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getTitle())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_TITLE, pageReqDTO.getTitle());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getCreateTimeBegin()) && StringUtils.isNotBlank(pageReqDTO.getCreateTimeEnd())) {
            Date createTimeBegin = DateUtils.parse(ObjUtils.toString(pageReqDTO.getCreateTimeBegin()), "yyyy/MM/dd HH:mm:ss");
            Date createTimeEnd = DateUtils.parse(ObjUtils.toString(pageReqDTO.getCreateTimeEnd()), "yyyy/MM/dd HH:mm:ss");
            creditApplyOrderPOQueryWrapper.between(CardCreditApplyOrderPO.DB_COL_CREATE_TIME, createTimeBegin, createTimeEnd);
        }

        if (StringUtils.isNotBlank(pageReqDTO.getCreaterName())) {
            creditApplyOrderPOQueryWrapper.like(CardCreditApplyOrderPO.DB_COL_CREATER_NAME, pageReqDTO.getCreaterName());
        }

        creditApplyOrderPOQueryWrapper.orderByDesc(CardCreditApplyOrderPO.DB_COL_UPDATE_TIME);
        Page<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOPage = super.findPage(creditApplyOrderPOQueryWrapper, current, size);
        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderPageResDTOPage(cardCreditApplyOrderDTOPage);
    }

    public Boolean add(final CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderAddReqDTO(cardCreditApplyOrderAddReqDTO);

        CardCreditApplyOrderDTO addCardCreditApplyOrderDTO = CardCreditApplyOrderDO.me().buildAddCardCreditApplyOrderDTO(cardCreditApplyOrderAddReqDTO);

        return super.saveDTO(addCardCreditApplyOrderDTO);
    }

    public Boolean addAllColumn(final CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderAddReqDTO(cardCreditApplyOrderAddReqDTO);

        CardCreditApplyOrderDTO addCardCreditApplyOrderDTO = CardCreditApplyOrderDO.me().buildAddCardCreditApplyOrderDTO(cardCreditApplyOrderAddReqDTO);

        return super.saveAllColumn(addCardCreditApplyOrderDTO);
    }

    public Boolean addBatchAllColumn(final List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderAddReqDTOList(cardCreditApplyOrderAddReqDTOList);

        List<CardCreditApplyOrderDTO> addBatchCardCreditApplyOrderDTOList = CardCreditApplyOrderDO.me().buildAddBatchCardCreditApplyOrderDTOList(cardCreditApplyOrderAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardCreditApplyOrderDTOList);
    }

    public CardCreditApplyOrderShowResDTO show(final String id) {
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = super.findById(id);

        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderShowResDTO(cardCreditApplyOrderDTO);
    }

    public List<CardCreditApplyOrderShowResDTO> showByIds(final List<String> ids) {
        CardCreditApplyOrderDO.me().checkIds(ids);

        List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList = super.findBatchIds(ids);

        return CardCreditApplyOrderDO.me().transferCardCreditApplyOrderShowResDTOList(cardCreditApplyOrderDTOList);
    }

    public Boolean modify(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderModifyReqDTO(cardCreditApplyOrderModifyReqDTO);

        CardCreditApplyOrderDTO modifyCardCreditApplyOrderDTO = CardCreditApplyOrderDO.me().buildModifyCardCreditApplyOrderDTO(cardCreditApplyOrderModifyReqDTO);

        return super.modifyById(modifyCardCreditApplyOrderDTO);
    }

    public Boolean modifyAllColumn(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderModifyReqDTO(cardCreditApplyOrderModifyReqDTO);

        CardCreditApplyOrderDTO modifyCardCreditApplyOrderDTO = CardCreditApplyOrderDO.me().buildModifyCardCreditApplyOrderDTO(cardCreditApplyOrderModifyReqDTO);

        return super.modifyAllColumnById(modifyCardCreditApplyOrderDTO);
    }

    public Boolean removeByParams(final CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO) {
        CardCreditApplyOrderDO.me().checkCardCreditApplyOrderRemoveReqDTO(cardCreditApplyOrderRemoveReqDTO);

        CardCreditApplyOrderDTO removeCardCreditApplyOrderDTO = CardCreditApplyOrderDO.me().buildRemoveCardCreditApplyOrderDTO(cardCreditApplyOrderRemoveReqDTO);

        return super.remove(removeCardCreditApplyOrderDTO);
    }

    @Override
    protected CardCreditApplyOrderPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditApplyOrderPO();
        }

        return BeanUtil.toBean(map, CardCreditApplyOrderPO.class);
    }

    @Override
    protected CardCreditApplyOrderDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditApplyOrderDTO();
        }

        return BeanUtil.toBean(map, CardCreditApplyOrderDTO.class);
    }

    public Boolean removeByApplyOrderId(String applyOrderId) {
        QueryWrapper<CardCreditApplyOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLY_ORDER_ID, applyOrderId);
        return super.remove(queryWrapper);
    }

    /**
     * 查询申请人是否有发放中的发放单
     * @param companyId 公司ID
     * @param applicantId 申请人ID
     * @return 是否有发放中的发放单
     */
    public boolean hasGrantingApplyOrder(String companyId, String applicantId) {
        QueryWrapper<CardCreditApplyOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardCreditApplyOrderPO.DB_COL_APPLICANT_ID, applicantId);
        queryWrapper.in(CardCreditApplyOrderPO.DB_COL_APPLY_STATE, ApplyOrderStateEnum.GRANTING.getCode(), ApplyOrderStateEnum.WAITING.getCode());

        return super.count(queryWrapper) > 0;
    }
}
