package com.fenbei.fx.card.common.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>
 */
@Data
public class KeyValueVO implements Serializable {

    private Integer key;

    private String type;

    private String value;

    private String color;

    public KeyValueVO(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public KeyValueVO(String type,String value){
        this.type = type;
        this.value = value;
    }

    public KeyValueVO() {
    }
}
