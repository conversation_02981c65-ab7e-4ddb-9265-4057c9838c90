package com.fenbei.fx.card.service.cardholder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/23
 */
@Data
public class CardholderDetailResDTO implements Serializable {

    private static final long serialVersionUID = 3096237130632113995L;
    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 申请类型 1.创建 2.更新
     */
    private Integer applyType;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，4.银行处理中 5.银行失败 6.银行成功
     */
    private Integer applyStatus;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 申请人名
     */
    private String firstName;

    /**
     * 申请人姓
     */
    private String lastName;


    /**
     * 姓名
     */
    private String name;

    private String nationCode;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birth;

    /**
     * email地址
     */
    private String email;

    /**
     * 证件的国家:US
     */
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    private Integer identificationType;

    /**
     * 证件号
     */
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围 2-长期有效
     */
    private Integer identificationExpiryType;

    /**
     * 用户地址
     */
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;

    /**
     * 展示的状态描述
     */
    private String showStatusStr;

    /**
     * 展示的状态
     */
    private Integer showStatus;



    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    private String cardPlatformName;

}
