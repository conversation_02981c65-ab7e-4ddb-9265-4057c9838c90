package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 国际卡额度发放单 批量发放 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderBatchTrySendReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 申请单ID列表
     */
    @NotEmpty(message = "申请单ID列表不能为空")
    private List<String> applyOrderIds;

}
