package com.fenbei.fx.card.service.bankcardflow;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
public interface BankCardFlowService extends BaseService<BankCardFlowDTO> {

    static BankCardFlowService me() {
        return SpringUtil.getBean(BankCardFlowService.class);
    }

    /**
     * 列表
     *
     * @param bankCardFlowListReqDTO 入参DTO
     * @return
     */
    List<BankCardFlowListResDTO> list(BankCardFlowListReqDTO bankCardFlowListReqDTO);

    /**
     * First查询
     *
     * @param bankCardFlowListReqDTO 入参DTO
     * @return
     */
    BankCardFlowListResDTO listOne(BankCardFlowListReqDTO bankCardFlowListReqDTO);

    /**
     * 分页
     *
     * @param bankCardFlowPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<BankCardFlowPageResDTO> pagination(BankCardFlowPageReqDTO bankCardFlowPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param bankCardFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean add(BankCardFlowAddReqDTO bankCardFlowAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param bankCardFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(BankCardFlowAddReqDTO bankCardFlowAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param bankCardFlowAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    BankCardFlowShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<BankCardFlowShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param bankCardFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param bankCardFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO);

    /**
     * 参数删除
     *
     * @param bankCardFlowRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO);
}
