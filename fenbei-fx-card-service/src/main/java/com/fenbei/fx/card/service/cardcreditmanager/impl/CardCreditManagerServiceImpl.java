package com.fenbei.fx.card.service.cardcreditmanager.impl;

import com.fenbei.fx.card.service.cardcreditmanager.dto.*;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardcreditmanager.po.CardCreditManagerPO;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 国际卡额度申请退回管理表 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Slf4j
@Service
public class CardCreditManagerServiceImpl extends BaseServiceImpl<CardCreditManagerManager, CardCreditManagerPO, CardCreditManagerDTO> implements CardCreditManagerService {

    @Override
    public List<CardCreditManagerListResDTO> list(final CardCreditManagerListReqDTO cardCreditManagerListReqDTO) {
        return manager.list(cardCreditManagerListReqDTO);
    }

    @Override
    public CardCreditManagerListResDTO listOne(final CardCreditManagerListReqDTO cardCreditManagerListReqDTO) {
        return manager.listOne(cardCreditManagerListReqDTO);
    }

    @Override
    public Page<CardCreditManagerPageResDTO> pagination(final CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardCreditManagerPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        return manager.add(cardCreditManagerAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        return manager.addAllColumn(cardCreditManagerAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList) {
        return manager.addBatchAllColumn(cardCreditManagerAddReqDTOList);
    }

    @Override
    public CardCreditManagerShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardCreditManagerShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        return manager.modify(cardCreditManagerModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        return manager.modifyAllColumn(cardCreditManagerModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO) {
        return manager.removeByParams(cardCreditManagerRemoveReqDTO);
    }

    @Override
    public CardCreditManagerReturnRespDTO refund(CardCreditManagerReturnReqDTO cardCreditReturnRpcReqDTO) {
        return manager.refund(cardCreditReturnRpcReqDTO);
    }

    @Override
    public List<CardCreditManagerDTO> queryUncheckApply(String fxCardId) {
        return manager.queryUncheckApply(fxCardId);
    }

    @Override
    public List<CardCreditManagerDTO> queryUncheckApplyByEmployeeId(String employeeId) {
        return manager.queryUncheckApplyByEmployeeId(employeeId);
    }

    @Override
    public List<CardCreditManagerDTO> queryFailedApply(String companyId, String bankName, Date startDate) {
        return manager.queryFailedApply(companyId,bankName,startDate);
    }

    @Override
    public List<CardCreditManagerDTO> queryExistedApply(String bizNo) {
        return manager.queryExistedApply(bizNo);
    }
}
