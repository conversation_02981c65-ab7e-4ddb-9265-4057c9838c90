package com.fenbei.fx.card.service.cardcreditapplyorder;

import com.fenbei.fx.card.service.cardcreditapplyorder.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;

import java.util.List;

/**
 * 国际卡额度发放单 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
public interface CardCreditApplyOrderService extends BaseService<CardCreditApplyOrderDTO> {

    static CardCreditApplyOrderService me() {
        return SpringUtil.getBean(CardCreditApplyOrderService.class);
    }

    /**
     * 列表
     *
     * @param cardCreditApplyOrderListReqDTO 入参DTO
     * @return
     */
    List<CardCreditApplyOrderListResDTO> list(CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO);

    /**
     * First查询
     *
     * @param cardCreditApplyOrderListReqDTO 入参DTO
     * @return
     */
    CardCreditApplyOrderListResDTO listOne(CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO);

    /**
     * 分页
     *
     * @param cardCreditApplyOrderPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardCreditApplyOrderPageResDTO> pagination(CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO, Integer current, Integer size);

    Page<CardCreditApplyOrderPageResDTO> paginationByCondition(CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO, Integer current,
                                                               Integer size);

    /**
     * 新增
     *
     * @param cardCreditApplyOrderAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO);


    CardCreditApplyOrderDTO createApplyOrder(CardCreditApplyOrderAddReqVO cardCreditApplyOrderAddReqVO);

    /**
     * 新增(所有字段)
     *
     * @param cardCreditApplyOrderAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardCreditApplyOrderAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardCreditApplyOrderShowResDTO show(String id);

    CardCreditApplyOrderShowResDTO showByApplyOrderId(String companyId, String applyOrderId);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardCreditApplyOrderShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardCreditApplyOrderModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardCreditApplyOrderModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardCreditApplyOrderRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO);

    CardCreditApplyOrderTrySendResVO trySend(String companyId, CardCreditApplyOrderTrySendReqVO trySendReqDTO);

    /**
     * 批量发放额度
     *
     * @param companyId 公司ID
     * @param batchTrySendReqVO 批量发放请求参数
     * @return 批量发放结果
     */
    CardCreditApplyOrderBatchTrySendResVO batchTrySend(String companyId, CardCreditApplyOrderBatchTrySendReqVO batchTrySendReqVO);

    CardCreditApplyOrderDTO modifyByApplyOrderId(String companyId, CardCreditApplyOrderModifyReqVO creditApplyOrderModifyReqVO);

    Boolean removeByApplyOrderId(String companyId, String applyOrderId);
}
