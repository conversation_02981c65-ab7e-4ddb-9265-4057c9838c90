package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public enum CardholderApplyStatusEnum {

    /**
     * 待审核：编辑、删除、审核、详情
     * 更新中：详情
     * 更新失败：编辑、审核、详情
     *
     * showStatus 1：待审核 2：更新中 4：更新失败
     */
    APPLYING(1, 1, "待审核", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.DELETE.getKey(), CardholderOperTypeEnum.APPROVE.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),

    PASS(2, 2, "审核通过", Lists.newArrayList(CardholderOperTypeEnum.DETAIL.getKey())),
    REFUSE(3, -1, "审核拒绝", Lists.newArrayList()),
    BANK_DEALING(4, 2, "银行处理中", Lists.newArrayList(CardholderOperTypeEnum.DETAIL.getKey())),

    BANK_REFUSE(5, 4, "银行失败", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.APPROVE.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),

    BANK_PASS(6, -1,"银行成功", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.DISABLE.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),
    ;

    private final int key;
    private final int showStatus;
    private final String msg;
    private List<Integer> usableOpera;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("待审核", "cardholder.apply.status.applying");
        I18N_KEY_MAP.put("审核通过", "cardholder.apply.status.pass");
        I18N_KEY_MAP.put("审核拒绝", "cardholder.apply.status.refuse");
        I18N_KEY_MAP.put("银行处理中", "cardholder.apply.status.bank.dealing");
        I18N_KEY_MAP.put("银行失败", "cardholder.apply.status.bank.refuse");
        I18N_KEY_MAP.put("银行成功", "cardholder.apply.status.bank.pass");
    }

    public int getKey() {
        return key;
    }

    public int getShowStatus() {return showStatus;}

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }

    public List<Integer> getUsableOpera() {
        return usableOpera;
    }


    CardholderApplyStatusEnum(int key, int showStatus, String msg) {
        this.key = key;
        this.showStatus = showStatus;
        this.msg = msg;
    }
    CardholderApplyStatusEnum(int key, int showStatus, String msg, List<Integer> usableOpera) {
        this.key = key;
        this.showStatus = showStatus;
        this.msg = msg;
        this.usableOpera = usableOpera;
    }


    public static CardholderApplyStatusEnum getEnum(int key) {
        for (CardholderApplyStatusEnum item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static List<Integer> getNotRefusedCodes() {
        return Lists.newArrayList(APPLYING.getKey(), PASS.getKey(), BANK_DEALING.getKey(), BANK_PASS.getKey());
    }

    public static boolean isPass(Integer status) {
        return Objects.equals(status, PASS.key);
    }

    public static boolean isRefuse(Integer status) {
        return Objects.equals(status, REFUSE.key);
    }

    public static boolean isApplying(Integer status) {
        return Objects.equals(status, APPLYING.key);
    }

    public static boolean canApproveStatus(Integer status) {
        return Objects.equals(status, BANK_REFUSE.key) || Objects.equals(status, APPLYING.key);
    }

    public static boolean isBankDealing(Integer status) {
        return Objects.equals(status, BANK_DEALING.key);
    }

    public static boolean isApproveStatus(Integer status) {
        return Objects.equals(status, PASS.key) || Objects.equals(status, REFUSE.key);
    }

}
