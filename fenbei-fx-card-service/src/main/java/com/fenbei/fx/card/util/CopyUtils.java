package com.fenbei.fx.card.util;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.CollectionUtils;

import java.beans.PropertyDescriptor;
import java.util.*;

public class CopyUtils {
    /**
     * 复制对象
     *
     * @param source 原对象
     * @param clazz 目标对象Class类型
     * @param <T>
     * @param <K>
     * @return 目标对象
     */
    public static <T,K> T convert(K source, Class<T> clazz) {
        T t = BeanUtils.instantiateClass(clazz);
        BeanUtils.copyProperties(source, t);
        return t;
    }

    /**
     * 复制集合
     *
     * @param sourceList 原集合
     * @param clazz 目标对象Class对象
     * @param <T>
     * @param <K>
     * @return 目标集合
     */
    public static <T,K> List<T> copyList(List<K> sourceList, Class<T> clazz) {
        List<T> target = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return target;
        }
        sourceList.stream().filter(Objects::nonNull).forEach(source -> target.add(convert(source, clazz)));
        return target;
    }

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

}
