package com.fenbei.fx.card.common.constant;

import com.finhub.framework.exception.assertion.ServletExceptionAssert;

import com.fenbeitong.finhub.common.constant.FinhubMessageType;

import com.fenbei.fx.card.util.I18nUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public enum GlobalCoreResponseCode implements ServletExceptionAssert{

    SUCCESS(0, "SUCCESS", FinhubMessageType.SUCCESS),

    EXCEPTION(500, "系统错误", FinhubMessageType.TIP_TOAST),

    ILLEGAL_ARGUMENT(600, "参数错误", FinhubMessageType.TIP_TOAST),
    PARAMS_NULL(601, "请求参数为空", FinhubMessageType.TIP_TOAST),

    SERVER_REQUEST_FAST(1001, "超过请求次数限制", FinhubMessageType.TIP_WINDOW),
    SERVER_REQUEST_FAST_MORE(1002, "超过过于频繁，请稍等1分钟后重试", FinhubMessageType.TIP_WINDOW),
    RemoteService401(999920, "访问URL需要身份认证", FinhubMessageType.TIP_TOAST),

    BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST2(********, "您已被移除企业，无法申请", FinhubMessageType.TIP_WINDOW,"企业被禁用"),
    BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST3(********, "您的账号已被企业禁用，无法申请", FinhubMessageType.TIP_WINDOW,"企业被禁用"),


    PHONE_EXIST_EXCEPTION(********, "该手机号已注册过，请更换手机号重试", FinhubMessageType.TIP_TOAST),
    EMAIL_EXIST_EXCEPTION(********, "该邮箱已注册过，请更换邮箱重试", FinhubMessageType.TIP_TOAST),

    EMAIL_FORMAT_ERROR(********, "该邮箱格式有误，请修改后重试", FinhubMessageType.TIP_TOAST),
    EMAIL_DOMAIN_NOT_ALLOWED(********, "邮箱请填写您的个人邮箱地址，如：@qq、@163等公共邮箱。企业邮箱（@公司域名）无法用于此操作，请勿使用", FinhubMessageType.TIP_TOAST),

    APPLY_NOT_EXIST(********, "该申请已不存在或有进行中的申请！", FinhubMessageType.TIP_TOAST),
    APPLY_APPROVE_STATUS_ERROR(********, "传递的审批状态不对！", FinhubMessageType.TIP_TOAST),
    CARDHOLDER_NOT_EXIST(********, "持卡人信息不存在！", FinhubMessageType.TIP_TOAST),
    CARDHOLDER_UPDATE_ERROR(11100007, "持卡人更新失败！", FinhubMessageType.TIP_TOAST),
    COMMIT_CARDHOLDER_UPDATE_ERROR(11100008, "提交持卡人更新失败！", FinhubMessageType.TIP_TOAST),
    CARDHOLDER_STATUS_NOT_SUPPORT_UPDATE(11100008, "当前状态不能更新！", FinhubMessageType.TIP_TOAST),
    CARDHOLDER_ENABLE_SUPPORT_UPDATE(11100009, "持卡人状态更新失败！", FinhubMessageType.TIP_TOAST),
    APPROVE_NO_AUTH(11100010, "无权限操作此条数据！", FinhubMessageType.TIP_TOAST),
    ADDRESS_NOT_WHOLE_ERROR(11100011, "地址信息不全！", FinhubMessageType.TIP_TOAST),
    ADDRESS_NOT_SUPPORT_ERROR(11100012, "暂不支持的州信息！", FinhubMessageType.TIP_TOAST),
    APPLY_NOT_APPROVE_STATUS_ERROR(11100013, "该申请不是待审核状态！", FinhubMessageType.TIP_TOAST),
    OPERATION_OFTEN(53050143, "操作过于频繁，请稍后重试", FinhubMessageType.TIP_WINDOW),
    CARDHOLDER_HAS_EXIST(53050144, "持卡人信息已存在，不能重复申请", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_HAS_EXIST(********, "持卡人申请信息已存在，不能重复申请", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_EXPIRY_DATE_ERROR(********, "证件过期时间不能为空", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_EXPIRY_DATE_FORMAT_ERROR(********, "证件过期时间格式不正确", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_ERROR(********, "用户持卡人申请信息存在异常", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_IS_BANK_DEALING(********, "当前持卡人信息已经在渠道处理中暂时不能更新", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_UPDATE_HAS_EXIST(********, "当前持卡人存在进行中的更新信息，请完成后再提交", FinhubMessageType.TIP_TOAST),
    CARDHOLDERAPPLY_UPDATE_ERROR(********, "更新持卡人信息失败", FinhubMessageType.TIP_TOAST),

    // 卡申请
    CARD_HOLDER_NOT_EXIT(********, "持卡人不存在,请先创建持卡人", FinhubMessageType.TIP_WINDOW),
    CARD_APPLY_EXIT(********, "当前卡正在审核中,请勿重复提交", FinhubMessageType.TIP_WINDOW),
    CARD_APPLY_CREATE_ERROR(********, "airwallex创建卡信息失败", FinhubMessageType.TIP_WINDOW),
    CARD_APPLY_UPDATE_ERROR(********, "airwallex更新卡信息失败", FinhubMessageType.TIP_WINDOW),
    CARD_NOT_EXIT(********, "卡片不存在", FinhubMessageType.TIP_WINDOW),
    CARD_STATUS_UPDATE_ERROR(********, "卡片状态操作失败", FinhubMessageType.TIP_WINDOW),
    CARD_STATUS_UPDATE_CAN_NOT_DO(********, "当前卡片不可以进行此操作", FinhubMessageType.TIP_WINDOW),
    AIR_REQUEST_STATUS_NOT_MATCH(********, "airwallex操作后状态异常", FinhubMessageType.TIP_WINDOW),
    COMPANY_ACCOUNT_NOT_EXIT(********, "企业账户不存在", FinhubMessageType.TIP_WINDOW),
    COMPANY_ACCOUNT_STATUS_ERROR(********, "企业账户不可用", FinhubMessageType.TIP_WINDOW),
    COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH(********, "企业账户可用余额不足", FinhubMessageType.TIP_WINDOW),
    CARD_HOLDER_NOT_PASS(********, "您的持卡人信息已同步至企业管理员，请联系企业管理员先对您的持卡人信息作审核，审核通过后才可申请实体卡！", FinhubMessageType.TIP_WINDOW),
    CARD_QUERY_ERROR(********, "查询卡敏感信息失败", FinhubMessageType.TIP_WINDOW),
    CARD_UPDATE_ERROR_LIAN_NO(********, "连连不支持此操作", FinhubMessageType.TIP_WINDOW),
    LIANLIAN_REQUEST_STATUS_NOT_MATCH(********, "连连操作后状态异常", FinhubMessageType.TIP_WINDOW),
    CARD_CVV_NOT_MATCH(********, "CVV输入错误，请检查后重新填写", FinhubMessageType.TIP_WINDOW),
    CARD_PHYSICAL_ACTIVE_FAILED(********, "激活失败，失败原因为：{0}，点击下发【重新激活】", FinhubMessageType.TIP_WINDOW),
    CARD_PIN_NOT_MATCH(********, "新旧密码一致，请检查后重新填写", FinhubMessageType.TIP_WINDOW),

    CARD_PIN_NOT_DATA(12000019, "密码非四位数字，请检查后重新填写", FinhubMessageType.TIP_WINDOW),
    CARD_PIN_CARDNO_MATCH(12000020, "密码不能是卡号后四位，请检查后重新填写", FinhubMessageType.TIP_WINDOW),
    LIANLIAN_REQUEST_STATUS_TIMEOUT(12000021, "连连网络超时", FinhubMessageType.TIP_WINDOW),
    CARD_VERIFYCODE_NOT_DATA(12000022, "验证码非六位数字，请检查后重新填写", FinhubMessageType.TIP_WINDOW),
    CARD_PHYSICAL_NEWPIN_FAILED(12000023, "密码更换失败\n，失败原因为：{0}，请您重新修改密码", FinhubMessageType.TIP_WINDOW),
    CARD_OLDPIN_NOT_MATCH(********, "旧密码不对，请检查后重新填写", FinhubMessageType.TIP_WINDOW),
    //模式配置
    EMPLOYEE_INFO_MISSING(********, "员工配置信息为空", FinhubMessageType.TIP_WINDOW),
    ORDER_ALREADY_XIAFA(********,"单据已经下发或者正在发放中,请刷新页面重试!", FinhubMessageType.TIP_WINDOW),

    //额度管理
    CREDIT_APPLY_REJECT_OF_COMPANY_ACCOUNT_BALANCE_NOT_ENOUGH(*********,"企业账户可用余额不足",FinhubMessageType.TIP_WINDOW),
    CREDIT_APPLY_REJECT_OF_SYSTEM_ERROR(*********,"系统异常",FinhubMessageType.TIP_WINDOW),
    CREDIT_APPLY_REJECT_OF_NO_EFFECTIVE_ACCT(*********,"未查询到有效账户",FinhubMessageType.TIP_WINDOW),

    CREDIT_APPLY_REJECT_OF_COMPANY_ACCOUNT_GRANT_OFF(*********,"企业账户已被禁止下发", FinhubMessageType.TIP_WINDOW),

    CREDIT_APPLY_REJECT_OF_HAS_BALANCE(*********,"卡当前有余额",FinhubMessageType.TIP_WINDOW),
    CREDIT_APPLY_REJECT_OF_CARD_DISABLED(*********,"卡状态不可用",FinhubMessageType.TIP_WINDOW),

    CREDIT_RETURN_FAILED_OF_ACCT_ERROR(*********,"额度回收异常",FinhubMessageType.TIP_WINDOW),
    CREDIT_NOT_EXIST_ERROR(*********,"额度申请单信息不存在",FinhubMessageType.TIP_WINDOW),
    CREDIT_UNCHECK_AMOUNT_ERROR(*********,"申请单未核销金额不能小于本次核销金额",FinhubMessageType.TIP_WINDOW),
    EXIST_CREDIT_UNCHECK_AMOUNT_ERROR(*********,"存在申请核销金额大于未核销金额的单据",FinhubMessageType.TIP_WINDOW),
    CREDIT_APPLY_REJECT_OF_HAS_UNCHECKED(*********,"卡当前有未核销金额",FinhubMessageType.TIP_WINDOW),

    LIANLIAN_BALANCE_NOT_ENOUGH(*********,"卡当前有冻结余额,无法退回",FinhubMessageType.TIP_WINDOW),

    CREDIT_APPLY_ERROR(140000012,"额度申请异常,请稍后重试",FinhubMessageType.TIP_WINDOW),

    TITLE_ORDER_LENGTH_MAX(140000013,"单据标题长度限制30个字!", FinhubMessageType.TIP_WINDOW),
    REASON_ORDER_LENGTH_MAX(140000014,"事由长度限制200个字!", FinhubMessageType.TIP_WINDOW),
    CREATE_ORDER_SIZE_MAX(140000015,"超过50单据创建的限制!", FinhubMessageType.TIP_WINDOW),
    CREATE_CODE_FIAL(140000016,"生成单据业务编码失败!", FinhubMessageType.TIP_WINDOW),
    APPLY_CREDIT_NOT_EXIST(140000017,"额度发放单不存在!", FinhubMessageType.TIP_WINDOW),
    //预算管理
    BUDGET_ERROR(150000001,"预算接口异常",FinhubMessageType.TIP_WINDOW),

    //核销
    VERIFICATION_REQ_ERROR(160000001,"参数异常",FinhubMessageType.TIP_WINDOW),
    VERIFICATION_ORDER_NOT_FUND(160000002,"订单未查到",FinhubMessageType.TIP_WINDOW),
    VERIFICATION_COST_ID_NOT_MATCH(160000003,"核销费用id与绑定费用id不匹配",FinhubMessageType.TIP_WINDOW),
    VERIFICATION_NO_UNCHECK_AMOOUNT(160000004,"待核销金额为0",FinhubMessageType.TIP_WINDOW),
    VERIFICATION_NO_UNCHECK_AMOOUNT_ERROR(160000005,"订单待核销金额小于传入值",FinhubMessageType.TIP_WINDOW),
    VERIFICATION_NO_VERIFICATION_ORDER(160000006,"不存在核销单记录",FinhubMessageType.TIP_WINDOW),
    PAYMENT_EXPORT_TIME_MOBTH_SCOPE_ERROR(160000007,"请选择最长一个月范围进行查询",FinhubMessageType.TIP_TOAST),
    PAYMENT_EXPORT_TIME_YEAR_SCOPE_ERROR(160000008,"请选择最长一年范围进行查询",FinhubMessageType.TIP_TOAST),



    ;

    private int code;
    private String message;
    private int type;
    private String title;

    GlobalCoreResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    GlobalCoreResponseCode(int code, String message, int type) {
        this.code = code;
        this.message = message;
        this.type = type;
    }

    GlobalCoreResponseCode(int code, String message, int type, String title) {
        this.code = code;
        this.message = message;
        this.type = type;
        this.title = title;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return I18nUtils.getMessage("GlobalCoreResponseCode." + this.name(), this.message);
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
