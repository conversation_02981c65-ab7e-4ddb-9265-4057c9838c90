package com.fenbei.fx.card.service.remote.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Title: SaasBugetDto
 * @ProjectName fenbei-pay
 * @Description: 扣管控参数对象
 */
@Builder
@Data
public class SaasBugetDto {

    /**
      * 公司id
      */
    @NotBlank
    @JsonProperty("company_id")
    private String companyId;
    /**
     * 员工id
     */
    @NotBlank
    @JsonProperty("employee_id")
    private String employeeId;
    /**
     * 场景类型
     */
    @NotNull
    @JsonProperty("order_category")
    private Integer orderType;

    /**
     * 业务编码
     */
    @NotBlank
    @JsonProperty("order_id")
    private String bizNo;
    /**
     * 操作金额
     */
    @NotNull
    @JsonProperty("order_amount")
    private BigDecimal orderAmount;
    /**
     * 费用归属类型
     */
    @JsonProperty("cost_attribution_category")
    private Integer costAttributionCategory;
    /**
     * 费用归属id
     */
    @JsonProperty("cost_attribution_id")
    private String costAttributionId;
    /**
     * 费用归属时间类型
     */
    @JsonProperty("create_time")
    private String costAttributionTime;

    @JsonProperty("cost_attribution_list")
    private List<BudgetCostAttributionDTO> costAttributionList;

    /**
     * 费用归属可选范围 1.部门 2.项目 3.部门或项目 4.部门和项目
     */
    @JsonProperty("cost_attribution_scope")
    private Integer costAttributionScope;

    /**
     * 预算扣减类型(仅费用归属为部门和项目有效) 1.部门和项目 2.部门 3.项目
     */
    @JsonProperty("budget_cost_attr_type")
    private Integer budgetCostAttrType;

    /**
     * 备用金id
     */
    @JsonProperty("pettyId")
    private String pettyId;

    /**
     * 获取申请交易单号
     */
    @JsonProperty("transNo")
    private String transNo;

    /**  必须给：目前是为了兼容老版本这没添加@NotNull
     *   REFUND_CREDIT(2, "退还","退还额度",PlusMinus.MINUS),
     *   COMPANY_REFUND_CREDIT(3, "企业退还","企业回收额度",PlusMinus.MINUS),
     *   SYSTEM_REFUND_CREDIT(4, "系统退还","系统回收额度",PlusMinus.MINUS);
     *  退还的具体业务类型请明确
     */
    @JsonProperty("bankApplyCreditType")
    private Integer bankApplyCreditType;
//
//    /**
//     * 已选择的额度申请单及金额
//     */
//    @JsonProperty("selectedCreditApplyList")
//    private List<BankRefundSelectedCreditApplyDTO> selectedCreditApplyList;
}
