package com.fenbei.fx.card.service.card.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.common.enums.CardStatusEnum;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.common.enums.DeleteFlagEnum;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.json.JsonUtils;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.core.str.StringUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 国际卡 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Slf4j
@Service
public class CardServiceImpl extends BaseServiceImpl<CardManager, CardPO, CardDTO> implements CardService {

    @Autowired @Lazy
    private CardApplyManager cardApplyManager;

    @DubboReference
    private IBaseEmployeeExtService iBaseEmployeeExtService;


    @Override
    public CardListResDTO listOne(final CardListReqDTO cardListReqDTO) {
        return manager.listOne(cardListReqDTO);
    }

    @Override
    public Page<CardPageResDTO> pagination(final CardPageReqDTO cardPageReqDTO, final Integer current,
                                           final Integer size) {

        return manager.mergePagination(cardPageReqDTO,current,size);
    }

    @Override
    public Page<CardPageResDTO> allCard(CardPageReqDTO cardPageReqDTO, Integer current, Integer size) {
        return manager.pagination(cardPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardAddReqDTO cardAddReqDTO) {
        return manager.add(cardAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardAddReqDTO cardAddReqDTO) {
        return manager.addAllColumn(cardAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardAddReqDTO> cardAddReqDTOList) {
        return manager.addBatchAllColumn(cardAddReqDTOList);
    }

    @Override
    public CardShowResDTO cardDetail(final String fxCardId) {
        return manager.cardDetail(fxCardId);
    }


    @Override
    public CardSumBalanceResDTO cardSumBalance(final String companyId) {
        return manager.cardSumBalance(companyId);
    }

    @Override
    public List<CardSumBalanceListResDTO> cardSumBalanceList(String companyId) {
        return manager.cardSumBalanceList(companyId);
    }


    @Override
    public CardDTO cardDetailByCardId(String cardId) {
        return manager.cardDetailByCardId(cardId);
    }

    @Override
    public CardDTO cardDetailByFxCardId(String fxCardId) {
        return manager.cardDetailByFxCardId(fxCardId);
    }

    @Override
    public CardDTO cardDetailByBankAccountNo(String bankAccountNo) {
        return manager.cardDetailByBankAccountNo(bankAccountNo);
    }

    @Override
    public List<CardDTO> queryByEmployeeId(String employeeId) {
        return manager.queryByEmployeeId(employeeId);
    }

    @Override
    public List<CardShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardModifyReqDTO cardModifyReqDTO) {
        return manager.modify(cardModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardModifyReqDTO cardModifyReqDTO) {
        return manager.modifyAllColumn(cardModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardRemoveReqDTO cardRemoveReqDTO) {
        return manager.removeByParams(cardRemoveReqDTO);
    }

    @Override
    public Boolean updateCardStatus(UpdateCardStatusReqDTO updateCardStatusReqDTO) {
        return manager.updateCardStatus(updateCardStatusReqDTO);
    }

    @Override
    public Boolean updateLimits(UpdateCardStatusReqDTO updateCardStatusReqDTO) {
        return manager.updateLimits(updateCardStatusReqDTO);
    }

    @Override
    public Boolean activateCard(ActivateCardReqDTO activateCardReqDTO) {
        return manager.activateCard(activateCardReqDTO);
    }

    @Override
    public CardCaptchaApplyResDTO captchaApply(CardCaptchaApplyReqDTO cardCaptchaApplyReqDTO) {
        return manager.captchaApply(cardCaptchaApplyReqDTO);
    }

    @Override
    public Boolean physcardActivate(CardPhyscardActiveReqDTO cardPhyscardActiveReqDTO) {
        return manager.physcardActivate(cardPhyscardActiveReqDTO);
    }

    @Override
    public Boolean physcardResetPinCheck(CardPhyscardResetPinCheckReqDTO physcardResetPinCheckDTO) {
        return manager.physcardResetPinCheck(physcardResetPinCheckDTO);
    }

    @Override
    public Boolean physcardResetPin(CardPhyscardResetPinReqDTO physcardResetPinReqDTO) {
        return manager.physcardResetPin(physcardResetPinReqDTO);
    }

    @Override
    public Page<CardWebListPageResDTO> queryCardListForNoAuth(CardWebListPageReqDTO cardWebListPageReqDTO, int current, int size) {
        log.info("queryCardListForNoAuth 开始查询，参数：{}", JsonUtils.toJson(cardWebListPageReqDTO));
        String companyId = cardWebListPageReqDTO.getCompanyId();
        Page<CardWebListPageResDTO> result = new  Page<>(current, size, 0L);

        List<CardWebListPageResDTO> cardPageResDTOList = new ArrayList<>();
        // 1. 先按照条件查找到拥有卡片的人员
        List<String> employeeIds = findEmployeesWithCards(cardWebListPageReqDTO);
        log.info("queryCardListForNoAuth 查询到符合条件的员工数量{}",employeeIds.size());
        if (CollectionUtils.isEmpty(employeeIds)) {
            log.info("未找到符合条件的员工");
            return result;
        }
        // 分页处理员工列表
        int startIndex = (current - 1) * size;
        int endIndex = Math.min(startIndex + size, employeeIds.size());
        List<String> pageEmployeeIds = employeeIds.subList(startIndex, endIndex);
        // 2. 按照人员分别查询拥有的卡片详情
        for (String employeeId : pageEmployeeIds) {
            try {
                CardWebListPageResDTO cardPageResDTO = buildCardPageResDTO(companyId,employeeId);
                if (cardPageResDTO != null) {
                    cardPageResDTOList.add(cardPageResDTO);
                }
            } catch (Exception e) {
                log.error("查询员工卡片信息失败，employeeId: {}", employeeId, e);
            }
        }
        result.setCurrent(current);
        result.setSize(size);
        result.setRecords(cardPageResDTOList);
        result.setTotal(employeeIds.size());
        log.info("queryCardListForNoAuth 查询完成，返回 {} 条记录", cardPageResDTOList.size());
        return result;
    }

    /**
     * 根据条件查找拥有卡片的员工ID列表
     */
    private List<String> findEmployeesWithCards(CardWebListPageReqDTO cardWebListPageReqDTO) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();

        // 基本条件
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());

        if (StringUtils.isNotBlank(cardWebListPageReqDTO.getCompanyId())) {
            queryWrapper.eq(CardPO.DB_COL_COMPANY_ID, cardWebListPageReqDTO.getCompanyId());
        }

        if (StringUtils.isNotBlank(cardWebListPageReqDTO.getEmployeeId())) {
            queryWrapper.like(CardPO.DB_COL_EMPLOYEE_ID, cardWebListPageReqDTO.getEmployeeId());
        }


        if (StringUtils.isNotBlank(cardWebListPageReqDTO.getEmployeeName())) {
            queryWrapper.like(CardPO.DB_COL_NAME_ON_CARD, cardWebListPageReqDTO.getEmployeeName());
        }

        if (StringUtils.isNotBlank(cardWebListPageReqDTO.getBankAccountNo())) {
            queryWrapper.like(CardPO.DB_COL_BANK_CARD_NO, cardWebListPageReqDTO.getBankAccountNo());
        }


        if (StringUtils.isNotBlank(cardWebListPageReqDTO.getEmployeePhone())) {
            // 通过申请信息查询
            List<String> fxCardList = cardApplyManager.getFxCardIdByPhoneInfo(cardWebListPageReqDTO.getEmployeePhone());
            if (CollectionUtils.isEmpty(fxCardList)) {
                return new ArrayList<>();
            } else {
                queryWrapper.in(CardPO.DB_COL_FX_CARD_ID, fxCardList);
            }
        }
        queryWrapper.eq(CardPO.DB_COL_CARD_STATUS, CardStatusEnum.ACTIVE.getStatus());
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        // 查询去重的员工ID列表
        queryWrapper.select("DISTINCT " + CardPO.DB_COL_EMPLOYEE_ID);
        queryWrapper.orderByDesc(CardPO.DB_COL_CREATE_TIME);
        List<CardDTO> cardDTOList = manager.findList(queryWrapper);

        return cardDTOList.stream()
                .map(CardDTO::getEmployeeId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 构建卡片分页响应DTO，包含员工部门信息
     */
    private CardWebListPageResDTO buildCardPageResDTO(String companyId,String employeeId) {
        try {
            CardWebListPageResDTO cardWebListPageResDTO = new CardWebListPageResDTO();
            cardWebListPageResDTO.setCompanyId(companyId);
            cardWebListPageResDTO.setEmployeeId(employeeId);

            List<CardWebListPageResDTO.CardsInfo> cardsInfoList = new ArrayList<>();
            // 查询该员工的卡片信息
            List<CardDTO> userCards = manager.queryByEmployeeId(employeeId);
            if (CollectionUtils.isNotEmpty(userCards)) {
                for (CardDTO cardDTO : userCards) {
                    CardWebListPageResDTO.CardsInfo cardsInfo = new CardWebListPageResDTO.CardsInfo();
                    cardsInfo.setFbCardNo(cardDTO.getFxCardId());
                    cardsInfo.setInfo(cardDTO.getCardPlatform() + "(" + cardDTO.getBankCardNo() + ")");
                    cardsInfo.setBankAccountNo(cardDTO.getBankCardNo());
                    cardsInfo.setBankName(CardPlatformEnum.getPlatform(cardDTO.getCardPlatform()).getName());
                    cardsInfo.setBankNameCode(cardDTO.getCardPlatform());
                    cardsInfoList.add(cardsInfo);
                }
            }
            cardWebListPageResDTO.setCardsInfo(cardsInfoList);

            // 查询员工信息，组装人员部门信息
            EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
            if (employeeContract != null) {
                // 设置员工基本信息
                cardWebListPageResDTO.setEmployeeName(employeeContract.getName());
                cardWebListPageResDTO.setEmployeePhone(employeeContract.getPhone_num());

                // 设置部门信息
                if (StringUtils.isNotBlank(employeeContract.getOrg_name())) {
                    cardWebListPageResDTO.setUserUnitName(employeeContract.getOrg_name());
                }

                if (StringUtils.isNotBlank(employeeContract.getOrg_name())) {
                    cardWebListPageResDTO.setStairOrgUnitName(employeeContract.getOrg_name());
                }
            }
            return cardWebListPageResDTO;
        } catch (Exception e) {
            log.error("构建卡片响应DTO失败，companyId: {},employeeId: {}",companyId,employeeId, e);
            return null;
        }
    }


}
