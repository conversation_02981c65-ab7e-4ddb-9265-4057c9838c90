package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 卡归属(企业或者个人)
 */
@AllArgsConstructor
public enum CardIssueToEnum {

    /**
     * 卡归属(企业或者个人)
     */
    ORGANISATION(1, "企业", "ORGANISATION"),
    INDIVIDUAL(2, "个人", "INDIVIDUAL"),
    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("企业", "card.issue.to.organisation");
        I18N_KEY_MAP.put("个人", "card.issue.to.individual");
    }

    public static CardIssueToEnum getCardIssueTo(Integer code) {
        for (CardIssueToEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    private String airCode;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAirCode() {
        return airCode;
    }

    public void setAirCode(String airCode) {
        this.airCode = airCode;
    }
}
