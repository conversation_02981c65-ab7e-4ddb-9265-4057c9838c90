package com.fenbei.fx.card.service.cardverificationflow.manager;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.dao.cardverificationflow.CardVerificationFlowDAO;
import com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO;
import com.fenbei.fx.card.service.cardverificationflow.converter.CardVerificationFlowConverter;
import com.fenbei.fx.card.service.cardverificationflow.domain.CardVerificationFlowDO;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.object.MapUtils;
import com.finhub.framework.core.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 核销记录表 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Slf4j
@Component
public class CardVerificationFlowManager extends BaseManagerImpl<CardVerificationFlowDAO, CardVerificationFlowPO, CardVerificationFlowDTO, CardVerificationFlowConverter> {

    public static CardVerificationFlowManager me() {
        return SpringUtil.getBean(CardVerificationFlowManager.class);
    }

    public List<CardVerificationFlowListResDTO> list(final CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO) {
        CardVerificationFlowDTO paramsDTO = CardVerificationFlowDO.me().buildListParamsDTO(cardVerificationFlowListReqDTO);

        List<CardVerificationFlowDTO> cardVerificationFlowDTOList = super.findList(paramsDTO);

        return CardVerificationFlowDO.me().transferCardVerificationFlowListResDTOList(cardVerificationFlowDTOList);
    }

    public CardVerificationFlowListResDTO listOne(final CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO) {
        CardVerificationFlowDTO paramsDTO = CardVerificationFlowDO.me().buildListParamsDTO(cardVerificationFlowListReqDTO);

        CardVerificationFlowDTO cardVerificationFlowDTO = super.findOne(paramsDTO);

        return CardVerificationFlowDO.me().transferCardVerificationFlowListResDTO(cardVerificationFlowDTO);
    }

    public Page<CardVerificationFlowPageResDTO> pagination(final CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO, final Integer current, final Integer size) {
        CardVerificationFlowDTO paramsDTO = CardVerificationFlowDO.me().buildPageParamsDTO(cardVerificationFlowPageReqDTO);

        Page<CardVerificationFlowDTO> cardVerificationFlowDTOPage = super.findPage(paramsDTO, current, size);

        return CardVerificationFlowDO.me().transferCardVerificationFlowPageResDTOPage(cardVerificationFlowDTOPage);
    }

    public Boolean add(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        CardVerificationFlowDO.me().checkCardVerificationFlowAddReqDTO(cardVerificationFlowAddReqDTO);

        CardVerificationFlowDTO addCardVerificationFlowDTO = CardVerificationFlowDO.me().buildAddCardVerificationFlowDTO(cardVerificationFlowAddReqDTO);

        return super.saveDTO(addCardVerificationFlowDTO);
    }

    public Boolean addAllColumn(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        CardVerificationFlowDO.me().checkCardVerificationFlowAddReqDTO(cardVerificationFlowAddReqDTO);

        CardVerificationFlowDTO addCardVerificationFlowDTO = CardVerificationFlowDO.me().buildAddCardVerificationFlowDTO(cardVerificationFlowAddReqDTO);

        return super.saveAllColumn(addCardVerificationFlowDTO);
    }

    public Boolean addBatchAllColumn(final List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList) {
        CardVerificationFlowDO.me().checkCardVerificationFlowAddReqDTOList(cardVerificationFlowAddReqDTOList);

        List<CardVerificationFlowDTO> addBatchCardVerificationFlowDTOList = CardVerificationFlowDO.me().buildAddBatchCardVerificationFlowDTOList(cardVerificationFlowAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardVerificationFlowDTOList);
    }

    public CardVerificationFlowShowResDTO show(final String id) {
        CardVerificationFlowDTO cardVerificationFlowDTO = super.findById(id);

        return CardVerificationFlowDO.me().transferCardVerificationFlowShowResDTO(cardVerificationFlowDTO);
    }

    public List<CardVerificationFlowShowResDTO> showByIds(final List<String> ids) {
        CardVerificationFlowDO.me().checkIds(ids);

        List<CardVerificationFlowDTO> cardVerificationFlowDTOList = super.findBatchIds(ids);

        return CardVerificationFlowDO.me().transferCardVerificationFlowShowResDTOList(cardVerificationFlowDTOList);
    }

    public Boolean modify(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        CardVerificationFlowDO.me().checkCardVerificationFlowModifyReqDTO(cardVerificationFlowModifyReqDTO);

        CardVerificationFlowDTO modifyCardVerificationFlowDTO = CardVerificationFlowDO.me().buildModifyCardVerificationFlowDTO(cardVerificationFlowModifyReqDTO);

        return super.modifyById(modifyCardVerificationFlowDTO);
    }

    public Boolean modifyAllColumn(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        CardVerificationFlowDO.me().checkCardVerificationFlowModifyReqDTO(cardVerificationFlowModifyReqDTO);

        CardVerificationFlowDTO modifyCardVerificationFlowDTO = CardVerificationFlowDO.me().buildModifyCardVerificationFlowDTO(cardVerificationFlowModifyReqDTO);

        return super.modifyAllColumnById(modifyCardVerificationFlowDTO);
    }

    public Boolean removeByParams(final CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO) {
        CardVerificationFlowDO.me().checkCardVerificationFlowRemoveReqDTO(cardVerificationFlowRemoveReqDTO);

        CardVerificationFlowDTO removeCardVerificationFlowDTO = CardVerificationFlowDO.me().buildRemoveCardVerificationFlowDTO(cardVerificationFlowRemoveReqDTO);

        return super.remove(removeCardVerificationFlowDTO);
    }

    public List<CardVerificationFlowDTO> queryCardVerificationInfoByFxCardIdAdVerificationId(String verificationId){
        if (StringUtils.isBlank(verificationId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardVerificationFlowPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardVerificationFlowPO.DB_COL_VERIFICATION_ID,verificationId);
        return this.findList(queryWrapper);
    }



    @Override
    protected CardVerificationFlowPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardVerificationFlowPO();
        }

        return (CardVerificationFlowPO) MapUtils.toBean(map, CardVerificationFlowPO.class);
    }

    @Override
    protected CardVerificationFlowDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardVerificationFlowDTO();
        }

        return (CardVerificationFlowDTO) MapUtils.toBean(map, CardVerificationFlowDTO.class);
    }
}
