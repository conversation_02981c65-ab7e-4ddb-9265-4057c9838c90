package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public enum CardholderStatusEnum {

    /**
     * 企业禁用：编辑、启用、详情
     * 生效中：编辑、禁用、详情
     * 失效：编辑、启用、详情
     *
     * showStatus 3：生效中 5：已失效
     */
    EFFECTIVE(1, 3, "生效中", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.DISABLE.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),

    DISABLED(2, 5, "已禁用", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.OPEN.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),
    EXPIRED(3, 5, "已失效", Lists.newArrayList(CardholderOperTypeEnum.EDIT.getKey(), CardholderOperTypeEnum.OPEN.getKey(), CardholderOperTypeEnum.DETAIL.getKey())),
   ;

    private final int key;
    private final int showStatus;
    private final String msg;
    private List<Integer> usableOpera;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("生效中", "cardholder.status.effective");
        I18N_KEY_MAP.put("已禁用", "cardholder.status.disabled");
        I18N_KEY_MAP.put("已失效", "cardholder.status.expired");
    }

    public int getKey() {
        return key;
    }

    public int getShowStatus() {return showStatus;}

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }

    public List<Integer> getUsableOpera() {
        return usableOpera;
    }


    CardholderStatusEnum(int key, int showStatus, String msg) {
        this.key = key;
        this.showStatus = showStatus;
        this.msg = msg;
    }
    CardholderStatusEnum(int key, int showStatus, String msg, List<Integer> usableOpera) {
        this.key = key;
        this.showStatus = showStatus;
        this.msg = msg;
        this.usableOpera = usableOpera;
    }


    public static CardholderStatusEnum getEnum(int key) {
        for (CardholderStatusEnum item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static List<Integer> getEnumsWithShowStatus(int showStatus) {
        List<Integer> list = new ArrayList<>();
        for (CardholderStatusEnum item : values()) {
            if (item.getShowStatus() == showStatus) {
                list.add(item.getKey());
            }
        }
        return list;
    }

    public static Boolean isDisabled(Integer key) {

        return Objects.equals(key, DISABLED.key);
    }

    public static Boolean isEffective(Integer key) {

        return Objects.equals(key, EFFECTIVE.key);
    }

}
