package com.fenbei.fx.card.scheduler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fenbei.fx.card.service.userinfo.UserInfoService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户安全备份定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2015/05/13 17:34
 */
@Slf4j
@Component
public class UserSafeBackupJob {

    /**
     * 用户安全备份任务执行器
     */
    @XxlJob("userSafeBackupHandler")
    public void userSafeBackupHandler() throws Exception {
        try {
            String param = XxlJobHelper.getJobParam();
            XxlJobHelper.log("用户安全备份任务开始执行. [JobId={}, 开始时间={}, 执行参数={}]", XxlJobHelper.getJobId(), DateUtil.now(), StrUtil.toStringOrNull(param));
            String resultMsg = UserInfoService.me().backupUserSafeInfo();
            XxlJobHelper.handleSuccess("success");
            XxlJobHelper.log("用户安全备份任务执行成功. [JobId={}, 结束时间={}, 执行结果={}]", XxlJobHelper.getJobId(), DateUtil.now(), resultMsg);
        } catch (Exception e) {
            XxlJobHelper.handleFail(e.getMessage());
            XxlJobHelper.log("用户安全备份任务执行失败. [JobId={}, 结束时间={}]", XxlJobHelper.getJobId(), DateUtil.now());
        }
    }
}
