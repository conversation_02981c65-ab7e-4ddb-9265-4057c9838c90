package com.fenbei.fx.card.service.cardholderapply.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/24
 */
@Data
public class AddressDto implements Serializable {
    private static final long serialVersionUID = 1369446454359250041L;

    /**
     * City of address地址城市
     */
    private AddressBaseDto city;

    /**
     * ISO country code of addressISO国家地址代码
     */
    private AddressBaseDto country;

    /**
     * 地址州或地区
     */
    private AddressBaseDto state;

    /**
     * 地址行1
     */
    private String line1;

    /**
     * 地址行2
     */
    private String line2;


    /**
     * 地址邮编或邮政编码
     */
    private String postcode;

}
