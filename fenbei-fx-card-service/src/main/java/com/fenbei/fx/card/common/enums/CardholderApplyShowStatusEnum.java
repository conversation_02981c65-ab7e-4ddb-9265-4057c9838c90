package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 展示给前端的持卡人/申请单状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public enum CardholderApplyShowStatusEnum {

    /**
     * showStatus 1：待审核 2：更新中 4：更新失败
     */
    APPLYING(1, "待审核"
        , Lists.newArrayList(CardholderOperTypeEnum.EDIT, CardholderOperTypeEnum.DELETE, CardholderOperTypeEnum.APPROVE, CardholderOperTypeEnum.DETAIL)
        , Lists.newArrayList(CardholderApplyStatusEnum.APPLYING.getKey())),

    UPDATING(2, "更新中", Lists.newArrayList(CardholderOperTypeEnum.DETAIL)
        , Lists.newArrayList(CardholderApplyStatusEnum.PASS.getKey(), CardholderApplyStatusEnum.BANK_DEALING.getKey())),

    UPDATING_FAILED(4, "更新失败"
        , Lists.newArrayList(CardholderOperTypeEnum.EDIT, CardholderOperTypeEnum.APPROVE, CardholderOperTypeEnum.DETAIL)
        , Lists.newArrayList(CardholderApplyStatusEnum.BANK_REFUSE.getKey())),
   ;

    private final int key;
    private final String desc;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("待审核", "cardholder.apply.show.status.applying");
        I18N_KEY_MAP.put("更新中", "cardholder.apply.show.status.updating");
        I18N_KEY_MAP.put("更新失败", "cardholder.apply.show.status.updating.failed");
    }

    private List<CardholderOperTypeEnum> usableOpera;

    /**
     * 对应表中状态类型
     */
    private List<Integer> status;

    public int getKey() {
        return key;
    }

    public String getDesc() {
        String i18nKey = I18N_KEY_MAP.get(desc);
        return i18nKey == null ? desc : I18nUtils.getMessage(i18nKey);
    }

    public List<CardholderOperTypeEnum> getUsableOpera() {
        return usableOpera;
    }

    public List<Integer> getStatus() {
        return status;
    }

    CardholderApplyShowStatusEnum(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }
    CardholderApplyShowStatusEnum(int key, String desc, List<CardholderOperTypeEnum> usableOpera, List<Integer> status) {
        this.key = key;
        this.desc = desc;
        this.usableOpera = usableOpera;
        this.status = status;
    }


    public static CardholderApplyShowStatusEnum getEnum(int key) {
        for (CardholderApplyShowStatusEnum item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static Boolean existKey(int key) {
        return null != getEnum(key);
    }

    public static CardholderApplyShowStatusEnum getEnumWhithApplyStatus(int applyStatus) {
        for (CardholderApplyShowStatusEnum item : values()) {
            for (Integer status : item.getStatus()) {
                if (status == applyStatus) {
                    return item;
                }
            }

        }
        return null;
    }

}
