package com.fenbei.fx.card.service.cardmail.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.cardmail.CardMailService;
import com.fenbei.fx.card.service.cardmail.dto.CardMailAddReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailRemoveReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailShowResDTO;
import com.fenbei.fx.card.service.cardmail.manager.CardMailManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实体卡邮寄管理表 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Slf4j
@Service
public class CardMailServiceImpl extends BaseServiceImpl<CardMailManager, CardMailPO, CardMailDTO> implements CardMailService {

    @Override
    public List<CardMailListResDTO> list(final CardMailListReqDTO cardMailListReqDTO) {
        return manager.list(cardMailListReqDTO);
    }

    @Override
    public CardMailListResDTO listOne(final CardMailListReqDTO cardMailListReqDTO) {
        return manager.listOne(cardMailListReqDTO);
    }

    @Override
    public Page<CardMailPageResDTO> pagination(final CardMailPageReqDTO cardMailPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardMailPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardMailAddReqDTO cardMailAddReqDTO) {
        return manager.add(cardMailAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardMailAddReqDTO cardMailAddReqDTO) {
        return manager.addAllColumn(cardMailAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardMailAddReqDTO> cardMailAddReqDTOList) {
        return manager.addBatchAllColumn(cardMailAddReqDTOList);
    }

    @Override
    public CardMailShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardMailShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        return manager.modify(cardMailModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        return manager.modifyAllColumn(cardMailModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardMailRemoveReqDTO cardMailRemoveReqDTO) {
        return manager.removeByParams(cardMailRemoveReqDTO);
    }
}
