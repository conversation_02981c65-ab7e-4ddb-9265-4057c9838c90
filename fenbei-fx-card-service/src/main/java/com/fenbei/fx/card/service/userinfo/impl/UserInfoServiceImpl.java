package com.fenbei.fx.card.service.userinfo.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.userinfo.po.UserInfoPO;
import com.fenbei.fx.card.service.userinfo.UserInfoService;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoAddReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoModifyReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoRemoveReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoShowResDTO;
import com.fenbei.fx.card.service.userinfo.manager.UserInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Slf4j
@Service
public class UserInfoServiceImpl extends BaseServiceImpl<UserInfoManager, UserInfoPO, UserInfoDTO> implements UserInfoService {

    @Override
    public List<UserInfoListResDTO> list(final UserInfoListReqDTO userInfoListReqDTO) {
        return manager.list(userInfoListReqDTO);
    }

    @Override
    public UserInfoListResDTO listOne(final UserInfoListReqDTO userInfoListReqDTO) {
        return manager.listOne(userInfoListReqDTO);
    }

    @Override
    public Page<UserInfoPageResDTO> pagination(final UserInfoPageReqDTO userInfoPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(userInfoPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final UserInfoAddReqDTO userInfoAddReqDTO) {
        return manager.add(userInfoAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final UserInfoAddReqDTO userInfoAddReqDTO) {
        return manager.addAllColumn(userInfoAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<UserInfoAddReqDTO> userInfoAddReqDTOList) {
        return manager.addBatchAllColumn(userInfoAddReqDTOList);
    }

    @Override
    public UserInfoShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<UserInfoShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        return manager.modify(userInfoModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        return manager.modifyAllColumn(userInfoModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final UserInfoRemoveReqDTO userInfoRemoveReqDTO) {
        return manager.removeByParams(userInfoRemoveReqDTO);
    }

    @Override
    public String backupUserSafeInfo() {
        return manager.backupUserSafeInfo();
    }
}
