package com.fenbei.fx.card.service.cardholder.dto;

import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
@Data
public class CardholderOrApplyListReqDTO implements Serializable {

    private static final long serialVersionUID = 7993041966674140800L;

    /**
     * 员工id
     */
    private String employeeId;

    private String phone;

    private String companyId;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;


}
