package com.fenbei.fx.card.service.cardcreditmanagerrelation.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditmanagerrelation.po.CardCreditManagerRelationPO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 关联申请单记录 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardCreditManagerRelationConverter extends BaseConverter<CardCreditManagerRelationDTO, CardCreditManagerRelationPO> {

    static CardCreditManagerRelationConverter me() {
        return SpringUtil.getBean(CardCreditManagerRelationConverter.class);
    }

    CardCreditManagerRelationDTO convertToCardCreditManagerRelationDTO(CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO);

    CardCreditManagerRelationDTO convertToCardCreditManagerRelationDTO(CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO);

    CardCreditManagerRelationDTO convertToCardCreditManagerRelationDTO(CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO);

    CardCreditManagerRelationDTO convertToCardCreditManagerRelationDTO(CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO);

    CardCreditManagerRelationDTO convertToCardCreditManagerRelationDTO(CardCreditManagerRelationPageReqDTO cardCreditManagerRelationPageReqDTO);

    CardCreditManagerRelationShowResDTO convertToCardCreditManagerRelationShowResDTO(CardCreditManagerRelationDTO cardCreditManagerRelationDTO);

    List<CardCreditManagerRelationShowResDTO> convertToCardCreditManagerRelationShowResDTOList(List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList);

    CardCreditManagerRelationListResDTO convertToCardCreditManagerRelationListResDTO(CardCreditManagerRelationDTO cardCreditManagerRelationDTO);

    List<CardCreditManagerRelationListResDTO> convertToCardCreditManagerRelationListResDTOList(List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList);

    List<CardCreditManagerRelationDTO> convertToCardCreditManagerRelationDTOList(List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList);

    CardCreditManagerRelationPageResDTO convertToCardCreditManagerRelationPageResDTO(CardCreditManagerRelationDTO cardCreditManagerRelationDTO);

    List<CardCreditManagerRelationPageResDTO> convertToCardCreditManagerRelationPageResDTOList(List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList);

    Page<CardCreditManagerRelationPageResDTO> convertToCardCreditManagerRelationPageResDTOPage(Page<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOPage);
}
