package com.fenbei.fx.card.service.cardoperflow.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardoperflow.po.CardOperFlowPO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 卡操作流水 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardOperFlowConverter extends BaseConverter<CardOperFlowDTO, CardOperFlowPO> {

    static CardOperFlowConverter me() {
        return SpringUtil.getBean(CardOperFlowConverter.class);
    }

    CardOperFlowDTO convertToCardOperFlowDTO(CardOperFlowAddReqDTO cardOperFlowAddReqDTO);

    CardOperFlowDTO convertToCardOperFlowDTO(CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO);

    CardOperFlowDTO convertToCardOperFlowDTO(CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO);

    CardOperFlowDTO convertToCardOperFlowDTO(CardOperFlowListReqDTO cardOperFlowListReqDTO);

    CardOperFlowDTO convertToCardOperFlowDTO(CardOperFlowPageReqDTO cardOperFlowPageReqDTO);

    CardOperFlowShowResDTO convertToCardOperFlowShowResDTO(CardOperFlowDTO cardOperFlowDTO);

    List<CardOperFlowShowResDTO> convertToCardOperFlowShowResDTOList(List<CardOperFlowDTO> cardOperFlowDTOList);

    CardOperFlowListResDTO convertToCardOperFlowListResDTO(CardOperFlowDTO cardOperFlowDTO);

    List<CardOperFlowListResDTO> convertToCardOperFlowListResDTOList(List<CardOperFlowDTO> cardOperFlowDTOList);

    List<CardOperFlowDTO> convertToCardOperFlowDTOList(List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList);

    CardOperFlowPageResDTO convertToCardOperFlowPageResDTO(CardOperFlowDTO cardOperFlowDTO);

    List<CardOperFlowPageResDTO> convertToCardOperFlowPageResDTOList(List<CardOperFlowDTO> cardOperFlowDTOList);

    Page<CardOperFlowPageResDTO> convertToCardOperFlowPageResDTOPage(Page<CardOperFlowDTO> cardOperFlowDTOPage);
}
