package com.fenbei.fx.card.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

public class DateFormatUtil {

    //其他年份的时间格式
    public static final String OTHER_YEAR_DATE_FORMAT = "yyyy/MM/dd HH:mm";
    //当前年份时间格式
    public static final String CUR_YEAR_DATE_FORMAT = "MM/dd HH:mm";
    public static final String EMPTY = "";

    /**
     * 格式：
     *     今年以内：mm/dd hh:mm，如：03/18 14:01
     *     非今年内：yyyy/mm/dd hh:mm，如：2021/12/25 14:01
     * 注意：
     *     使用 24 小时制；
     *     日期和时间之间包含一个空格；
     *     特殊：【费控报销】发票开票日期：yyyy/mm/dd
     *
     * @param dateTime 时间，可以是数据创建时间，更新时间
     * @return 根据时间返回前端需要展示的类型
     **/
    public static String tradeDateFormat(Date dateTime) {

        if (dateTime == null) {
            return EMPTY;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(dateTime);
        int dateYear = c.get(Calendar.YEAR);//传入时间的年份
        int curYear = LocalDate.now().getYear();//当前年份
        SimpleDateFormat format = null;
        //比较传入的日期是否是当前年份，使用不同的模版
        if (dateYear == curYear) {
            format = new SimpleDateFormat(CUR_YEAR_DATE_FORMAT);
        } else {
            format = new SimpleDateFormat(OTHER_YEAR_DATE_FORMAT);
        }
        return format.format(dateTime);
    }
}
