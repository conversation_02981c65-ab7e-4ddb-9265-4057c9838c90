package com.fenbei.fx.card.service.bankcardflow.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.bankcardflow.po.BankCardFlowPO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import com.fenbei.fx.card.service.bankcardflow.converter.BankCardFlowConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class BankCardFlowDO extends BaseDO<BankCardFlowDTO, BankCardFlowPO, BankCardFlowConverter> {

    public static BankCardFlowDO me() {
        return SpringUtil.getBean(BankCardFlowDO.class);
    }

    public void checkBankCardFlowAddReqDTO(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        if (Func.isEmpty(bankCardFlowAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkBankCardFlowAddReqDTOList(final List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList) {
        if (Func.isEmpty(bankCardFlowAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkBankCardFlowModifyReqDTO(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        if (Func.isEmpty(bankCardFlowModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkBankCardFlowRemoveReqDTO(final BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO) {
        if (Func.isEmpty(bankCardFlowRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public BankCardFlowDTO buildListParamsDTO(final BankCardFlowListReqDTO bankCardFlowListReqDTO) {
        return converter.convertToBankCardFlowDTO(bankCardFlowListReqDTO);
    }

    public BankCardFlowDTO buildPageParamsDTO(final BankCardFlowPageReqDTO bankCardFlowPageReqDTO) {
        return converter.convertToBankCardFlowDTO(bankCardFlowPageReqDTO);
    }

    public BankCardFlowDTO buildAddBankCardFlowDTO(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        return converter.convertToBankCardFlowDTO(bankCardFlowAddReqDTO);
    }

    public List<BankCardFlowDTO> buildAddBatchBankCardFlowDTOList(final List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList) {
        return converter.convertToBankCardFlowDTOList(bankCardFlowAddReqDTOList);
    }

    public BankCardFlowDTO buildModifyBankCardFlowDTO(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        return converter.convertToBankCardFlowDTO(bankCardFlowModifyReqDTO);
    }

    public BankCardFlowDTO buildRemoveBankCardFlowDTO(final BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO) {
        return converter.convertToBankCardFlowDTO(bankCardFlowRemoveReqDTO);
    }

    public List<BankCardFlowListResDTO> transferBankCardFlowListResDTOList(final List<BankCardFlowDTO> bankCardFlowDTOList) {
        if (Func.isEmpty(bankCardFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToBankCardFlowListResDTOList(bankCardFlowDTOList);
    }

    public BankCardFlowListResDTO transferBankCardFlowListResDTO(final BankCardFlowDTO bankCardFlowDTO) {
        if (Func.isEmpty(bankCardFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToBankCardFlowListResDTO(bankCardFlowDTO);
    }

    public Page<BankCardFlowPageResDTO> transferBankCardFlowPageResDTOPage(final Page<BankCardFlowDTO> bankCardFlowDTOPage) {
        if (Func.isEmpty(bankCardFlowDTOPage) || Func.isEmpty(bankCardFlowDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToBankCardFlowPageResDTOPage(bankCardFlowDTOPage);
    }

    public BankCardFlowShowResDTO transferBankCardFlowShowResDTO(final BankCardFlowDTO bankCardFlowDTO) {
        if (Func.isEmpty(bankCardFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToBankCardFlowShowResDTO(bankCardFlowDTO);
    }

    public List<BankCardFlowShowResDTO> transferBankCardFlowShowResDTOList(final List<BankCardFlowDTO> bankCardFlowDTOList) {
        if (Func.isEmpty(bankCardFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToBankCardFlowShowResDTOList(bankCardFlowDTOList);
    }
}
