package com.fenbei.fx.card.service.cardholderapply.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyAddReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyListReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyListResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyModifyReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyPageReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyPageResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyShowResDTO;
import com.fenbei.fx.card.service.cardholderapply.converter.CardholderApplyConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 持卡人操作申请 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Slf4j
@Component
public class CardholderApplyDO extends BaseDO<CardholderApplyDTO, CardholderApplyPO, CardholderApplyConverter> {

    public static CardholderApplyDO me() {
        return SpringUtil.getBean(CardholderApplyDO.class);
    }

    public void checkCardholderApplyAddReqDTO(final CardholderApplyAddReqDTO cardholderApplyAddReqDTO) {
        if (Func.isEmpty(cardholderApplyAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardholderApplyAddReqDTOList(final List<CardholderApplyAddReqDTO> cardholderApplyAddReqDTOList) {
        if (Func.isEmpty(cardholderApplyAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardholderApplyModifyReqDTO(final CardholderApplyModifyReqDTO cardholderApplyModifyReqDTO) {
        if (Func.isEmpty(cardholderApplyModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardholderApplyRemoveReqDTO(final CardholderApplyRemoveReqDTO cardholderApplyRemoveReqDTO) {
        if (Func.isEmpty(cardholderApplyRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardholderApplyDTO buildListParamsDTO(final CardholderApplyListReqDTO cardholderApplyListReqDTO) {
        return converter.convertToCardholderApplyDTO(cardholderApplyListReqDTO);
    }

    public CardholderApplyDTO buildPageParamsDTO(final CardholderApplyPageReqDTO cardholderApplyPageReqDTO) {
        return converter.convertToCardholderApplyDTO(cardholderApplyPageReqDTO);
    }

    public CardholderApplyDTO buildAddCardholderApplyDTO(final CardholderApplyAddReqDTO cardholderApplyAddReqDTO) {
        return converter.convertToCardholderApplyDTO(cardholderApplyAddReqDTO);
    }

    public List<CardholderApplyDTO> buildAddBatchCardholderApplyDTOList(final List<CardholderApplyAddReqDTO> cardholderApplyAddReqDTOList) {
        return converter.convertToCardholderApplyDTOList(cardholderApplyAddReqDTOList);
    }

    public CardholderApplyDTO buildModifyCardholderApplyDTO(final CardholderApplyModifyReqDTO cardholderApplyModifyReqDTO) {
        return converter.convertToCardholderApplyDTO(cardholderApplyModifyReqDTO);
    }

    public CardholderApplyDTO buildRemoveCardholderApplyDTO(final CardholderApplyRemoveReqDTO cardholderApplyRemoveReqDTO) {
        return converter.convertToCardholderApplyDTO(cardholderApplyRemoveReqDTO);
    }

    public List<CardholderApplyListResDTO> transferCardholderApplyListResDTOList(final List<CardholderApplyDTO> cardholderApplyDTOList) {
        if (Func.isEmpty(cardholderApplyDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardholderApplyListResDTOList(cardholderApplyDTOList);
    }

    public CardholderApplyListResDTO transferCardholderApplyListResDTO(final CardholderApplyDTO cardholderApplyDTO) {
        if (Func.isEmpty(cardholderApplyDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardholderApplyListResDTO(cardholderApplyDTO);
    }

    public Page<CardholderApplyPageResDTO> transferCardholderApplyPageResDTOPage(final Page<CardholderApplyDTO> cardholderApplyDTOPage) {
        if (Func.isEmpty(cardholderApplyDTOPage) || Func.isEmpty(cardholderApplyDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardholderApplyPageResDTOPage(cardholderApplyDTOPage);
    }

    public CardholderApplyShowResDTO transferCardholderApplyShowResDTO(final CardholderApplyDTO cardholderApplyDTO) {
        if (Func.isEmpty(cardholderApplyDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardholderApplyShowResDTO(cardholderApplyDTO);
    }

    public List<CardholderApplyShowResDTO> transferCardholderApplyShowResDTOList(final List<CardholderApplyDTO> cardholderApplyDTOList) {
        if (Func.isEmpty(cardholderApplyDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardholderApplyShowResDTOList(cardholderApplyDTOList);
    }
}
