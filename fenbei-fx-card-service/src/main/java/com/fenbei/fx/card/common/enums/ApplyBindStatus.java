package com.fenbei.fx.card.common.enums;

/**
 * 交易记录绑定状态
 */
public enum ApplyBindStatus {
    // key=9开头不能再用，9开头用在退款中
    NO(1, "未绑定", "未创建费用"),
    YES(2, "已绑定", "已创建费用"),
    ;

    private Integer key;
    private String msg;
    private String msgForClient;

    public Integer getKey() {
        return key;
    }

    public String getMsg() {
        return msg;
    }

    public String getMsgForClient() {
        return msgForClient;
    }


    ApplyBindStatus(Integer key, String msg, String msgForClient) {
        this.key = key;
        this.msg = msg;
        this.msgForClient = msgForClient;
    }


    public static ApplyBindStatus getEnum(Integer key) {
        if (null == key){
            return NO;
        }
        for (ApplyBindStatus item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return NO;
    }

    public String getString() {
        return this.name();
    }


}
