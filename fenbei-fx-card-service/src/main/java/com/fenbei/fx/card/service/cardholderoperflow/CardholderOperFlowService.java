package com.fenbei.fx.card.service.cardholderoperflow;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;

import java.util.List;

/**
 * 持卡人被操作流水 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardholderOperFlowService extends BaseService<CardholderOperFlowDTO> {

    static CardholderOperFlowService me() {
        return SpringUtil.getBean(CardholderOperFlowService.class);
    }

    /**
     * 列表
     *
     * @param cardholderOperFlowListReqDTO 入参DTO
     * @return
     */
    List<CardholderOperFlowListResDTO> list(CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO);

    /**
     * First查询
     *
     * @param cardholderOperFlowListReqDTO 入参DTO
     * @return
     */
    CardholderOperFlowListResDTO listOne(CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO);

    /**
     * 分页
     *
     * @param cardholderOperFlowPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardholderOperFlowPageResDTO> pagination(CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardholderOperFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardholderOperFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardholderOperFlowAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardholderOperFlowShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardholderOperFlowShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardholderOperFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardholderOperFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardholderOperFlowRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO);
}
