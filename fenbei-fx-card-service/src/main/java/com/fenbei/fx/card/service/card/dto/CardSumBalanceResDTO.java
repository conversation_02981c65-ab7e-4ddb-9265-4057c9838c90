package com.fenbei.fx.card.service.card.dto;

import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.util.USDNumberFormatUtil;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 国际卡 详情 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
public class CardSumBalanceResDTO implements Serializable {


    private static final long serialVersionUID = 5409185459234711691L;


    /**
     * 币种
     */
    private String currency = CurrencyEnum.USD.getCurrencyCode();


    /**
     * 可用余额汇总
     */
    private BigDecimal sumBalance = BigDecimal.ZERO;

    /**
     * 可用余额汇总展示
     */
    private String showUSDSumBalance = CurrencyNumberFormatUtil.moneyFormart(CurrencyEnum.getCurrencyByCodeIgnoreCase(currency),BigDecimalUtils.fen2yuan(sumBalance));

}
