package com.fenbei.fx.card.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.fenbeitong.finhub.common.utils.DateUtils;

import java.util.Date;

/**
 * id生成器封装
 */
public class IdUtils {

    public static Long getId(){
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        return snowflake.nextId();
    }
    public static Snowflake genSnowflake() {
        return IdUtil.createSnowflake(1, 1);
    }

    public static void main(String[] args) {
        Long id = getId();
        System.out.println(id);
//        1661625773200642048
        Date date = new Date();
        Date date1 = DateUtils.addDay(date,-7);
        Date date2 = DateUtils.addDay(date1, 5);
        int s = DateUtils.compareByDateTime(date2, DateUtils.now());
        System.out.println(s);
    }
}
