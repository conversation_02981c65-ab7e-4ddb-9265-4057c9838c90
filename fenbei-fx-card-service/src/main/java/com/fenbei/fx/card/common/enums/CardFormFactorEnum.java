package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 卡片形式：1-PHYSICAL、2-VIRTUAL
 */
@AllArgsConstructor
public enum CardFormFactorEnum {

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    PHYSICAL(1, "实体卡", "PHYSICAL"),
    VIRTUAL(2, "虚拟卡", "VIRTUAL"),
    ;

    private static final Map<Integer, CardFormFactorEnum> ENUM_MAP = Maps.newHashMap();
    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("实体卡", "card.form.factor.physical");
        I18N_KEY_MAP.put("虚拟卡", "card.form.factor.virtual");

        for (CardFormFactorEnum item : values()) {
            ENUM_MAP.put(item.getCode(), item);
        }
    }

    public static CardFormFactorEnum getEnum(Integer code) {
        return ENUM_MAP.get(code);
    }

    public static CardFormFactorEnum getFormFactor(Integer code) {
        return getEnum(code);
    }

    public static boolean isPhysical(Integer code) {
        return Objects.equals(CardFormFactorEnum.PHYSICAL.getCode(), code);
    }


    public static boolean isVirtual(Integer code) {
        return Objects.equals(CardFormFactorEnum.VIRTUAL.getCode(), code);
    }

    private Integer code;

    private String name;

    private String airCode;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAirCode() {
        return airCode;
    }

    public void setAirCode(String airCode) {
        this.airCode = airCode;
    }
}
