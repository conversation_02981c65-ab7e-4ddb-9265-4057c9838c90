package com.fenbei.fx.card.service.cardapply.dto;

import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 企业账户ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyAcctBaseDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 账户类型:备用金账户
     */
    private Integer accountSubType;

    /**
     * 企业ID
     */
    private String companyId;


    private String bankAccountNo;

    private String bankAcctId;

    private String bankName;

}
