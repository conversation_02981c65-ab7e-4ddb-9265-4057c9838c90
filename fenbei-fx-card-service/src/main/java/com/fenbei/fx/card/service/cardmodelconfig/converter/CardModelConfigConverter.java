package com.fenbei.fx.card.service.cardmodelconfig.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmodelconfig.po.CardModelConfigPO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡使用模式配置 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardModelConfigConverter extends BaseConverter<CardModelConfigDTO, CardModelConfigPO> {

    static CardModelConfigConverter me() {
        return SpringUtil.getBean(CardModelConfigConverter.class);
    }

    CardModelConfigDTO convertToCardModelConfigDTO(CardModelConfigAddReqDTO cardModelConfigAddReqDTO);

    CardModelConfigDTO convertToCardModelConfigDTO(CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO);

    CardModelConfigDTO convertToCardModelConfigDTO(CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO);

    CardModelConfigDTO convertToCardModelConfigDTO(CardModelConfigListReqDTO cardModelConfigListReqDTO);

    CardModelConfigDTO convertToCardModelConfigDTO(CardModelConfigPageReqDTO cardModelConfigPageReqDTO);

    CardModelConfigShowResDTO convertToCardModelConfigShowResDTO(CardModelConfigDTO cardModelConfigDTO);

    List<CardModelConfigShowResDTO> convertToCardModelConfigShowResDTOList(List<CardModelConfigDTO> cardModelConfigDTOList);

    CardModelConfigListResDTO convertToCardModelConfigListResDTO(CardModelConfigDTO cardModelConfigDTO);

    List<CardModelConfigListResDTO> convertToCardModelConfigListResDTOList(List<CardModelConfigDTO> cardModelConfigDTOList);

    List<CardModelConfigDTO> convertToCardModelConfigDTOList(List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList);

    CardModelConfigPageResDTO convertToCardModelConfigPageResDTO(CardModelConfigDTO cardModelConfigDTO);

    List<CardModelConfigPageResDTO> convertToCardModelConfigPageResDTOList(List<CardModelConfigDTO> cardModelConfigDTOList);

    Page<CardModelConfigPageResDTO> convertToCardModelConfigPageResDTOPage(Page<CardModelConfigDTO> cardModelConfigDTOPage);
}
