package com.fenbei.fx.card.service.card.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by FBT on 2023/4/18.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCanOperationDTO implements Serializable{

    /** 1：卡状态列表  2：审核列表*/
    private Integer type ;

    private boolean freezeFlag;

    private boolean openFlag;

    private boolean updateFlag;

    private boolean checkFlag;

    private boolean logoutFlag;

    private boolean deleteFlag;

    private boolean detailFlag;

    private String statusStr;


}
