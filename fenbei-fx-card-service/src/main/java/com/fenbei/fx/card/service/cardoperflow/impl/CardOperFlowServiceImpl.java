package com.fenbei.fx.card.service.cardoperflow.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardoperflow.po.CardOperFlowPO;
import com.fenbei.fx.card.service.cardoperflow.CardOperFlowService;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import com.fenbei.fx.card.service.cardoperflow.manager.CardOperFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 卡操作流水 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Service
public class CardOperFlowServiceImpl extends BaseServiceImpl<CardOperFlowManager, CardOperFlowPO, CardOperFlowDTO> implements CardOperFlowService {

    @Override
    public List<CardOperFlowListResDTO> list(final CardOperFlowListReqDTO cardOperFlowListReqDTO) {
        return manager.list(cardOperFlowListReqDTO);
    }

    @Override
    public CardOperFlowListResDTO listOne(final CardOperFlowListReqDTO cardOperFlowListReqDTO) {
        return manager.listOne(cardOperFlowListReqDTO);
    }

    @Override
    public Page<CardOperFlowPageResDTO> pagination(final CardOperFlowPageReqDTO cardOperFlowPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardOperFlowPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        return manager.add(cardOperFlowAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        return manager.addAllColumn(cardOperFlowAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList) {
        return manager.addBatchAllColumn(cardOperFlowAddReqDTOList);
    }

    @Override
    public CardOperFlowShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardOperFlowShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        return manager.modify(cardOperFlowModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        return manager.modifyAllColumn(cardOperFlowModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO) {
        return manager.removeByParams(cardOperFlowRemoveReqDTO);
    }
}
