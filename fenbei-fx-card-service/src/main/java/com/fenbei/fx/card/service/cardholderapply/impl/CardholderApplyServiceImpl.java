package com.fenbei.fx.card.service.cardholderapply.impl;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.CardholderApplyStatusEnum;
import com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDetailResDTO;
import com.fenbei.fx.card.service.cardholderapply.CardholderApplyService;
import com.fenbei.fx.card.service.cardholderapply.dto.*;
import com.fenbei.fx.card.service.cardholderapply.manager.CardholderApplyManager;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 持卡人操作申请 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Service
public class CardholderApplyServiceImpl extends BaseServiceImpl<CardholderApplyManager, CardholderApplyPO, CardholderApplyDTO> implements CardholderApplyService {

    @Override
    public CardholderDetailResDTO applyCreate(CardholderApplyAddReqDTO reqDTO) {
        CardholderApplyDTO applyDTO = manager.applyCreate(reqDTO);
        return manager.convert2CardholderDetailResDTO(applyDTO);
    }

    @Override
    public CardholderDetailResDTO applyModify(CardholderApplyAddReqDTO reqDTO) {
        CardholderApplyDTO applyDTO = manager.applyModify(reqDTO);
        return manager.convert2CardholderDetailResDTO(applyDTO);
    }

    @Override
    public Boolean approve(String id, Integer status, String reason) {
        return manager.applyApprove(id, status, reason);
    }

    /**
     * 处理银行处理中的数据
     */
    @Override
    public void handleBankDealingData(){
        manager.handleBankDealingData();

    }

    @Override
    public Boolean removeByApplyId(String applyId) {
        CardholderApplyDTO applyDTO = manager.findByApplyId(applyId);
        if (Objects.isNull(applyDTO) || CardholderApplyStatusEnum.isApplying(applyDTO.getApplyStatus())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.APPLY_NOT_EXIST);
        }
        return manager.removeById(applyDTO.getId());
    }
}
