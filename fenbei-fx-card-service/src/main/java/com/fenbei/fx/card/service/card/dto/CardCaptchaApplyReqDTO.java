package com.fenbei.fx.card.service.card.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 国际卡操作申请 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCaptchaApplyReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 卡ID
     */
    private String fxCardId;


    /**
     *  重置实体卡密码: reset_card_pin
     *  实体卡激活   : physcard_active
     */
    @NotNull
    private String busiType;


    /**
     * 不传默认MOBILE
     * 手机号:MOBILE
     * 邮件: EMAIL
     */
    private String sendType="MOBILE";
}
