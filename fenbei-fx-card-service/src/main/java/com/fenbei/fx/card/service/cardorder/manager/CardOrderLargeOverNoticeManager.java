package com.fenbei.fx.card.service.cardorder.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.fxpay.api.enums.ExchangeType;
import com.fenbeitong.fxpay.api.interfaces.IExchangeRateService;
import com.fenbeitong.fxpay.api.vo.ExchangeRateReq;
import com.fenbeitong.fxpay.api.vo.ExchangeRateRes;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupReceiverVO;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupVO;
import com.fenbeitong.saas.api.service.message.setting.IMessageSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CardOrderLargeOverNoticeManager {

    @Autowired
    CardService cardService;

    @Autowired
    CardOrderService cardOrderService;

    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;
    @Autowired
    public MessageService messageService;
    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;
    @DubboReference
    private IMessageSettingService iMessageSettingService;

    @DubboReference
    private  IExchangeRateService iExchangeRateService;

    /**
     * 员工海外卡使用通知，大额异动提醒
     */
    String ITEM_OVERSEA_LARGE_OVER_REMIND = "oversea_large_over_remind";


    public static String CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID = "67569ddf59aac212705316a4";


    /**
     this.setIs_check(setup.getIsChecked());
     this.setApp_notice(setup.getIntVal1());
     this.setMail_notice(setup.getIntVal2());
     this.setPhone_notice(setup.getIntVal3());
     Integer overAmount=null;
     try{
     overAmount=Integer.parseInt(setup.getStrVal1());
     }catch (NumberFormatException numberFormatException){
     overAmount=null;
     }catch(Exception exception){
     overAmount=null;
     }
     this.setAmount(overAmount);
     */

    public void sendNoticeMsgForLargeOver(String fxCardId, String bizNo,String tradeId,String subTradeId,Integer tradeType){
        CardDTO cardDTO = cardService.cardDetailByFxCardId(fxCardId);
        try {
            String title = "大额异动提醒";
            //查询配置
            FinhubLogger.info("海外卡大额异动提醒 查询消息配置信息 companyId = {}" ,cardDTO.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(cardDTO.getCompanyId(), Collections.singletonList(ITEM_OVERSEA_LARGE_OVER_REMIND));
            FinhubLogger.info("海外卡大额异动提醒 查询消息配置信息 companyId = {}，res = {}" ,cardDTO.getCompanyId() ,JSON.toJSONString(messageSetupVOS));
            if (messageSetupVOS != null && messageSetupVOS.size() > 0) {
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0) {
                    sendCompanyNoticeForLargeOver(messageSetupVO, cardDTO, bizNo, tradeId, subTradeId, tradeType, title);
                }
            }
        }catch (Exception e){
            FinhubLogger.warn("海外卡大额异动提醒通知消息推送异常，employeeId={},bizNo={}", cardDTO.getEmployeeId(), bizNo , e );
        }
    }


    private void sendCompanyNoticeForLargeOver(MessageSetupVO messageSetupVO,CardDTO cardDTO, String bizNo,String tradeId,String subTradeId,Integer tradeType,String title){
        boolean appWebFlag = (1 == messageSetupVO.getIntVal1());
        boolean emailFlag = (1 == messageSetupVO.getIntVal2());
        boolean smsFlag = (1 == messageSetupVO.getIntVal3());

        if(!appWebFlag&&!emailFlag&&!smsFlag){
            // 无可发送渠道
            FinhubLogger.info("海外卡大额异动提醒 无可发送渠道不发提醒 companyId = {}，res = {}" ,cardDTO.getCompanyId() ,JSON.toJSONString(messageSetupVO));
        }
        String overAmount = messageSetupVO.getStrVal1();
        if(StringUtils.isBlank(overAmount)){
            // 未设置金额不发提醒
            FinhubLogger.info("海外卡大额异动提醒 未设置金额不发提醒 companyId = {}，res = {}" ,cardDTO.getCompanyId() ,JSON.toJSONString(messageSetupVO));
            return ;
        }
        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(cardDTO.getCompanyId(),ITEM_OVERSEA_LARGE_OVER_REMIND);

        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)){
            return ;
        }
        List<String> userIdList = messageSetupReceiverVOS.stream().map(MessageSetupReceiverVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)){
            return;
        }

        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardDTO.getEmployeeId()), cardDTO.getCompanyId());
        EmployeeCompanyDto companyEmployeeHolder = employeeCompanyDtos.get(0);
        CardOrderDTO cardOrderDTO;
        if(StringUtils.isBlank(subTradeId)){
            cardOrderDTO = cardOrderService.findByTradeId(tradeId,tradeType);
        }else{
            cardOrderDTO = cardOrderService.findByTradeIdAndSub(tradeId,subTradeId,tradeType);
        }
        if(cardOrderDTO==null) {
            FinhubLogger.info("海外卡大额异动提醒 未获取到交易详情不发提醒 companyId = {}，cardOrderDTO = {}" ,cardDTO.getCompanyId(), JSON.toJSONString(cardOrderDTO));
            return ;
        }
        //阀值
        BigDecimal overAmountBig = BigDecimalUtils.obj2big(overAmount);
        //交易金额与币种
        String billTradeCurrencyFrom = cardOrderDTO.getBillTradeCurrency();
        BigDecimal billTradeAmount = cardOrderDTO.getBillTradeAmount();
        if(CurrencyEnum.CNY.getCurrencyCode().equalsIgnoreCase(billTradeCurrencyFrom)){
            BigDecimal billTradeAmountYuanCNY=BigDecimalUtils.fen2yuan(billTradeAmount);
            if(billTradeAmountYuanCNY.compareTo(overAmountBig)<=0){
                // 未设置金额不发提醒
                FinhubLogger.info("海外卡大额异动提醒 金额小于配置金额不发提醒 companyId = {}，billTradeAmountYuanCNY = {},overAmountBig={}" ,cardDTO.getCompanyId(), billTradeAmountYuanCNY, overAmountBig);
                return;
            }
        }else{
            ExchangeType exchangeType = ExchangeType.queryExchangeType(billTradeCurrencyFrom, CurrencyEnum.CNY.getCurrencyCode());
            ExchangeRateReq exchangeRateReq = new ExchangeRateReq();
            exchangeRateReq.setExchangeType(exchangeType);
            ResponseVo<ExchangeRateRes> exchangeRateResResponseVo = iExchangeRateService.queryExchangeRate(exchangeRateReq);

            if(exchangeRateResResponseVo.isSuccess()){
                BigDecimal rate = exchangeRateResResponseVo.getData().getRate();
                BigDecimal billTradeAmountCNY = billTradeAmount.multiply(rate);
                BigDecimal billTradeAmountYuanCNY=BigDecimalUtils.fen2yuan(billTradeAmountCNY);
                if(billTradeAmountYuanCNY.compareTo(overAmountBig)<=0){
                    // 未设置金额不发提醒
                    FinhubLogger.info("海外卡大额异动提醒 金额小于配置金额不发提醒 companyId = {}，billTradeAmountYuanCNY = {},overAmountBig={}" ,cardDTO.getCompanyId(), billTradeAmountYuanCNY, overAmountBig);
                    return;
                }
            }else{
                // 未设置金额不发提醒
                FinhubLogger.info("海外卡大额异动提醒 获取汇率回空不发提醒 companyId = {}，messageSetupVO = {},exchangeRateReq={}" ,cardDTO.getCompanyId() ,JSON.toJSONString(messageSetupVO),JSON.toJSONString(exchangeRateReq));
                return;
            }
        }


        String message = convertMsgForFailed4Other(cardDTO,companyEmployeeHolder,cardOrderDTO);
        if (appWebFlag){//APP通知
            userIdList.forEach(userId ->{
                try {
                    FinhubLogger.info("海外卡大额异动提醒发送站内信给配置人员  入参 req = {},msg = {}", userId , message);
                    sendWebPush4ForLargeOver(cardDTO.getCompanyId(),userId,cardDTO.getCardPlatform(),cardDTO.getBankCardNo(),message,title);
                    sendMsgCenter4ForLargeOver(cardDTO.getCompanyId(),userId,bizNo,message,title);
                    pushAlert4ForLargeOver(cardDTO.getCompanyId(),userId,bizNo,message,title,null);
                }catch (Exception e){
                    FinhubLogger.info("海外卡大额异动提醒发送站内通知给配置人员失败 userId = {} , message = {}", userId , message , e);
                }
            });
        }
        List<EmployeeCompanyDto> employeeOrgUnitDTOSOther = baseEmployeeExtService.queryEmployeeCompanyInfoListById(userIdList, cardDTO.getCompanyId());
        if (emailFlag){//邮件通知
            Set<String> mailSet = employeeOrgUnitDTOSOther.stream().map(EmployeeCompanyDto::getUserEmail).collect(Collectors.toSet());
            String companyName = employeeOrgUnitDTOSOther.get(0).getCompanyName();
            FinhubLogger.info("海外卡大额异动提醒发送邮件给配置人员  入参 req = {},msg = {}", mailSet , message);
            sendMail4ForLargeOver(mailSet,message,companyName);
        }
        if (smsFlag){//短信通知
            FinhubLogger.info("海外卡大额异动提醒发送短信给配置人员  入参 req = {},msg = {}", userIdList , message);
            employeeOrgUnitDTOSOther.forEach(employee ->{
                try {
                    sendMsg4ForLargeOver(employee.getUserPhone(),CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID,message);
                } catch (Exception e){
                    FinhubLogger.info("海外卡大额异动提醒发送短信给配置人员失败 employeeId = {} , message = {}", employee.getId(), message , e);
                }
            });
        }
    }


    private String convertMsgForFailed4Holder(CardDTO cardDTO,BigDecimal operAmount){
        String currency = cardDTO.getCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String message = MessageFormat.format("由于企业账户余额不足，您有{0}{1}海外卡额度下发失败，请联系管理员进行充值，余额充足后将为您重新下发额度。", currencyByCode.getSymbol(),BigDecimalUtils.fen2yuan(operAmount));
        return message;
    }

    private String convertMsgForFailed4Other(CardDTO cardDTO, EmployeeCompanyDto companyEmployeeHolder,CardOrderDTO cardOrderDTO){
        String bankCardNo = cardDTO.getBankCardNo();
        String bankCardNoSub = StringUtils.substring(bankCardNo, bankCardNo.length() - 4);

        String tradeTime = DateUtils.format(cardOrderDTO.getTradeTime(), DateUtils.FORMAT_DATETIME_HHMM);

        BigDecimal billTradeAmount = cardOrderDTO.getBillTradeAmount();

        String currency = cardOrderDTO.getBillTradeCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String message = MessageFormat.format("您好，{0}持有的尾号为{1}的海外卡，在{2}发生一笔金额为{3}{4}的交易，已高于您企业设定的大额异动提醒值。", companyEmployeeHolder.getUserName(),bankCardNoSub,tradeTime,BigDecimalUtils.fen2yuan(billTradeAmount),currencyByCode.getCurrencyCode());
        return message;
    }


    private void sendWebPush4ForLargeOver(String companyId , String employeeId , String bankName , String bankAccountNo, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.FxCardTrade.getCode());
            kafkaWebMessageMsg.setBizType(BizType.FxCardTrade.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(employeeId);
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", bankName);
            json.put("bankAccountNo",bankAccountNo);
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            FinhubLogger.info("海外卡大额异动提醒推送WebPush 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡大额异动提醒消息发送失败");
            e.printStackTrace();
        }
    }

    private void sendMsg4ForLargeOver(String phone, String tempId , String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡大额异动提醒,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡大额异动提醒，短信发送失败！！！");
            e2.printStackTrace();
        }
    }


    private void sendMsgCenter4ForLargeOver(String companyId , String employeeId , String bizNo , String msg , String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.FxCardTrade.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡大额异动提醒推送 MsgCenter 参数={}",JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡大额异动提醒消息中心发送失败！！！");
            e.printStackTrace();
        }
    }

    public void sendMail4ForLargeOver(Set<String> emailSet , String msg,String companyName) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email ->{
                if (EmailCheckUtils.emailFormat(email)){
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)){
                return ;
            }
            FinhubLogger.info("sendMail4ForLargeOver req = {},msg = {}", emailSet , msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】{0}海外卡大额异动提醒";
            String subjectFormat = MessageFormat.format(subject, companyName);
            emailContract.setSubject(subjectFormat);
            String text = "您好!" + "\n" + "感谢您使用分贝通的服务"+"\n"+ msg;
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        }catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}",emailSet,msg , e);
        }
    }

    private void pushAlert4ForLargeOver(String companyId , String employeeId , String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.FxCardTrade.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡大额异动提醒push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡大额异动提醒push消息参数msg失败:{}", msg,e);
        }
    }


}
