package com.fenbei.fx.card.service.card.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 国际卡操作申请 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPhyscardActiveReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 卡ID
     */
    @NotNull
    private String fxCardId;

    /**
     * 卡的cvv
     */
    @NotNull
    private String cardCvv;

    /**
     * 设置密码
     */
    @NotNull
    private String cardPin;
    /**
     * 验证码授权令牌
     */
    @NotNull
    private String token;

    /**
     * 验证码
     */
    @NotNull
    private String verifyCode;
}
