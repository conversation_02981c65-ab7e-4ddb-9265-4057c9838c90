package com.fenbei.fx.card.service.cardorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录 App 分页 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardTradeInfoAppPageReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 消费类型
     * 示例: 1-预定 4-预定释放 5-交易失败 11-消费 12-退款
     */
    private Integer transactionType;

    /**
     * 交易日期（大于等于）
     * 示例：2021-05-20 00:00:00
     */
    private Date tradeGeTime;

    /**
     * 交易日期（小于等于）
     * 示例：2021-05-20 23:59:59
     */
    private Date tradeLeTime;

    /**
     * 交易金额（大于等于）,单位分
     * 示例：100.00
     */
    private BigDecimal tradeGeAmount;

    /**
     * 交易金额（小于等于）,单位分
     * 示例：200.00
     */
    private BigDecimal tradeLeAmount;

    /**
     * 核销状态
     * 示例：0-无核销状态（如退款单，已全额退款的正向单） 2-未核销 3-核销中 4-已核销
     */
    private Integer checkStatus;

    private String bizNo;

    private boolean appShow;
}
