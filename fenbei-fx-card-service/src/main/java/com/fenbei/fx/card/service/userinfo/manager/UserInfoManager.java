package com.fenbei.fx.card.service.userinfo.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.core.str.StringUtils;

import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.dao.userinfo.UserInfoDAO;
import com.fenbei.fx.card.dao.userinfo.po.UserInfoPO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.userinfo.converter.UserInfoConverter;
import com.fenbei.fx.card.service.userinfo.domain.UserInfoDO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoAddReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoModifyReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoRemoveReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoShowResDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Slf4j
@Component
public class UserInfoManager extends BaseManagerImpl<UserInfoDAO, UserInfoPO, UserInfoDTO, UserInfoConverter> {

    @DubboReference
    private IRCompanyService companyService;

    @DubboReference
    private IBaseEmployeeExtService employeeService;

    public static UserInfoManager me() {
        return SpringUtil.getBean(UserInfoManager.class);
    }

    public List<UserInfoListResDTO> list(final UserInfoListReqDTO userInfoListReqDTO) {
        UserInfoDTO paramsDTO = UserInfoDO.me().buildListParamsDTO(userInfoListReqDTO);

        List<UserInfoDTO> userInfoDTOList = super.findList(paramsDTO);

        return UserInfoDO.me().transferUserInfoListResDTOList(userInfoDTOList);
    }

    public UserInfoListResDTO listOne(final UserInfoListReqDTO userInfoListReqDTO) {
        UserInfoDTO paramsDTO = UserInfoDO.me().buildListParamsDTO(userInfoListReqDTO);

        UserInfoDTO userInfoDTO = super.findOne(paramsDTO);

        return UserInfoDO.me().transferUserInfoListResDTO(userInfoDTO);
    }

    public Page<UserInfoPageResDTO> pagination(final UserInfoPageReqDTO userInfoPageReqDTO, final Integer current, final Integer size) {
        UserInfoDTO paramsDTO = UserInfoDO.me().buildPageParamsDTO(userInfoPageReqDTO);

        Page<UserInfoDTO> userInfoDTOPage = super.findPage(paramsDTO, current, size);

        return UserInfoDO.me().transferUserInfoPageResDTOPage(userInfoDTOPage);
    }

    public Boolean add(final UserInfoAddReqDTO userInfoAddReqDTO) {
        UserInfoDO.me().checkUserInfoAddReqDTO(userInfoAddReqDTO);

        UserInfoDTO addUserInfoDTO = UserInfoDO.me().buildAddUserInfoDTO(userInfoAddReqDTO);

        return super.saveDTO(addUserInfoDTO);
    }

    public Boolean addAllColumn(final UserInfoAddReqDTO userInfoAddReqDTO) {
        UserInfoDO.me().checkUserInfoAddReqDTO(userInfoAddReqDTO);

        UserInfoDTO addUserInfoDTO = UserInfoDO.me().buildAddUserInfoDTO(userInfoAddReqDTO);

        return super.saveAllColumn(addUserInfoDTO);
    }

    public Boolean addBatchAllColumn(final List<UserInfoAddReqDTO> userInfoAddReqDTOList) {
        UserInfoDO.me().checkUserInfoAddReqDTOList(userInfoAddReqDTOList);

        List<UserInfoDTO> addBatchUserInfoDTOList = UserInfoDO.me().buildAddBatchUserInfoDTOList(userInfoAddReqDTOList);

        return super.saveBatchAllColumn(addBatchUserInfoDTOList);
    }

    public UserInfoShowResDTO show(final String id) {
        UserInfoDTO userInfoDTO = super.findById(id);

        return UserInfoDO.me().transferUserInfoShowResDTO(userInfoDTO);
    }

    public List<UserInfoShowResDTO> showByIds(final List<String> ids) {
        UserInfoDO.me().checkIds(ids);

        List<UserInfoDTO> userInfoDTOList = super.findBatchIds(ids);

        return UserInfoDO.me().transferUserInfoShowResDTOList(userInfoDTOList);
    }

    public Boolean modify(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        UserInfoDO.me().checkUserInfoModifyReqDTO(userInfoModifyReqDTO);

        UserInfoDTO modifyUserInfoDTO = UserInfoDO.me().buildModifyUserInfoDTO(userInfoModifyReqDTO);

        return super.modifyById(modifyUserInfoDTO);
    }

    public Boolean modifyAllColumn(final UserInfoModifyReqDTO userInfoModifyReqDTO) {
        UserInfoDO.me().checkUserInfoModifyReqDTO(userInfoModifyReqDTO);

        UserInfoDTO modifyUserInfoDTO = UserInfoDO.me().buildModifyUserInfoDTO(userInfoModifyReqDTO);

        return super.modifyAllColumnById(modifyUserInfoDTO);
    }

    public Boolean removeByParams(final UserInfoRemoveReqDTO userInfoRemoveReqDTO) {
        UserInfoDO.me().checkUserInfoRemoveReqDTO(userInfoRemoveReqDTO);

        UserInfoDTO removeUserInfoDTO = UserInfoDO.me().buildRemoveUserInfoDTO(userInfoRemoveReqDTO);

        return super.remove(removeUserInfoDTO);
    }

    public String backupUserSafeInfo() {
        QueryWrapper<UserInfoPO> userInfoPOQueryWrapper = new QueryWrapper<>();
        userInfoPOQueryWrapper.select(UserInfoPO.DB_COL_FX_CARD_ID);
        List<UserInfoDTO> userInfoDTOList = findList(userInfoPOQueryWrapper);

        Set<String> fxCardIdSet = Sets.newHashSet();
        if (Func.isNotEmpty(userInfoDTOList)) {
            fxCardIdSet = userInfoDTOList.stream().map(UserInfoDTO::getFxCardId).collect(Collectors.toSet());
        }

        QueryWrapper<CardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.select(CardPO.DB_COL_ID, CardPO.DB_COL_FX_CARD_ID);
        List<CardDTO> cardDTOList = CardManager.me().findList(cardPOQueryWrapper);

        List<UserInfoDTO> userInfoDTOBatchList = Lists.newArrayList();
        if (Func.isNotEmpty(cardDTOList)) {
            for (CardDTO cardDTO : cardDTOList) {
                try {
                    String fxCardId = cardDTO.getFxCardId();
                    if (fxCardIdSet.contains(fxCardId)) {
                        continue;
                    }

                    CardDTO filledCardDTO = CardManager.me().findById(cardDTO.getId());
                    Company company = companyService.getByEmployeeId(filledCardDTO.getEmployeeId());
                    EmployeeContract employee = employeeService.queryEmployeeInfo(filledCardDTO.getEmployeeId(), filledCardDTO.getCompanyId());
                    if(ObjectUtils.isEmpty(filledCardDTO)||ObjectUtils.isEmpty(company)||ObjectUtils.isEmpty(employee)){
                        continue;
                    }
                    userInfoDTOBatchList.add(transferUserInfoDTO(filledCardDTO, company, employee));
                } catch (Exception e) {
                    log.warn("backupUserSafeInfo error, cardDTO: {}", cardDTO, e);
                }
            }

            if (Func.isNotEmpty(userInfoDTOBatchList)) {
                super.saveBatchAllColumn(userInfoDTOBatchList);
            }
        }

        return "成功备份" + userInfoDTOBatchList.size() + "条用户数据";
    }

    private UserInfoDTO transferUserInfoDTO(CardDTO filledCardDTO, Company company, EmployeeContract employee) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(employee.getEmployee_id());
        userInfoDTO.setUserName(StringUtils.toStringOrEmpty(employee.getName()));
        userInfoDTO.setUserPhone(StringUtils.toStringOrEmpty(employee.getPhone_num()));
        userInfoDTO.setUserEmail(StringUtils.toStringOrEmpty(employee.getEmail()));
        userInfoDTO.setIdCardNo(StringUtils.toStringOrEmpty(employee.getId_number()));
        userInfoDTO.setEmployeeLocation(StringUtils.toStringOrEmpty(employee.getEmployeeLocation()));
        userInfoDTO.setCompanyId(StringUtils.toStringOrEmpty(employee.getCompany_id()));
        userInfoDTO.setCompanyName(StringUtils.toStringOrEmpty(employee.getCompany_name()));
        userInfoDTO.setCertificateNum(StringUtils.toStringOrEmpty(company.getCertificateNum()));
        userInfoDTO.setOfficeAddress(StringUtils.toStringOrEmpty(company.getOfficeAddress()));
        userInfoDTO.setCompanyAccountId(StringUtils.toStringOrEmpty(filledCardDTO.getCompanyAccountId()));
        userInfoDTO.setFxCardId(StringUtils.toStringOrEmpty(filledCardDTO.getFxCardId()));
        userInfoDTO.setCardPlatform(StringUtils.toStringOrEmpty(filledCardDTO.getCardPlatform()));
        userInfoDTO.setCardBrand(StringUtils.toStringOrEmpty(filledCardDTO.getCardBrand()));
        userInfoDTO.setBankCardId(StringUtils.toStringOrEmpty(filledCardDTO.getBankCardId()));
        userInfoDTO.setBankCardNo(StringUtils.toStringOrEmpty(filledCardDTO.getBankCardNo()));
        userInfoDTO.setCardPublicTime(filledCardDTO.getCardPublicTime());
        userInfoDTO.setCardPurpose(StringUtils.toStringOrEmpty(filledCardDTO.getCardPurpose()));
        userInfoDTO.setCardCvv(StringUtils.toStringOrEmpty(filledCardDTO.getCardCvv()));
        userInfoDTO.setNameOnCard(StringUtils.toStringOrEmpty(filledCardDTO.getNameOnCard()));
        userInfoDTO.setCardExpiryMonth(StringUtils.toStringOrEmpty(filledCardDTO.getCardExpiryMonth()));
        userInfoDTO.setCardExpiryYear(StringUtils.toStringOrEmpty(filledCardDTO.getCardExpiryYear()));
        return userInfoDTO;
    }

    @Override
    protected UserInfoPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new UserInfoPO();
        }

        return BeanUtil.toBean(map, UserInfoPO.class);
    }

    @Override
    protected UserInfoDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new UserInfoDTO();
        }

        return BeanUtil.toBean(map, UserInfoDTO.class);
    }
}
