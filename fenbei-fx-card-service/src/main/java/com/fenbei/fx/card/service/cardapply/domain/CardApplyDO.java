package com.fenbei.fx.card.service.cardapply.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;
import com.fenbei.fx.card.util.I18nUtils;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardapply.po.CardApplyPO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyAddReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyModifyReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyRemoveReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyShowResDTO;
import com.fenbei.fx.card.service.cardapply.converter.CardApplyConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡操作申请 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardApplyDO extends BaseDO<CardApplyDTO, CardApplyPO, CardApplyConverter> {

    public static CardApplyDO me() {
        return SpringUtil.getBean(CardApplyDO.class);
    }

    public void checkCardApplyAddReqDTO(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        if (Func.isEmpty(cardApplyAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardApplyAddReqDTOList(final List<CardApplyAddReqDTO> cardApplyAddReqDTOList) {
        if (Func.isEmpty(cardApplyAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardApplyModifyReqDTO(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        if (Func.isEmpty(cardApplyModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardApplyRemoveReqDTO(final CardApplyRemoveReqDTO cardApplyRemoveReqDTO) {
        if (Func.isEmpty(cardApplyRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardApplyDTO buildListParamsDTO(final CardApplyListReqDTO cardApplyListReqDTO) {
        return converter.convertToCardApplyDTO(cardApplyListReqDTO);
    }

    public CardApplyDTO buildPageParamsDTO(final CardApplyPageReqDTO cardApplyPageReqDTO) {
        return converter.convertToCardApplyDTO(cardApplyPageReqDTO);
    }

    public CardApplyDTO buildAddCardApplyDTO(final CardApplyAddReqDTO cardApplyAddReqDTO) {
        return converter.convertToCardApplyDTO(cardApplyAddReqDTO);
    }

    public List<CardApplyDTO> buildAddBatchCardApplyDTOList(final List<CardApplyAddReqDTO> cardApplyAddReqDTOList) {
        return converter.convertToCardApplyDTOList(cardApplyAddReqDTOList);
    }

    public CardApplyDTO buildModifyCardApplyDTO(final CardApplyModifyReqDTO cardApplyModifyReqDTO) {
        return converter.convertToCardApplyDTO(cardApplyModifyReqDTO);
    }

    public CardApplyDTO buildRemoveCardApplyDTO(final CardApplyRemoveReqDTO cardApplyRemoveReqDTO) {
        return converter.convertToCardApplyDTO(cardApplyRemoveReqDTO);
    }

    public List<CardApplyListResDTO> transferCardApplyListResDTOList(final List<CardApplyDTO> cardApplyDTOList) {
        if (Func.isEmpty(cardApplyDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardApplyListResDTOList(cardApplyDTOList);
    }

    public CardApplyListResDTO transferCardApplyListResDTO(final CardApplyDTO cardApplyDTO) {
        if (Func.isEmpty(cardApplyDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardApplyListResDTO(cardApplyDTO);
    }

    public Page<CardApplyPageResDTO> transferCardApplyPageResDTOPage(final Page<CardApplyDTO> cardApplyDTOPage) {
        if (Func.isEmpty(cardApplyDTOPage) || Func.isEmpty(cardApplyDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardApplyPageResDTOPage(cardApplyDTOPage);
    }

    public CardApplyShowResDTO transferCardApplyShowResDTO(final CardApplyDTO cardApplyDTO) {
        if (Func.isEmpty(cardApplyDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardApplyShowResDTO(cardApplyDTO);
    }

    public List<CardApplyShowResDTO> transferCardApplyShowResDTOList(final List<CardApplyDTO> cardApplyDTOList) {
        if (Func.isEmpty(cardApplyDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardApplyShowResDTOList(cardApplyDTOList);
    }
}
