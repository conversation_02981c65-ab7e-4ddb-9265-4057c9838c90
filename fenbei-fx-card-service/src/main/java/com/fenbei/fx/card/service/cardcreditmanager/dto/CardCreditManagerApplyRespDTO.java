package com.fenbei.fx.card.service.cardcreditmanager.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CardCreditManagerApplyRespDTO implements Serializable {
    /**
     * 员工
     */
    protected String employeeId;
    /**
     * 公司ID
     */
    protected String companyId;
    /**
     * 海外卡卡ID
     */
    private String fxCardId;
    /**
     * 申请后卡余额
     */
    private BigDecimal cardBalance;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 申请额度单号
     */
    private String applyTransNo;
    /**
     * 额度申请结果 true为成功,false为失败
     */
    private Boolean applyStatus;
    /**
     * 失败原因
     */
    private String failedReason;
}
