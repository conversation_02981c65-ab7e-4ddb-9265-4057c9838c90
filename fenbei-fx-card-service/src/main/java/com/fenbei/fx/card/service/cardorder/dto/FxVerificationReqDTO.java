package com.fenbei.fx.card.service.cardorder.dto;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FxVerificationReqDTO {

    /**
     * 每个单号关联的核销金额
     */
    @NotNull
    private List<Item> items;

    /**
     * 关联的申请单
     */
    @NotNull
    private List<ApplyItem> applyItems;

    /**
     * 费用ID
     */
    //@NotNull
    //private Integer costId;

    /**
     * 核销单号
     */
    @NotNull
    private String verificationId;

    private String employeeId;

    private String fxCardId;

    @Data
    public static class Item{

        private String bizNo;

        /**
         * 单位（美元）
         */
        private BigDecimal amount;

        /**
         * 交易类型
         */
        private Integer type;

    }

    @Data
    public static class ApplyItem {

        /**
         * 申请单号
         */
        private String applyTransNo;

        /**
         * 申请单批次单号
         */
        private String applyTransBatchNo;

        /**
         * 单位（美元）
         */
        private BigDecimal amount;

    }

}
