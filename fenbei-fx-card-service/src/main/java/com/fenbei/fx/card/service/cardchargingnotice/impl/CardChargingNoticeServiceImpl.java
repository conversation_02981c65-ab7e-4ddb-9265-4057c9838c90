package com.fenbei.fx.card.service.cardchargingnotice.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.dao.cardchargingnotice.CardChargingNoticeDAO;
import com.fenbei.fx.card.dao.cardchargingnotice.po.CardChargingNoticePO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardShowResDTO;
import com.fenbei.fx.card.service.cardchargingnotice.CardChargingNoticeService;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeDTO;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeRetryDTO;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.fxpay.api.enums.charging.ChargingEventType;
import com.fenbeitong.fxpay.api.interfaces.IChargingService;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.charging.ChargingEventReq;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleInfoContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-31 下午3:15
 */
@Service
@Slf4j
public class CardChargingNoticeServiceImpl implements CardChargingNoticeService {

    @Resource
    CardChargingNoticeDAO cardChargingNoticeDAO;

    @DubboReference
    IChargingService iChargingService;

    @Resource @Lazy
    CardService cardService;

    @DubboReference
    IBaseEmployeeExtService iBaseEmployeeExtService;

    @Override
    public int saveChargingNotice(CardChargingNoticeDTO cardChargingNoticeDTO) {
        CardChargingNoticePO cardChargingNoticePO = buildCardChargingNoticePO(cardChargingNoticeDTO);
        int insert = 0;
        try {
            insert = cardChargingNoticeDAO.insert(cardChargingNoticePO);
        }catch (Exception e){
            log.info("saveChargingNotice error: {}", JsonUtils.toJson(cardChargingNoticeDTO));
            return 0;
        }

        CompletableFuture.runAsync(() -> {
            //fx-pay接收计费通知
            ChargingEventReq chargingEventReq = buildChargingReq(cardChargingNoticePO);
            try {
                log.info("iChargingService.charging param{}", JsonUtils.toJson(chargingEventReq));
                ResponseVo<String> charging = iChargingService.charging(chargingEventReq);
                log.info("iChargingService.charging resp{}", JsonUtils.toJson(charging));
                if(charging.getCode().equals("000000")){
                    //不报错更新通知状态为成功
                    QueryWrapper<CardChargingNoticePO> updateWrapper = new QueryWrapper<>();
                    updateWrapper.eq(CardChargingNoticePO.DB_COL_REQUEST_ID,cardChargingNoticePO.getRequestId());
                    CardChargingNoticePO update = new CardChargingNoticePO();
                    update.setCallStatus(1);
                    cardChargingNoticeDAO.update(update,updateWrapper);
                }else{
                    updateFail(cardChargingNoticePO.getRequestId(),1);
                }
            }catch(Exception e){
                log.error("iChargingService.charging err=",e);
                updateFail(cardChargingNoticePO.getRequestId(),1);
            }
        });
        return insert;
    }

    private void updateFail(String requestId,Integer callNum){
        //更新通知状态
        QueryWrapper<CardChargingNoticePO> updateWrapper = new QueryWrapper<>();
        updateWrapper.eq(CardChargingNoticePO.DB_COL_REQUEST_ID,requestId);
        CardChargingNoticePO update = new CardChargingNoticePO();
        update.setCallStatus(2);
        update.setCallNum(callNum);
        update.setCallNextTime(DateUtils.addMinute(new Date(),callNum*10));
        cardChargingNoticeDAO.update(update,updateWrapper);
    }

    private ChargingEventReq buildChargingReq(CardChargingNoticePO cardChargingNoticePO) {
        ChargingEventReq chargingEventReq = new ChargingEventReq();
        chargingEventReq.setRequestId(cardChargingNoticePO.getRequestId());
        chargingEventReq.setEventType(cardChargingNoticePO.getEventType());
        chargingEventReq.setCompanyId(cardChargingNoticePO.getCompanyId());
        chargingEventReq.setEmployeeId(cardChargingNoticePO.getEmployeeId());
        EmployeeSimpleInfoContract employeeInfoByEmloyeeId = iBaseEmployeeExtService.getEmployeeInfoByEmloyeeId(cardChargingNoticePO.getEmployeeId());
        chargingEventReq.setCompanyName(employeeInfoByEmloyeeId.getCompany_name());
        chargingEventReq.setEmployeeName(employeeInfoByEmloyeeId.getUser_name());
        CardShowResDTO cardShowResDTO = cardService.cardDetail(cardChargingNoticePO.getFxCardId());
        chargingEventReq.setChannel(cardShowResDTO.getCardPlatform());
        chargingEventReq.setCurrency(cardShowResDTO.getCurrency());
        chargingEventReq.setFeatures(buildChargingFeatures(cardChargingNoticePO).toJSONString());
        chargingEventReq.setEventTime(cardChargingNoticePO.getCreateTime());
        return chargingEventReq;
    }

    private JSONObject buildChargingFeatures(CardChargingNoticePO cardChargingNoticePO) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fxCardId",cardChargingNoticePO.getFxCardId());
        jsonObject.put("cardStatus",cardChargingNoticePO.getCardStatus());
        if(ChargingEventType.FXCARD_CONSUME.getCode()==cardChargingNoticePO.getEventType()){
            jsonObject.put("tradeAmount",cardChargingNoticePO.getTradeAmount());
        }
        return jsonObject;
    }

    private CardChargingNoticePO buildCardChargingNoticePO(CardChargingNoticeDTO cardChargingNoticeDTO) {
        CardChargingNoticePO cardChargingNoticePO = new CardChargingNoticePO();
        cardChargingNoticePO.setRequestId(cardChargingNoticeDTO.getRequestId());
        cardChargingNoticePO.setEmployeeId(cardChargingNoticeDTO.getEmployeeId());
        cardChargingNoticePO.setCompanyId(cardChargingNoticeDTO.getCompanyId());
        cardChargingNoticePO.setFxCardId(cardChargingNoticeDTO.getFxCardId());
        cardChargingNoticePO.setEventType(cardChargingNoticeDTO.getEventType());
        cardChargingNoticePO.setTradeAmount(cardChargingNoticeDTO.getTradeAmount());
        cardChargingNoticePO.setCardStatus(cardChargingNoticeDTO.getCardStatus());
        cardChargingNoticePO.setCreateTime(new Date());
        return cardChargingNoticePO;
    }

    @Override
    public void noticeRetry(CardChargingNoticeRetryDTO cardChargingNoticeRetryDTO) {
        //查询状态为通知失败｜未通知
        List<CardChargingNoticePO> cardChargingNoticePOList = queryUnNoticeSucceed(cardChargingNoticeRetryDTO);
        if(!CollectionUtils.isEmpty(cardChargingNoticePOList)){
            for (CardChargingNoticePO cardChargingNoticePO : cardChargingNoticePOList) {
                ChargingEventReq chargingEventReq = buildChargingReq(cardChargingNoticePO);
                try {
                    log.info("iChargingService.charging param{}", JsonUtils.toJson(chargingEventReq));
                    ResponseVo<String> charging = iChargingService.charging(chargingEventReq);
                    log.info("iChargingService.charging resp{}", JsonUtils.toJson(charging));
                    if(charging.getCode().equals("000000")){
                        //不报错更新通知状态为成功
                        QueryWrapper<CardChargingNoticePO> updateWrapper = new QueryWrapper<>();
                        updateWrapper.eq(CardChargingNoticePO.DB_COL_REQUEST_ID,cardChargingNoticePO.getRequestId());
                        CardChargingNoticePO update = new CardChargingNoticePO();
                        update.setCallStatus(1);
                        cardChargingNoticeDAO.update(update,updateWrapper);
                    }else{
                        updateFailByPO(cardChargingNoticePO);
                    }
                }catch(Exception e){
                    log.error("iChargingService.charging err=",e);
                    updateFailByPO(cardChargingNoticePO);
                }
            }
        }
    }

    private void updateFailByPO(CardChargingNoticePO cardChargingNoticePO) {
        CardChargingNoticePO update = new CardChargingNoticePO();
        Integer callNum = cardChargingNoticePO.getCallNum();
        update.setCallNum(++callNum);
        update.setCallNextTime(DateUtils.addMinute(new Date(),update.getCallNum()*10));
        update.setId(cardChargingNoticePO.getId());
        cardChargingNoticeDAO.updateById(update);
    }

    private List<CardChargingNoticePO> queryUnNoticeSucceed(CardChargingNoticeRetryDTO cardChargingNoticeRetryDTO) {
        QueryWrapper<CardChargingNoticePO> cardChargingNoticePOQueryWrapper = new QueryWrapper<>();
        if(!cardChargingNoticeRetryDTO.getIsRetry()) {
            cardChargingNoticePOQueryWrapper.in(CardChargingNoticePO.DB_COL_CALL_STATUS, Lists.newArrayList(0, 2));
            cardChargingNoticePOQueryWrapper.le(CardChargingNoticePO.DB_COL_CALL_NUM, 5);
            cardChargingNoticePOQueryWrapper.le(CardChargingNoticePO.DB_COL_CALL_NEXT_TIME, new Date());
        }else{
            cardChargingNoticePOQueryWrapper.eq(CardChargingNoticePO.DB_COL_REQUEST_ID,cardChargingNoticeRetryDTO.getRequestId());
        }
        List<CardChargingNoticePO> cardChargingNoticePOS = cardChargingNoticeDAO.selectList(cardChargingNoticePOQueryWrapper);
        return cardChargingNoticePOS;
    }
}
