package com.fenbei.fx.card.service.cardemployeemodelconfig.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardemployeemodelconfig.CardEmployeeModelConfigDAO;
import com.fenbei.fx.card.dao.cardemployeemodelconfig.po.CardEmployeeModelConfigPO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.domain.CardEmployeeModelConfigDO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.converter.CardEmployeeModelConfigConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 国际卡员工使用模式配置 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardEmployeeModelConfigManager extends BaseManagerImpl<CardEmployeeModelConfigDAO, CardEmployeeModelConfigPO, CardEmployeeModelConfigDTO, CardEmployeeModelConfigConverter> {

    public static CardEmployeeModelConfigManager me() {
        return SpringUtil.getBean(CardEmployeeModelConfigManager.class);
    }

    public List<CardEmployeeModelConfigListResDTO> list(final CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO) {
        CardEmployeeModelConfigDTO paramsDTO = CardEmployeeModelConfigDO.me().buildListParamsDTO(cardEmployeeModelConfigListReqDTO);

        List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList = super.findList(paramsDTO);

        return CardEmployeeModelConfigDO.me().transferCardEmployeeModelConfigListResDTOList(cardEmployeeModelConfigDTOList);
    }

    public CardEmployeeModelConfigListResDTO listOne(final CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO) {
        CardEmployeeModelConfigDTO paramsDTO = CardEmployeeModelConfigDO.me().buildListParamsDTO(cardEmployeeModelConfigListReqDTO);

        CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO = super.findOne(paramsDTO);

        return CardEmployeeModelConfigDO.me().transferCardEmployeeModelConfigListResDTO(cardEmployeeModelConfigDTO);
    }

    public Page<CardEmployeeModelConfigPageResDTO> pagination(final CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO, final Integer current, final Integer size) {
        CardEmployeeModelConfigDTO paramsDTO = CardEmployeeModelConfigDO.me().buildPageParamsDTO(cardEmployeeModelConfigPageReqDTO);

        Page<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOPage = super.findPage(paramsDTO, current, size);

        return CardEmployeeModelConfigDO.me().transferCardEmployeeModelConfigPageResDTOPage(cardEmployeeModelConfigDTOPage);
    }

    public Boolean add(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigAddReqDTO(cardEmployeeModelConfigAddReqDTO);

        CardEmployeeModelConfigDTO addCardEmployeeModelConfigDTO = CardEmployeeModelConfigDO.me().buildAddCardEmployeeModelConfigDTO(cardEmployeeModelConfigAddReqDTO);

        return super.saveDTO(addCardEmployeeModelConfigDTO);
    }

    public Boolean addAllColumn(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigAddReqDTO(cardEmployeeModelConfigAddReqDTO);

        CardEmployeeModelConfigDTO addCardEmployeeModelConfigDTO = CardEmployeeModelConfigDO.me().buildAddCardEmployeeModelConfigDTO(cardEmployeeModelConfigAddReqDTO);

        return super.saveAllColumn(addCardEmployeeModelConfigDTO);
    }

    public Boolean addBatchAllColumn(final List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigAddReqDTOList(cardEmployeeModelConfigAddReqDTOList);

        List<CardEmployeeModelConfigDTO> addBatchCardEmployeeModelConfigDTOList = CardEmployeeModelConfigDO.me().buildAddBatchCardEmployeeModelConfigDTOList(cardEmployeeModelConfigAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardEmployeeModelConfigDTOList);
    }

    public CardEmployeeModelConfigShowResDTO show(final String id) {
        CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO = super.findById(id);

        return CardEmployeeModelConfigDO.me().transferCardEmployeeModelConfigShowResDTO(cardEmployeeModelConfigDTO);
    }

    public List<CardEmployeeModelConfigShowResDTO> showByIds(final List<String> ids) {
        CardEmployeeModelConfigDO.me().checkIds(ids);

        List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList = super.findBatchIds(ids);

        return CardEmployeeModelConfigDO.me().transferCardEmployeeModelConfigShowResDTOList(cardEmployeeModelConfigDTOList);
    }

    public Boolean modify(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigModifyReqDTO(cardEmployeeModelConfigModifyReqDTO);

        CardEmployeeModelConfigDTO modifyCardEmployeeModelConfigDTO = CardEmployeeModelConfigDO.me().buildModifyCardEmployeeModelConfigDTO(cardEmployeeModelConfigModifyReqDTO);

        return super.modifyById(modifyCardEmployeeModelConfigDTO);
    }

    public Boolean modifyAllColumn(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigModifyReqDTO(cardEmployeeModelConfigModifyReqDTO);

        CardEmployeeModelConfigDTO modifyCardEmployeeModelConfigDTO = CardEmployeeModelConfigDO.me().buildModifyCardEmployeeModelConfigDTO(cardEmployeeModelConfigModifyReqDTO);

        return super.modifyAllColumnById(modifyCardEmployeeModelConfigDTO);
    }

    public Boolean removeByParams(final CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO) {
        CardEmployeeModelConfigDO.me().checkCardEmployeeModelConfigRemoveReqDTO(cardEmployeeModelConfigRemoveReqDTO);

        CardEmployeeModelConfigDTO removeCardEmployeeModelConfigDTO = CardEmployeeModelConfigDO.me().buildRemoveCardEmployeeModelConfigDTO(cardEmployeeModelConfigRemoveReqDTO);

        return super.remove(removeCardEmployeeModelConfigDTO);
    }

    @Override
    protected CardEmployeeModelConfigPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardEmployeeModelConfigPO();
        }

        return BeanUtil.toBean(map, CardEmployeeModelConfigPO.class);
    }

    @Override
    protected CardEmployeeModelConfigDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardEmployeeModelConfigDTO();
        }

        return BeanUtil.toBean(map, CardEmployeeModelConfigDTO.class);
    }
}
