package com.fenbei.fx.card.common.enums;

import java.util.Objects;

public enum LianLianCardStatusEnum {

    //NORMAL-正常 CLOSED-已冻结 CANCEL-已注销 EXPIRED-已过期
    APPLY("APPLY","审批中"),
    NORMAL("NORMAL","正常"),
    CLOSED("CLOSED","已冻结"),
    CANCEL("CANCEL","已注销"),
    EXPIRED("EXPIRED","已过期"),

    ;

    LianLianCardStatusEnum(String status, String value){
        this.status = status;
        this.value = value;
    }

    private final String status;

    private final String value;

    public String getStatus() {
        return status;
    }

    public String getValue() {
        return value;
    }

    public static LianLianCardStatusEnum getEnum(String status) {
        for (LianLianCardStatusEnum item : values()) {
            if (Objects.equals(item.getStatus(), status)) {
                return item;
            }
        }
        return null;
    }
}
