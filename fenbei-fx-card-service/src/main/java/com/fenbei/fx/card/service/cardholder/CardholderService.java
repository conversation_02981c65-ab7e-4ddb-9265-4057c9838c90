package com.fenbei.fx.card.service.cardholder;

import com.fenbei.fx.card.service.cardholder.dto.*;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyAddReqDTO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;

import java.util.List;

/**
 * 持卡人 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardholderService extends BaseService<CardholderDTO> {

    static CardholderService me() {
        return SpringUtil.getBean(CardholderService.class);
    }


    Page<CardholderByPageResDTO> findCardholderOrApplyByPage(CardholderByPageReqDTO reqDTO);

    /**
     * 查询用户的持卡人信息（包括）
     * @return
     */
    CardholderOrApplyListResDTO findUserCardholderOrApply(CardholderOrApplyListReqDTO reqDto);


    CardholderDetailResDTO modify(CardholderModifyReqDTO reqDTO);

    /**
     * 全量比对
     */
    void allCompare();

    CardholderDetailResDTO detail(String applyId, String fxCardholderId, Integer showStatus);

    /**
     * 持卡人启用禁用
     * @param reqDTO
     */
    void enableSwitch(CardholderEnableReqDTO reqDTO);

}
