package com.fenbei.fx.card.service.cardmail.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailAddReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailRemoveReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 实体卡邮寄管理表 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardMailConverter extends BaseConverter<CardMailDTO, CardMailPO> {

    static CardMailConverter me() {
        return SpringUtil.getBean(CardMailConverter.class);
    }

    CardMailDTO convertToCardMailDTO(CardMailAddReqDTO cardMailAddReqDTO);

    CardMailDTO convertToCardMailDTO(CardMailModifyReqDTO cardMailModifyReqDTO);

    CardMailDTO convertToCardMailDTO(CardMailRemoveReqDTO cardMailRemoveReqDTO);

    CardMailDTO convertToCardMailDTO(CardMailListReqDTO cardMailListReqDTO);

    CardMailDTO convertToCardMailDTO(CardMailPageReqDTO cardMailPageReqDTO);

    CardMailShowResDTO convertToCardMailShowResDTO(CardMailDTO cardMailDTO);

    List<CardMailShowResDTO> convertToCardMailShowResDTOList(List<CardMailDTO> cardMailDTOList);

    CardMailListResDTO convertToCardMailListResDTO(CardMailDTO cardMailDTO);

    List<CardMailListResDTO> convertToCardMailListResDTOList(List<CardMailDTO> cardMailDTOList);

    List<CardMailDTO> convertToCardMailDTOList(List<CardMailAddReqDTO> cardMailAddReqDTOList);

    CardMailPageResDTO convertToCardMailPageResDTO(CardMailDTO cardMailDTO);

    List<CardMailPageResDTO> convertToCardMailPageResDTOList(List<CardMailDTO> cardMailDTOList);

    Page<CardMailPageResDTO> convertToCardMailPageResDTOPage(Page<CardMailDTO> cardMailDTOPage);
}
