package com.fenbei.fx.card.service.cardemployeemodelconfig;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;

import java.util.List;

/**
 * 国际卡员工使用模式配置 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
public interface CardEmployeeModelConfigService extends BaseService<CardEmployeeModelConfigDTO> {

    static CardEmployeeModelConfigService me() {
        return SpringUtil.getBean(CardEmployeeModelConfigService.class);
    }

    /**
     * 列表
     *
     * @param cardEmployeeModelConfigListReqDTO 入参DTO
     * @return
     */
    List<CardEmployeeModelConfigListResDTO> list(CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO);

    /**
     * First查询
     *
     * @param cardEmployeeModelConfigListReqDTO 入参DTO
     * @return
     */
    CardEmployeeModelConfigListResDTO listOne(CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO);

    /**
     * 分页
     *
     * @param cardEmployeeModelConfigPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardEmployeeModelConfigPageResDTO> pagination(CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardEmployeeModelConfigAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardEmployeeModelConfigAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardEmployeeModelConfigAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardEmployeeModelConfigShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardEmployeeModelConfigShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardEmployeeModelConfigModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardEmployeeModelConfigModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardEmployeeModelConfigRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO);
}
