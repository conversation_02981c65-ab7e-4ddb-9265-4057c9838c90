package com.fenbei.fx.card.service.cardmodelconfig.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-22 下午8:13
 */
@Data
public class EmployeeModelConfigDTO extends BaseDTO implements Serializable {

    private String companyId;

    private String employeeId;

    /**
     * 模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    private Integer modelType;

    /**
     * 国际卡企业生效模式: 1.普通模式,2.备用金模式
     */
    private Integer activeModel;


    /**
     * 模式名称， 1.普通模式,2.备用金模式
     */
    private String modelName;
}
