package com.fenbei.fx.card.service.cardverificationflow.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 核销记录表 分页 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardVerificationFlowPageReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 用户分贝通卡id
     */
    private String fxCardId;

    /**
     * 关联额度申请单ID
     */
    private String creditId;

    /**
     * 核销单单号
     */
    private String verificationId;

    /**
     * 本次核销金额
     */
    private BigDecimal checkAmount;

    /**
     * 核销状态： 1：核销  2：撤回
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
