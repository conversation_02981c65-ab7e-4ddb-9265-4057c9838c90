package com.fenbei.fx.card.service.userinfo;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoAddReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoModifyReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoRemoveReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoShowResDTO;

import java.util.List;

/**
 * 用户信息 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
public interface UserInfoService extends BaseService<UserInfoDTO> {

    static UserInfoService me() {
        return SpringUtil.getBean(UserInfoService.class);
    }

    /**
     * 列表
     *
     * @param userInfoListReqDTO 入参DTO
     * @return
     */
    List<UserInfoListResDTO> list(UserInfoListReqDTO userInfoListReqDTO);

    /**
     * First查询
     *
     * @param userInfoListReqDTO 入参DTO
     * @return
     */
    UserInfoListResDTO listOne(UserInfoListReqDTO userInfoListReqDTO);

    /**
     * 分页
     *
     * @param userInfoPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<UserInfoPageResDTO> pagination(UserInfoPageReqDTO userInfoPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param userInfoAddReqDTO 入参DTO
     * @return
     */
    Boolean add(UserInfoAddReqDTO userInfoAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param userInfoAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(UserInfoAddReqDTO userInfoAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param userInfoAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<UserInfoAddReqDTO> userInfoAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    UserInfoShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<UserInfoShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param userInfoModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(UserInfoModifyReqDTO userInfoModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param userInfoModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(UserInfoModifyReqDTO userInfoModifyReqDTO);

    /**
     * 参数删除
     *
     * @param userInfoRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(UserInfoRemoveReqDTO userInfoRemoveReqDTO);

    /**
     * 定期备份用户安全信息
     */
    String backupUserSafeInfo();

}
