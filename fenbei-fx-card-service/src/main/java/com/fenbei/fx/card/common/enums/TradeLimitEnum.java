package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 申请类型
 */
@AllArgsConstructor
public enum TradeLimitEnum {

    PER_TRANSACTION("PER_TRANSACTION", "单笔交易"),
    DAILY("DAILY", "日限额"),
    WEEKLY("WEEKLY", "周限额"),
    MONTHLY("MONTHLY", "月限额"),
    ALL_TIME("ALL_TIME", "总限额"),

    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("单笔交易", "trade.limit.per.transaction");
        I18N_KEY_MAP.put("日限额", "trade.limit.daily");
        I18N_KEY_MAP.put("周限额", "trade.limit.weekly");
        I18N_KEY_MAP.put("月限额", "trade.limit.monthly");
        I18N_KEY_MAP.put("总限额", "trade.limit.all.time");
    }

    private String code;

    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

}
