package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by FBT on 2023/4/18.
 * 实体卡激活状态
 */
@AllArgsConstructor
public enum CardActiveStatusEnum {

    /**
     * 0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    NOT_REQUIRE(0,"无需激活"),
    WAIT(1,"待激活"),
    DEALING(2,"激活中"),
    FAIL(3,"激活失败"),
    SUCCESS(4,"激活成功")
    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("无需激活", "card.active.status.not.require");
        I18N_KEY_MAP.put("待激活", "card.active.status.wait");
        I18N_KEY_MAP.put("激活中", "card.active.status.dealing");
        I18N_KEY_MAP.put("激活失败", "card.active.status.fail");
        I18N_KEY_MAP.put("激活成功", "card.active.status.success");
    }


    private  Integer status;

    private  String name;

    public static CardActiveStatusEnum get(Integer status){
        for (CardActiveStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item;
            }
        }

        return null;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }



}
