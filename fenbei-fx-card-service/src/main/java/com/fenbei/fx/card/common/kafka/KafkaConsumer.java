package com.fenbei.fx.card.common.kafka;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.fenbei.fx.card.common.threadpool.AirThreadPoolExecutor;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.consumer.KafkaConsumerUtils;
import com.fenbeitong.finhub.kafka.eventbus.Event;
import com.fenbeitong.finhub.kafka.eventbusmsg.crm.AccountChangeEvent;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCompanyCardAcctChangeMsg;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class KafkaConsumer {

    public static KafkaConsumer me() {
        return SpringUtil.getBean(KafkaConsumer.class);
    }


    @KafkaListener(topics = "fenbei_company_card_acct_change_msg")
    public void listenerAccountChange(ConsumerRecord<?, ?> record){
        log.info("【kafka消息 ===接收员工海外卡账户变动topic：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), record.value());
        KafkaCompanyCardAcctChangeMsg iMessage = KafkaConsumerUtils.invokeIMessage(record.value().toString(),
            KafkaCompanyCardAcctChangeMsg.class);
        log.info("虚拟卡账户资金变动,消息内容:{}", iMessage.toString());
        if (ObjUtils.isEmpty(iMessage)) {
            return;
        }
        if (FundAccountSubType.isOverseaAcct(iMessage.getAccountSubType())&&iMessage.getKey()==2) {
            CompletableFuture.runAsync(()->{
                try {
                    CardCreditManagerManager.me().applyRetry(iMessage);
                } catch (Exception e){
                    FinhubLogger.error("异步调用重试操作，iMessage={}", JsonUtils.toJson(iMessage), e);
                }
            }, AirThreadPoolExecutor.cardRetryExecutorInstance);

        }
    }


    @KafkaListener(topics = "account_changed_event")
    public void consumerListener(ConsumerRecord<?, ?> record) {
        String body = ObjUtils.toString(record.value());
        log.info("当前 {} 监听kafka消息体: {} ", this.getClass(), body);
        log.info("【kafka消息 ===接收员工海外卡账户变动topic：{},分区：{}】消息内容如下：{}", record.topic(), record.partition(), body);
        Event event = JSON.parseObject(body, Event.class);
        AccountChangeEvent data = JSON.parseObject(JSON.toJSONString(event.getData()),AccountChangeEvent.class);
        if (ObjUtils.isEmpty(data)) {
            return;
        }
        if (FundAccountSubType.isOverseaAcct(data.getAccountSubType())&&data.getChangeType().getKey()==2) {
            CompletableFuture.runAsync(()->{
                KafkaCompanyCardAcctChangeMsg  cardAcctChangeMsg = new KafkaCompanyCardAcctChangeMsg();
                try {
                    cardAcctChangeMsg.setCompanyAcctModelSwitch(true);
                    cardAcctChangeMsg.setAccountSubType(data.getAccountSubType());
                    String companyId = data.getCompanyId();
                    String[] split = companyId.split(",");
                    companyId=split[0];
                    String bankAcctId =split[1];
                    String bankAcctName =split[2];
                    cardAcctChangeMsg.setCompanyId(companyId);
                    cardAcctChangeMsg.setBankName(bankAcctName);
                    cardAcctChangeMsg.setAccountModel(data.getAccountModel());
                    cardAcctChangeMsg.setKey(data.getChangeType().getKey());
                    cardAcctChangeMsg.setBalance(data.getCredit());
                    cardAcctChangeMsg.setOrderType(data.getOrderType());
                    cardAcctChangeMsg.setOperationAmount(data.getOperationAmount());
                    cardAcctChangeMsg.setName(data.getName());
                    CardCreditManagerManager.me().applyRetry(cardAcctChangeMsg);
                } catch (Exception e){
                    FinhubLogger.error("异步调用重试操作，iMessage={}", JsonUtils.toJson(cardAcctChangeMsg), e);
                }
            }, AirThreadPoolExecutor.cardRetryExecutorInstance);

        }
    }

}
