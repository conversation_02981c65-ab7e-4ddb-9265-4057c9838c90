package com.fenbei.fx.card.service.cardemployeemodelconfig.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardemployeemodelconfig.po.CardEmployeeModelConfigPO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.converter.CardEmployeeModelConfigConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡员工使用模式配置 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardEmployeeModelConfigDO extends BaseDO<CardEmployeeModelConfigDTO, CardEmployeeModelConfigPO, CardEmployeeModelConfigConverter> {

    public static CardEmployeeModelConfigDO me() {
        return SpringUtil.getBean(CardEmployeeModelConfigDO.class);
    }

    public void checkCardEmployeeModelConfigAddReqDTO(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        if (Func.isEmpty(cardEmployeeModelConfigAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardEmployeeModelConfigAddReqDTOList(final List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList) {
        if (Func.isEmpty(cardEmployeeModelConfigAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardEmployeeModelConfigModifyReqDTO(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        if (Func.isEmpty(cardEmployeeModelConfigModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardEmployeeModelConfigRemoveReqDTO(final CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO) {
        if (Func.isEmpty(cardEmployeeModelConfigRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardEmployeeModelConfigDTO buildListParamsDTO(final CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO) {
        return converter.convertToCardEmployeeModelConfigDTO(cardEmployeeModelConfigListReqDTO);
    }

    public CardEmployeeModelConfigDTO buildPageParamsDTO(final CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO) {
        return converter.convertToCardEmployeeModelConfigDTO(cardEmployeeModelConfigPageReqDTO);
    }

    public CardEmployeeModelConfigDTO buildAddCardEmployeeModelConfigDTO(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        return converter.convertToCardEmployeeModelConfigDTO(cardEmployeeModelConfigAddReqDTO);
    }

    public List<CardEmployeeModelConfigDTO> buildAddBatchCardEmployeeModelConfigDTOList(final List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList) {
        return converter.convertToCardEmployeeModelConfigDTOList(cardEmployeeModelConfigAddReqDTOList);
    }

    public CardEmployeeModelConfigDTO buildModifyCardEmployeeModelConfigDTO(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        return converter.convertToCardEmployeeModelConfigDTO(cardEmployeeModelConfigModifyReqDTO);
    }

    public CardEmployeeModelConfigDTO buildRemoveCardEmployeeModelConfigDTO(final CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO) {
        return converter.convertToCardEmployeeModelConfigDTO(cardEmployeeModelConfigRemoveReqDTO);
    }

    public List<CardEmployeeModelConfigListResDTO> transferCardEmployeeModelConfigListResDTOList(final List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList) {
        if (Func.isEmpty(cardEmployeeModelConfigDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardEmployeeModelConfigListResDTOList(cardEmployeeModelConfigDTOList);
    }

    public CardEmployeeModelConfigListResDTO transferCardEmployeeModelConfigListResDTO(final CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO) {
        return converter.convertToCardEmployeeModelConfigListResDTO(cardEmployeeModelConfigDTO);
    }

    public Page<CardEmployeeModelConfigPageResDTO> transferCardEmployeeModelConfigPageResDTOPage(final Page<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOPage) {
        if (Func.isEmpty(cardEmployeeModelConfigDTOPage) || Func.isEmpty(cardEmployeeModelConfigDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardEmployeeModelConfigPageResDTOPage(cardEmployeeModelConfigDTOPage);
    }

    public CardEmployeeModelConfigShowResDTO transferCardEmployeeModelConfigShowResDTO(final CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO) {
        if (Func.isEmpty(cardEmployeeModelConfigDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardEmployeeModelConfigShowResDTO(cardEmployeeModelConfigDTO);
    }

    public List<CardEmployeeModelConfigShowResDTO> transferCardEmployeeModelConfigShowResDTOList(final List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList) {
        if (Func.isEmpty(cardEmployeeModelConfigDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardEmployeeModelConfigShowResDTOList(cardEmployeeModelConfigDTOList);
    }
}
