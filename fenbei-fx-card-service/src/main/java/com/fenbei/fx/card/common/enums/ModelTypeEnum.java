package com.fenbei.fx.card.common.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 模式类型
 */
@AllArgsConstructor
public enum ModelTypeEnum {

    /**
     *  模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    COMPANY_UNIFY(1, "企业统一模式"),
    PERSON_CONFIG(2, "人员配置使用模式"),

    ;

    public static ModelTypeEnum getEnum(Integer code) {
        for (ModelTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
