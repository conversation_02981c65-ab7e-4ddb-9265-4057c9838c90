package com.fenbei.fx.card.common.enums;

import java.util.Objects;

public enum AirwallexTransactionStatusEnum {
    APPROVED("APPROVED","成功"),
    PENDING("PENDING","处理中"),
    FAILED("FAILED","失败"),

    ;

    AirwallexTransactionStatusEnum(String status, String value){
        this.status = status;
        this.value = value;
    }

    private final String status;

    private final String value;

    public String getStatus() {
        return status;
    }

    public String getValue() {
        return value;
    }

    public static AirwallexTransactionStatusEnum getEnum(String status) {
        for (AirwallexTransactionStatusEnum item : values()) {
            if (Objects.equals(item.getStatus(), status)) {
                return item;
            }
        }
        return null;
    }
}
