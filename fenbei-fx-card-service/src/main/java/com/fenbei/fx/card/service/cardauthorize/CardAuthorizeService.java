package com.fenbei.fx.card.service.cardauthorize;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;

import java.util.List;

/**
 * 国际卡授权表 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
public interface CardAuthorizeService extends BaseService<CardAuthorizeDTO> {

    static CardAuthorizeService me() {
        return SpringUtil.getBean(CardAuthorizeService.class);
    }

    /**
     * 列表
     *
     * @param cardAuthorizeListReqDTO 入参DTO
     * @return
     */
    List<CardAuthorizeListResDTO> list(CardAuthorizeListReqDTO cardAuthorizeListReqDTO);

    /**
     * First查询
     *
     * @param cardAuthorizeListReqDTO 入参DTO
     * @return
     */
    CardAuthorizeListResDTO listOne(CardAuthorizeListReqDTO cardAuthorizeListReqDTO);

    /**
     * 分页
     *
     * @param cardAuthorizePageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardAuthorizePageResDTO> pagination(CardAuthorizePageReqDTO cardAuthorizePageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardAuthorizeAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardAuthorizeAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardAuthorizeAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardAuthorizeShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardAuthorizeShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardAuthorizeModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardAuthorizeModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardAuthorizeRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO);

    CardAuthorizeDTO findByTradeId(String tradeId,String tradeType);

    CardAuthorizeDTO findByTradeIdAndSub(String tradeId, String subTradeId, String tradeType);
}
