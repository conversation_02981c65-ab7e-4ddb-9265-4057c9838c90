package com.fenbei.fx.card.service.usercard.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午2:38
 */
@Data
public class UserCardCreditChangeRecordDTO extends BaseDTO implements Serializable {

    /**
     * 发放记录
     */
     private List<UserCardCreditChangeGrantRecordDTO> grantRecordList;

    /**
     * 退还记录
     */
     private List<UserCardCreditChangeBackRecordDTO> backRecordList;
}
