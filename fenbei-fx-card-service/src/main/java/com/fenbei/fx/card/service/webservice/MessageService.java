package com.fenbei.fx.card.service.webservice;


import com.fenbei.fx.card.service.webservice.dto.CommonPushDto;
import com.fenbei.fx.card.service.webservice.dto.PushAlertDto;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.eventbus.event.common.IEvent;

import java.io.IOException;

public interface MessageService<T> {

    /**
     * 推送到友盟push服务
     * @param dto
     * @throws IOException
     */
    void pushAlertMsg(PushAlertDto dto) throws IOException;

    /**
     * 推送Event,到kafka
     * @param iEvent
     * @throws IOException
     */
    void pushEventMsg(IEvent iEvent);

    /**
     * 发送手机短信
     * @param msgBody
     * @throws IOException
     */
    void pushSMS(SmsContract msgBody) throws IOException;


    boolean pushMsgAll(T entity, CommonPushDto pushDTO);


    /**
     * 发送邮件
     * @param emailContract
     */
    public void sendEmail(EmailContract emailContract);
}
