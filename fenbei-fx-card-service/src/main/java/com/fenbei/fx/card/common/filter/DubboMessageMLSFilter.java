package com.fenbei.fx.card.common.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.context.i18n.LocaleContextHolder;

@Slf4j
@Activate(group = {CommonConstants.CONSUMER})
public class DubboMessageMLSFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String info = LocaleContextHolder.getLocale().toString();
        //log.info("language info is {}",info);
        RpcContext.getContext().setAttachment("lang",info);
        return invoker.invoke(invocation);
    }
}
