package com.fenbei.fx.card.common.enums;

import java.util.Objects;

public enum AirwallexTransactionTypeEnum {
    AUTHORIZATION("AUTHORIZATION","冻结款",TransactionTypeEnum.PRE_AUTH),
    CLEARING("CLEARING","资金扣除",TransactionTypeEnum.CONSUME),
    REFUND("REFUND","退款",TransactionTypeEnum.REFUND),
    REVERSAL("REVERS<PERSON>","预授权冲正",TransactionTypeEnum.PRE_AUTH_RELEASE),
    ORIGINAL_CREDIT("ORIGINAL_CREDIT","订阅制扣费",TransactionTypeEnum.CONSUME)


    ;

    AirwallexTransactionTypeEnum(String transactionType,String value,TransactionTypeEnum transactionTypeEnum){
        this.transactionType = transactionType;
        this.value = value;
        this.transactionTypeEnum = transactionTypeEnum;
    }

    private final String transactionType;

    private final String value;

    private final TransactionTypeEnum transactionTypeEnum;

    public String getTransactionType() {
        return transactionType;
    }

    public String getValue() {
        return value;
    }

    public TransactionTypeEnum getTransactionTypeEnum() {
        return transactionTypeEnum;
    }

    public static AirwallexTransactionTypeEnum getEnum(String transactionType) {
        for (AirwallexTransactionTypeEnum item : values()) {
            if (Objects.equals(item.getTransactionType(), transactionType)) {
                return item;
            }
        }
        return null;
    }
}
