package com.fenbei.fx.card.util;

import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.finhub.framework.swift.utils.ObjUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class CurrencyNumberFormatUtil {
    private static String CURRENCY_SUB = "-";

    public CurrencyNumberFormatUtil() {
    }

    public static String moneyFormart(CurrencyEnum currencyEnum, BigDecimal price, Boolean type) {
        if (ObjUtils.isEmpty(currencyEnum)){
            currencyEnum=CurrencyEnum.USD;
        }
        DecimalFormat format = new DecimalFormat("##,##0.00");
        if (null == price) {
            return currencyEnum.getSymbol() + format.format(new BigDecimal(0));
        } else {
            return type ? currencyEnum.getSymbol() + format.format(price) : CURRENCY_SUB + currencyEnum.getSymbol() + format.format(price);
        }
    }

    public static String moneyFormart(CurrencyEnum currencyEnum, BigDecimal price) {
        if (ObjUtils.isEmpty(currencyEnum)){
            currencyEnum=CurrencyEnum.USD;
        }
        DecimalFormat format = new DecimalFormat("##,##0.00");
        if (null == price) {
            return currencyEnum.getSymbol() + format.format(new BigDecimal(0));
        } else if (price.compareTo(BigDecimal.ZERO) >= 0) {
            return currencyEnum.getSymbol() + format.format(price);
        } else {
            BigDecimal priceABS = price.abs();
            return CURRENCY_SUB + currencyEnum.getSymbol() + format.format(priceABS);
        }
    }


    public static void main(String[] args) {
        String moneyFormart = moneyFormart(CurrencyEnum.USD, new BigDecimal(66.3));
        System.out.println(moneyFormart);
        String moneyFormartSub = moneyFormart(CurrencyEnum.USD, new BigDecimal(-66.3));
        System.out.println(moneyFormartSub);
        String moneyFormartCNY = moneyFormart(CurrencyEnum.CNY, new BigDecimal(66.3));
        System.out.println(moneyFormartCNY);
        String moneyFormartSubCNY = moneyFormart(CurrencyEnum.CNY, new BigDecimal(-66.3));
        System.out.println(moneyFormartSubCNY);

        String moneyFormartSubCNY2 = moneyFormart(null, new BigDecimal(-66.3));
        System.out.println(moneyFormartSubCNY2);

    }


}
