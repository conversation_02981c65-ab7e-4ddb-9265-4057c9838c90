package com.fenbei.fx.card.util;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ValidateUtils;

public class ValidateCommonUtils {

    public static <T> T validate(T obj) {

        try {
            ValidateUtils.validate(obj);
        } catch (FinhubException e){
            throw e;
        } catch (ValidateException e){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getType(),e.getMessage());
        } catch (Exception e){
            throw e;
        }
        return obj;
    }
}
