package com.fenbei.fx.card.service.cardholder.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
@Data
public class CardholderByPageResDTO implements Serializable {

    private static final long serialVersionUID = 7537907060983286618L;

    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    private String employeeId;

    private String name;

    private String phone;

    /**
     * 展示的状态描述
     */
    private String statusStr;

    /**
     * 展示的状态
     */
    private Integer status;


    /**
     * 展示的状态描述
     */
    private String showStatusStr;

    /**
     * 展示的状态
     */
    private Integer showStatus;

    /**
     * 失败原因
     */
    private String refuseReason;


    private Date createTime;


    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    private String cardPlatformName;
}
