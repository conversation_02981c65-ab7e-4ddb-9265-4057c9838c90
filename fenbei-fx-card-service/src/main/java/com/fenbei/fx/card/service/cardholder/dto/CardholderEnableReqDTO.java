package com.fenbei.fx.card.service.cardholder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 持卡人 启用禁用开关
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderEnableReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 启用禁用 1启用 2禁用
     * @CardholderStatusEnum
     */
    private Integer holderStatus;

}
