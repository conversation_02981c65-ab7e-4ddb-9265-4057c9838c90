package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 国际卡额度发放单 修改 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderTrySendReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;


    /**
     * 单据主键ID
     */
    @NotBlank
    private String applyOrderId;



}
