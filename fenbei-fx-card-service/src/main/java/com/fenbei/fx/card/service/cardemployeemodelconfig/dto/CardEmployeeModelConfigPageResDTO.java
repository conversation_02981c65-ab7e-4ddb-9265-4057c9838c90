package com.fenbei.fx.card.service.cardemployeemodelconfig.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡员工使用模式配置 分页 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardEmployeeModelConfigPageResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 国际卡员工生效模式: 1.普通模式,2.备用金模式
     */
    private Integer activeModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
