package com.fenbei.fx.card.service.usercard;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardShowResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListResDTO;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.*;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.manager.CardCreditManagerRelationManager;
import com.fenbei.fx.card.service.cardemployeemodelconfig.CardEmployeeModelConfigService;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderByPageReqDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderByPageResDTO;
import com.fenbei.fx.card.service.cardholder.manager.CardholderManager;
import com.fenbei.fx.card.service.cardmodelconfig.CardModelConfigService;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListResDTO;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.service.usercard.dto.*;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.util.DateFormatUtil;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbei.fx.card.util.I18nUtils;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.finhub.framework.core.page.Page;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fenbeitong.finhub.common.utils.DateUtils.FORMAT_DATETIME_WITH_SLASH;

/**
 * <AUTHOR>
 * @description 额度申请manager
 * @date 2023-05-20 下午2:48
 */
@Slf4j
@Component
public class UserCardCreditManager {

    public static UserCardCreditManager me() {
        return SpringUtil.getBean(UserCardCreditManager.class);
    }

    @Autowired
    private CardholderManager cardholderManager;

    @Autowired
    private CardService cardService;

    @Autowired
    private CardEmployeeModelConfigService cardEmployeeModelConfigService;

    @Autowired
    private CardCreditManagerManager cardCreditManagerManager;

    @Autowired
    private CardCreditManagerService cardCreditManagerService;

    @Autowired
    private CardModelConfigService cardModelConfigService;

    @Autowired
    private BankCardFlowService bankCardFlowService;

    @Autowired
    private CardApplyManager cardApplyManager;

    public UserCardCreditChangeRecordDTO cardCreditChangeRecordInfo(String fxCardId){
        UserCardCreditChangeRecordDTO userCardCreditChangeRecordDTO = new UserCardCreditChangeRecordDTO();
        CardCreditManagerListReqDTO cardCreditManagerListReqDTO = new CardCreditManagerListReqDTO();
        cardCreditManagerListReqDTO.setFxCardId(fxCardId);
        cardCreditManagerListReqDTO.setDeleteFlag(0);
        List<CardCreditManagerListResDTO> cardCreditManagerListResDTOS = cardCreditManagerManager.list(cardCreditManagerListReqDTO);
        if(!CollectionUtils.isEmpty(cardCreditManagerListResDTOS)){
            List<CardCreditManagerListResDTO> grantList = cardCreditManagerListResDTOS.stream().filter(cardCreditManagerListResDTO -> cardCreditManagerListResDTO.getApplyType().equals(CreditApplyTypeEnum.APPLY.getCode())).collect(Collectors.toList());
            userCardCreditChangeRecordDTO.setGrantRecordList(buildGrantRecordList(grantList));
            List<CardCreditManagerListResDTO> returnList = cardCreditManagerListResDTOS.stream().filter(cardCreditManagerListResDTO -> cardCreditManagerListResDTO.getApplyType().equals(CreditApplyTypeEnum.RETURN.getCode())).collect(Collectors.toList());
            userCardCreditChangeRecordDTO.setBackRecordList(buildReturnList(returnList));
        }
        return userCardCreditChangeRecordDTO;
    }

    private List<UserCardCreditChangeBackRecordDTO> buildReturnList(List<CardCreditManagerListResDTO> returnList) {
        List<UserCardCreditChangeBackRecordDTO> userCardCreditChangeBackRecordDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(returnList)){
            for (CardCreditManagerListResDTO cardCreditManagerListResDTO : returnList) {
                UserCardCreditChangeBackRecordDTO userCardCreditChangeBackRecordDTO = new UserCardCreditChangeBackRecordDTO();
                userCardCreditChangeBackRecordDTO.setRecordId(String.valueOf(cardCreditManagerListResDTO.getId()));
                userCardCreditChangeBackRecordDTO.setBackTitle(I18nUtils.transferI18nMessage("退还额度"));
                userCardCreditChangeBackRecordDTO.setBackTime(DateFormatUtil.tradeDateFormat(cardCreditManagerListResDTO.getCreateTime()));
                userCardCreditChangeBackRecordDTO.setBackAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(cardCreditManagerListResDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan((cardCreditManagerListResDTO.getAmount()))));
                userCardCreditChangeBackRecordDTO.setOperationTypeDesc(CardFlowOperationTypeEnum.getEnum(cardCreditManagerListResDTO.getOperationType()).getName());
                userCardCreditChangeBackRecordDTO.setApplyTransNo(cardCreditManagerListResDTO.getApplyTransNo());
                userCardCreditChangeBackRecordDTO.setOriApplyTransNo(cardCreditManagerListResDTO.getOriApplyTransNo());
                userCardCreditChangeBackRecordDTOS.add(userCardCreditChangeBackRecordDTO);
            }
        }
        return userCardCreditChangeBackRecordDTOS;
    }

    private List<UserCardCreditChangeGrantRecordDTO> buildGrantRecordList(List<CardCreditManagerListResDTO> grantList) {
        List<UserCardCreditChangeGrantRecordDTO> userCardCreditChangeGrantRecordDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(grantList)){
            for (CardCreditManagerListResDTO cardCreditManagerListResDTO : grantList) {
                UserCardCreditChangeGrantRecordDTO userCardCreditChangeGrantRecordDTO = new UserCardCreditChangeGrantRecordDTO();
                userCardCreditChangeGrantRecordDTO.setRecordId(String.valueOf(cardCreditManagerListResDTO.getId()));
                userCardCreditChangeGrantRecordDTO.setApplyReason(cardCreditManagerListResDTO.getApplyReason());
                userCardCreditChangeGrantRecordDTO.setCardModelDesc(ActiveModelEnum.getEnum(cardCreditManagerListResDTO.getCardModel()).getName());
                userCardCreditChangeGrantRecordDTO.setApplyTime(DateFormatUtil.tradeDateFormat((cardCreditManagerListResDTO.getCreateTime())));
                userCardCreditChangeGrantRecordDTO.setApplyStatusDesc(CreditApplyStatusEnum.getEnum(cardCreditManagerListResDTO.getApplyStatus()).getName());
                userCardCreditChangeGrantRecordDTO.setApplyAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(cardCreditManagerListResDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(cardCreditManagerListResDTO.getAmount())));
                userCardCreditChangeGrantRecordDTO.setApplyTransNo(cardCreditManagerListResDTO.getApplyTransNo());
                userCardCreditChangeGrantRecordDTOS.add(userCardCreditChangeGrantRecordDTO);
            }
        }
        return userCardCreditChangeGrantRecordDTOS;
    }

    public Page<UserCardCreditGrantListDTO> creditGrantList(UserCardCreditGrantListQueryDTO userCardCreditGrantListQueryDTO) {
        Page<UserCardCreditGrantListDTO> userCardCreditGrantListDTOPage = new Page<>();
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = convertCardCreditManagerPageReqDTO(userCardCreditGrantListQueryDTO);
        Page<CardCreditManagerPageResDTO> pagination = cardCreditManagerService.pagination(cardCreditManagerPageReqDTO, userCardCreditGrantListQueryDTO.getPageIndex(), userCardCreditGrantListQueryDTO.getPageSize());
        userCardCreditGrantListDTOPage.setTotal(pagination.getTotal());
        userCardCreditGrantListDTOPage.setCurrent(pagination.getCurrent());
        userCardCreditGrantListDTOPage.setSize(pagination.getSize());
        userCardCreditGrantListDTOPage.setRecords(buildCreditGrantListDTO(pagination.getRecords()));
        return userCardCreditGrantListDTOPage;
    }

    private List<UserCardCreditGrantListDTO> buildCreditGrantListDTO(List<CardCreditManagerPageResDTO> records) {
        List<UserCardCreditGrantListDTO> userCardCreditGrantListDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(records)){
            for (CardCreditManagerPageResDTO record : records) {
                UserCardCreditGrantListDTO userCardCreditGrantListDTO = new UserCardCreditGrantListDTO();
                userCardCreditGrantListDTO.setRecordId(String.valueOf(record.getId()));
                userCardCreditGrantListDTO.setApplyReason(record.getApplyReason());
                userCardCreditGrantListDTO.setApplyTitle(record.getApplyTitle());
                userCardCreditGrantListDTO.setApplyOperationUserName(record.getOperationUserName());
                userCardCreditGrantListDTO.setApplyOperationUserDept(record.getOperationUserDept());
                userCardCreditGrantListDTO.setApplyAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAmount())));
                userCardCreditGrantListDTO.setCostType(record.getCostType());
                userCardCreditGrantListDTO.setCostTypeName(record.getCostTypeName());
                //费用转换
                String costAttribution = "";
                if(StringUtils.isNotBlank(record.getCostAttribution())){
                    List<BudgetCostAttributionDTO> costAttributionDTOS = JsonUtils.toObj(record.getCostAttribution(), new TypeReference<List<BudgetCostAttributionDTO>>() {});
                    for (BudgetCostAttributionDTO budgetCostAttributionDTO:costAttributionDTOS){
                        costAttribution = budgetCostAttributionDTO.getCost_attribution_category() + ":" + budgetCostAttributionDTO.getCost_attribution_name();
                    }
                }
                userCardCreditGrantListDTO.setCostAttribution(costAttribution);
                userCardCreditGrantListDTO.setApplyTime(DateUtils.formatTime(record.getCreateTime()));
                userCardCreditGrantListDTO.setApplyStatusDesc(CreditApplyStatusEnum.getEnum(record.getApplyStatus()).getName());
                userCardCreditGrantListDTO.setBackedAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getReturnedAmount())));
                userCardCreditGrantListDTO.setWritenOffAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getWritenOffAmount())));
                userCardCreditGrantListDTO.setWriteOffIngAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getWritingOffAmount())));
                userCardCreditGrantListDTO.setUnWriteOffAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getUnwriteOffAmount())));
                userCardCreditGrantListDTO.setAvailableAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAvalibleAmount())));
                userCardCreditGrantListDTO.setWaitWriteOffAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getUncheckedAmount())));
                userCardCreditGrantListDTO.setApplyTransNo(record.getApplyTransNo());
                userCardCreditGrantListDTO.setBizNo(record.getBizNo());
                userCardCreditGrantListDTO.setApplyMeaningNo(record.getApplyMeaningNo());
                userCardCreditGrantListDTO.setApplyOperationUserName(record.getOperationUserName());
                userCardCreditGrantListDTO.setApplyOperationUserDept(record.getOperationUserDept());
                userCardCreditGrantListDTOS.add(userCardCreditGrantListDTO);
            }
        }
        return userCardCreditGrantListDTOS;
    }

    private CardCreditManagerPageReqDTO convertCardCreditManagerPageReqDTO(UserCardCreditGrantListQueryDTO userCardCreditGrantListQueryDTO) {
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = new CardCreditManagerPageReqDTO();
        cardCreditManagerPageReqDTO.setCompanyId(userCardCreditGrantListQueryDTO.getCompanyId());
        cardCreditManagerPageReqDTO.setApplyTransNo(userCardCreditGrantListQueryDTO.getApplyTransNo());
        cardCreditManagerPageReqDTO.setApplyReason(userCardCreditGrantListQueryDTO.getApplyReason());
        cardCreditManagerPageReqDTO.setApplyTitle(userCardCreditGrantListQueryDTO.getApplyTitle());
        cardCreditManagerPageReqDTO.setOperationUserName(userCardCreditGrantListQueryDTO.getApplyOperationUserName());
        cardCreditManagerPageReqDTO.setBeginTime(userCardCreditGrantListQueryDTO.getBeginApplyTime());
        cardCreditManagerPageReqDTO.setEndTime(userCardCreditGrantListQueryDTO.getEndApplyTime());
        cardCreditManagerPageReqDTO.setCostAttribution(userCardCreditGrantListQueryDTO.getCostAttribution());
        cardCreditManagerPageReqDTO.setApplyStatus(userCardCreditGrantListQueryDTO.getApplyStatus());
        cardCreditManagerPageReqDTO.setAvalibleAmountFlag(userCardCreditGrantListQueryDTO.getAvalibleAmountFlag());
        cardCreditManagerPageReqDTO.setCompanyId(userCardCreditGrantListQueryDTO.getCompanyId());
        cardCreditManagerPageReqDTO.setApplyType(CreditApplyTypeEnum.APPLY.getCode());
        cardCreditManagerPageReqDTO.setApplyMeaningNo(userCardCreditGrantListQueryDTO.getApplyMeaningNo());
        return cardCreditManagerPageReqDTO;
    }

    public Page<EmployeeModelConfigListDTO> employeeModelConfigList(EmployeeModelConfigListReqDTO employeeModelConfigListReqDTO) {
        Page<EmployeeModelConfigListDTO> employeeConfigListDTOPage = new Page<>();
        CardholderByPageReqDTO cardholderByPageReqDTO = new CardholderByPageReqDTO();
        String companyId = UserAuthHolder.getCurrentUser().getCompany_id();
        cardholderByPageReqDTO.setCompanyId(companyId);
        cardholderByPageReqDTO.setPageNo(employeeModelConfigListReqDTO.getPageIndex());
        cardholderByPageReqDTO.setPageSize(employeeModelConfigListReqDTO.getPageSize());
        cardholderByPageReqDTO.setName(employeeModelConfigListReqDTO.getEmployeeName());
        cardholderByPageReqDTO.setPhone(employeeModelConfigListReqDTO.getEmployeePhone());
        Page<CardholderByPageResDTO> cardholderOrApplyByPage = cardholderManager.findCardholderPageByStatus(cardholderByPageReqDTO,null);
        if(!CollectionUtils.isEmpty(cardholderOrApplyByPage.getRecords())) {
            employeeConfigListDTOPage.setRecords(buildEmployeeConfigDTO(cardholderOrApplyByPage.getRecords(), UserAuthHolder.getCurrentUser().getCompany_id()));
            employeeConfigListDTOPage.setTotal(cardholderOrApplyByPage.getTotal());
        }else {
            employeeModelConfigListReqDTO.setCompanyId(companyId);
            List<EmployeeModelConfigListDTO> configListDTOS = buildEmployeeConfigDTO4LianLian(employeeModelConfigListReqDTO);
            employeeConfigListDTOPage.setTotal(configListDTOS.size());
            employeeConfigListDTOPage.setRecords(configListDTOS);
        }

        return employeeConfigListDTOPage;
    }

    private List<EmployeeModelConfigListDTO> buildEmployeeConfigDTO(List<CardholderByPageResDTO> records,String companyId) {
        List<EmployeeModelConfigListDTO> employeeModelConfigListDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(records)){
            for (CardholderByPageResDTO record : records) {
                EmployeeModelConfigListDTO employeeModelConfigListDTO = new EmployeeModelConfigListDTO();
                employeeModelConfigListDTO.setEmployeeId(record.getEmployeeId());
                employeeModelConfigListDTO.setEmployeeName(record.getName());
                employeeModelConfigListDTO.setEmployeePhone(record.getPhone());
                //查询card表count
                CardDTO cardDTO = new CardDTO();
                cardDTO.setFxCardholderId(record.getFxCardholderId());
                List<CardDTO> cardDTOS = cardService.find(cardDTO);
                employeeModelConfigListDTO.setCardHoldNum(cardDTOS.size());
                //反查员工配置，存在取员工配置，否则默认普通
                CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = new CardEmployeeModelConfigListReqDTO();
                cardEmployeeModelConfigListReqDTO.setEmployeeId(record.getEmployeeId());
                CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO = cardEmployeeModelConfigService.listOne(cardEmployeeModelConfigListReqDTO);
                if(Objects.nonNull(cardEmployeeModelConfigListResDTO)){
                    employeeModelConfigListDTO.setActiveModel(cardEmployeeModelConfigListResDTO.getActiveModel());
                }else{
                    //查询公司对应默认配置
                    CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
                    cardModelConfigListReqDTO.setCompanyId(companyId);
                    CardModelConfigListResDTO cardModelConfigListResDTO = cardModelConfigService.listOne(cardModelConfigListReqDTO);
                    if(Objects.nonNull(cardModelConfigListResDTO)){
                        employeeModelConfigListDTO.setActiveModel(cardModelConfigListResDTO.getActiveModel());
                    }else{
                        employeeModelConfigListDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
                    }
                }
                employeeModelConfigListDTOS.add(employeeModelConfigListDTO);
            }
        }
        return employeeModelConfigListDTOS;
    }

    private List<EmployeeModelConfigListDTO> buildEmployeeConfigDTO4LianLian(EmployeeModelConfigListReqDTO employeeModelConfigListReqDTO) {
        //查询card表count
        CardDTO cardDTO = new CardDTO();
        cardDTO.setCompanyId(employeeModelConfigListReqDTO.getCompanyId());
//        cardDTO.setCardPlatform(CardPlatformEnum.LIANLIAN.getCode());
        cardDTO.setCardStatus(CardStatusEnum.ACTIVE.getStatus());
        List<CardDTO> cardDTOS = cardService.find(cardDTO);
        List<EmployeeModelConfigListDTO> employeeModelConfigListDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(cardDTOS)){
            for (CardDTO record : cardDTOS) {
                EmployeeModelConfigListDTO employeeModelConfigListDTO = new EmployeeModelConfigListDTO();
                employeeModelConfigListDTO.setEmployeeId(record.getEmployeeId());
                employeeModelConfigListDTO.setEmployeeName(record.getNameOnCard());
//                employeeModelConfigListDTO.setEmployeePhone(record.get);
                employeeModelConfigListDTO.setCardHoldNum(cardDTOS.size());
                //反查员工配置，存在取员工配置，否则默认普通
                CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = new CardEmployeeModelConfigListReqDTO();
                cardEmployeeModelConfigListReqDTO.setEmployeeId(record.getEmployeeId());
                CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO = cardEmployeeModelConfigService.listOne(cardEmployeeModelConfigListReqDTO);
                if (Objects.nonNull(cardEmployeeModelConfigListResDTO)) {
                    employeeModelConfigListDTO.setActiveModel(cardEmployeeModelConfigListResDTO.getActiveModel());
                } else {
                    //查询公司对应默认配置
                    CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
                    cardModelConfigListReqDTO.setCompanyId(employeeModelConfigListReqDTO.getCompanyId());
                    CardModelConfigListResDTO cardModelConfigListResDTO = cardModelConfigService.listOne(cardModelConfigListReqDTO);
                    if (Objects.nonNull(cardModelConfigListResDTO)) {
                        employeeModelConfigListDTO.setActiveModel(cardModelConfigListResDTO.getActiveModel());
                    } else {
                        employeeModelConfigListDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
                    }
                }
                if (employeeModelConfigListReqDTO.getEmployeeName() != null && employeeModelConfigListReqDTO.getEmployeeName().equals(record.getNameOnCard())) {
                    CardApplyListReqDTO cardApplyListReqDTO = new CardApplyListReqDTO();
                    cardApplyListReqDTO.setFxCardId(record.getFxCardId());
                    CardApplyListResDTO cardApplyListResDTO = cardApplyManager.listOne(cardApplyListReqDTO);
                    employeeModelConfigListDTO.setEmployeePhone(cardApplyListResDTO.getApplyerPhone());
                    employeeModelConfigListDTOS.add(employeeModelConfigListDTO);
                }else if (employeeModelConfigListReqDTO.getEmployeePhone() != null){
                    CardApplyListReqDTO cardApplyListReqDTO = new CardApplyListReqDTO();
                    cardApplyListReqDTO.setFxCardId(record.getFxCardId());
                    CardApplyListResDTO cardApplyListResDTO = cardApplyManager.listOne(cardApplyListReqDTO);
                    if (employeeModelConfigListReqDTO.getEmployeePhone().equals(cardApplyListResDTO.getApplyerPhone())) {
                        employeeModelConfigListDTO.setEmployeePhone(cardApplyListResDTO.getApplyerPhone());
                        employeeModelConfigListDTOS.add(employeeModelConfigListDTO);
                    }
                }else {
                    employeeModelConfigListDTOS.add(employeeModelConfigListDTO);
                }
            }
        }
        return employeeModelConfigListDTOS;
    }

    /**
     * 关联未使用完的申请单
     * @return UserCardCreditGrantRecordDTO
     */
    public UserCardCreditGrantRecordDTO relationApplyRecord(String fxCardId){
        CardCreditManagerListReqDTO cardCreditManagerListReqDTO = new CardCreditManagerListReqDTO();
        cardCreditManagerListReqDTO.setFxCardId(fxCardId);
        cardCreditManagerListReqDTO.setDeleteFlag(0);
        Page<UserCardCreditGrantRecordDetail> cardCreditManagerListResDTOPage = cardCreditManagerManager.queryAvailableRecord(fxCardId,1,20);
        UserCardCreditGrantRecordDTO userCardCreditGrantRecordDTO = new UserCardCreditGrantRecordDTO();
        userCardCreditGrantRecordDTO.setTotalCount(cardCreditManagerListResDTOPage.getTotal());
        userCardCreditGrantRecordDTO.setDataList(cardCreditManagerListResDTOPage.getRecords());
        return userCardCreditGrantRecordDTO;
    }

    /**
     * 卡额度变更记录查询
     * @param bankCardCreditChangeRecordPageDTO
     * @return
     */
    public Page<BankCardCreditChangeDTO> creditChangeRecordList(BankCardCreditChangeRecordPageDTO bankCardCreditChangeRecordPageDTO) {
        Page<BankCardCreditChangeDTO> bankCardFlowDTOPage = new Page<>();
        BankCardFlowPageReqDTO bankCardFlowPageReqDTO = buildBankCardFlowPageDTO(bankCardCreditChangeRecordPageDTO);
        Page<BankCardFlowPageResDTO> pagination = bankCardFlowService.pagination(bankCardFlowPageReqDTO, bankCardCreditChangeRecordPageDTO.getPageIndex(), bankCardCreditChangeRecordPageDTO.getPageSize());
        bankCardFlowDTOPage.setRecords(buildBankCardCreditChangeRecords(pagination.getRecords(),bankCardCreditChangeRecordPageDTO.getFxCardId()));
        bankCardFlowDTOPage.setSize(pagination.getSize());
        bankCardFlowDTOPage.setCurrent(pagination.getCurrent());
        bankCardFlowDTOPage.setTotal(pagination.getTotal());
        return bankCardFlowDTOPage;
    }

    private List<BankCardCreditChangeDTO> buildBankCardCreditChangeRecords(List<BankCardFlowPageResDTO> records,String fxCardId) {
        List<BankCardCreditChangeDTO> bankCardCreditChangeDTOS = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(records)){
            CardShowResDTO cardShowResDTO = cardService.cardDetail(fxCardId);
            for (BankCardFlowPageResDTO record : records) {
                BankCardCreditChangeDTO bankCardCreditChangeDTO = new BankCardCreditChangeDTO();
                bankCardCreditChangeDTO.setEmployeeName(cardShowResDTO.getCardHolderName());
                bankCardCreditChangeDTO.setCardNo(cardShowResDTO.getBankCardNo());
                bankCardCreditChangeDTO.setCardType(CardFormFactorEnum.getEnum(cardShowResDTO.getCardFormFactor()).getName());
                bankCardCreditChangeDTO.setOperationType(record.getOperationType());
                bankCardCreditChangeDTO.setOperationTypeDesc(CardFlowOperationTypeEnum.getEnumName(record.getOperationType()));
                bankCardCreditChangeDTO.setTransTime(DateUtils.format(record.getCreateTime(),FORMAT_DATETIME_WITH_SLASH));
                bankCardCreditChangeDTO.setConversCurrency(cardShowResDTO.getCurrency());
                bankCardCreditChangeDTO.setConversAmount(BigDecimalUtils.fen2yuan(record.getOperationAmount()));
                bankCardCreditChangeDTOS.add(bankCardCreditChangeDTO);
            }
        }
        return bankCardCreditChangeDTOS;
    }

    private BankCardFlowPageReqDTO buildBankCardFlowPageDTO(BankCardCreditChangeRecordPageDTO bankCardCreditChangeRecordPageDTO) {
        BankCardFlowPageReqDTO bankCardFlowPageReqDTO = new BankCardFlowPageReqDTO();
        bankCardFlowPageReqDTO.setFxCardId(bankCardCreditChangeRecordPageDTO.getFxCardId());
        return bankCardFlowPageReqDTO;
    }

    public Page<UserCardCreditChangeGrantRecordDTO> cardCreditGrantRecordInfo(String fxCardId,Integer pageNo,Integer pageSize) {
        if (StringUtils.isBlank(fxCardId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        Page<UserCardCreditChangeGrantRecordDTO> userCardCreditChangeGrantRecordDTOPage = new Page<>();
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = new CardCreditManagerPageReqDTO();
        cardCreditManagerPageReqDTO.setFxCardId(fxCardId);
        cardCreditManagerPageReqDTO.setApplyType(CreditApplyTypeEnum.APPLY.getCode());
        cardCreditManagerPageReqDTO.setDeleteFlag(0);
        Page<CardCreditManagerPageResDTO> pagination = cardCreditManagerManager.pagination(cardCreditManagerPageReqDTO, pageNo, pageSize);
        if(Objects.nonNull(pagination)){
            userCardCreditChangeGrantRecordDTOPage.setTotal(pagination.getTotal());
            userCardCreditChangeGrantRecordDTOPage.setRecords(buildCreditGrantRecordList(pagination.getRecords()));
        }
        return userCardCreditChangeGrantRecordDTOPage;
    }

    private List<UserCardCreditChangeGrantRecordDTO> buildCreditGrantRecordList(List<CardCreditManagerPageResDTO> records) {
        List<UserCardCreditChangeGrantRecordDTO> userCardCreditChangeGrantRecordDTOS = Lists.newArrayList();
        if(CollectionUtils.isEmpty(records)){
           return userCardCreditChangeGrantRecordDTOS;
        }
        for (CardCreditManagerPageResDTO record : records) {
            UserCardCreditChangeGrantRecordDTO userCardCreditChangeGrantRecordDTO = new UserCardCreditChangeGrantRecordDTO();
            userCardCreditChangeGrantRecordDTO.setRecordId(String.valueOf(record.getId()));
            String applyReason = (record.getApplyReason() == null ? "额度申请":record.getApplyReason());
            userCardCreditChangeGrantRecordDTO.setApplyReason(applyReason);
            userCardCreditChangeGrantRecordDTO.setCardModelDesc(ActiveModelEnum.getEnum(record.getCardModel()).getName());
            userCardCreditChangeGrantRecordDTO.setApplyTime(DateFormatUtil.tradeDateFormat((record.getCreateTime())));
            userCardCreditChangeGrantRecordDTO.setApplyStatusDesc(CreditApplyStatusEnum.getEnum(record.getApplyStatus()).getName());
            userCardCreditChangeGrantRecordDTO.setApplyAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAmount())));
            userCardCreditChangeGrantRecordDTO.setApplyTransNo(record.getApplyTransNo());
            userCardCreditChangeGrantRecordDTO.setApplyTitle(record.getApplyTitle());
            userCardCreditChangeGrantRecordDTO.setPettyType(CardModelEnum.getEnum(record.getCardModel()).getKey());
            userCardCreditChangeGrantRecordDTO.setPettyTypeDesc(CardModelEnum.getEnum(record.getCardModel()).getValue());
            userCardCreditChangeGrantRecordDTO.setApplyOrderType(record.getApplyOrderType());
            userCardCreditChangeGrantRecordDTO.setApplyOrderTypeDesc(I18nUtils.transferI18nMessage(userCardCreditChangeGrantRecordDTO.getApplyOrderTypeDesc()));
            userCardCreditChangeGrantRecordDTO.setApplyId(record.getBizNo());
            userCardCreditChangeGrantRecordDTO.setApplyOrderId(record.getOriApplyTransNo());
            userCardCreditChangeGrantRecordDTOS.add(userCardCreditChangeGrantRecordDTO);
        }
        return userCardCreditChangeGrantRecordDTOS;
    }

    public Page<UserCardCreditChangeBackRecordDTO> cardCreditBackRecordInfo(String fxCardId, Integer pageNo, Integer pageSize) {
        if (StringUtils.isBlank(fxCardId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        Page<UserCardCreditChangeBackRecordDTO> userCardCreditChangeBackRecordDTOPage = new Page<>();
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = new CardCreditManagerPageReqDTO();
        cardCreditManagerPageReqDTO.setFxCardId(fxCardId);
        cardCreditManagerPageReqDTO.setApplyType(CreditApplyTypeEnum.RETURN.getCode());
        cardCreditManagerPageReqDTO.setDeleteFlag(0);
        Page<CardCreditManagerPageResDTO> pagination = cardCreditManagerManager.pagination(cardCreditManagerPageReqDTO, pageNo, pageSize);
        if(Objects.nonNull(pagination)){
            userCardCreditChangeBackRecordDTOPage.setTotal(pagination.getTotal());
            userCardCreditChangeBackRecordDTOPage.setRecords(buildCreditBackRecordList(pagination.getRecords()));
        }
        return userCardCreditChangeBackRecordDTOPage;
    }

    public UserCardCreditChangeBackRecordDTO cardCreditBackRecordInfoByRecordId(String recordId) {
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = new CardCreditManagerPageReqDTO();
        cardCreditManagerPageReqDTO.setId(Long.valueOf(recordId));
        cardCreditManagerPageReqDTO.setDeleteFlag(0);
        CardCreditManagerDTO recordDTO = cardCreditManagerManager.findById(recordId);
        CurrencyEnum currencyEnum=CurrencyEnum.USD;
        UserCardCreditChangeBackRecordDTO userCardCreditChangeBackRecordDTO = new UserCardCreditChangeBackRecordDTO();
        userCardCreditChangeBackRecordDTO.setRecordId(String.valueOf(recordDTO.getId()));
        userCardCreditChangeBackRecordDTO.setBackTitle(I18nUtils.transferI18nMessage("退还额度"));
        userCardCreditChangeBackRecordDTO.setBackTime(DateFormatUtil.tradeDateFormat((recordDTO.getCreateTime())));
        userCardCreditChangeBackRecordDTO.setBackAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(recordDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(recordDTO.getAmount())));
        userCardCreditChangeBackRecordDTO.setOperationTypeDesc(CardFlowOperationTypeEnum.getEnum(recordDTO.getOperationType()).getName());
        userCardCreditChangeBackRecordDTO.setApplyTransNo(recordDTO.getApplyTransNo());
        userCardCreditChangeBackRecordDTO.setOriApplyTransNo(recordDTO.getOriApplyTransNo());
        userCardCreditChangeBackRecordDTO.setPettyType(CardModelEnum.getEnum(recordDTO.getCardModel()).getKey());
        userCardCreditChangeBackRecordDTO.setPettyTypeDesc(CardModelEnum.getEnum(recordDTO.getCardModel()).getValue());
        userCardCreditChangeBackRecordDTO.setApplyOrderType(1);
        userCardCreditChangeBackRecordDTO.setApplyOrderTypeDesc(I18nUtils.transferI18nMessage(userCardCreditChangeBackRecordDTO.getApplyOrderTypeDesc()));
        currencyEnum=CurrencyEnum.getCurrencyByCodeIgnoreCase(recordDTO.getCurrency());
        List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList = CardCreditManagerRelationManager.me().findByApplyTransNo(recordDTO.getBizNo());
        if (!CollectionUtils.isEmpty(cardCreditManagerRelationDTOList)){
            List<RelationApply> relationApplies = new ArrayList<>();
            for (CardCreditManagerRelationDTO  cardCreditManagerRelationDTO:cardCreditManagerRelationDTOList) {
                RelationApply relationApply =new RelationApply();
                relationApply.setRelationAmount(cardCreditManagerRelationDTO.getRelationAmount());
                relationApply.setRelationAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(recordDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(cardCreditManagerRelationDTO.getRelationAmount())));
                relationApply.setApplyAmount(cardCreditManagerRelationDTO.getApplyAmount());
                relationApply.setApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(recordDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(cardCreditManagerRelationDTO.getApplyAmount())));
                relationApply.setApplyTime(cardCreditManagerRelationDTO.getApplyTime());
                relationApply.setApplyTimeShow(DateFormatUtil.tradeDateFormat((cardCreditManagerRelationDTO.getApplyTime())));
                relationApply.setApplyTransNo(cardCreditManagerRelationDTO.getApplyTransNo());
                relationApply.setRecordId(cardCreditManagerRelationDTO.getRecordId());
                relationApply.setApplyTitle(cardCreditManagerRelationDTO.getApplyTitle());
                relationApply.setUncheckedAmount(cardCreditManagerRelationDTO.getUncheckedAmount());
                relationApply.setUncheckedAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(recordDTO.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(cardCreditManagerRelationDTO.getUncheckedAmount())));
                relationApplies.add(relationApply);
            }
            userCardCreditChangeBackRecordDTO.setGrantRecords(relationApplies);
        }else {
            userCardCreditChangeBackRecordDTO.setGrantRecords(new ArrayList<>());
        }

        return userCardCreditChangeBackRecordDTO;
    }

    private List<UserCardCreditChangeBackRecordDTO> buildCreditBackRecordList(List<CardCreditManagerPageResDTO> records) {
        List<UserCardCreditChangeBackRecordDTO> userCardCreditChangeBackRecordDTOS = Lists.newArrayList();
        if(CollectionUtils.isEmpty(records)){
            return userCardCreditChangeBackRecordDTOS;
        }
        for (CardCreditManagerPageResDTO record : records) {
            UserCardCreditChangeBackRecordDTO userCardCreditChangeBackRecordDTO = new UserCardCreditChangeBackRecordDTO();
            userCardCreditChangeBackRecordDTO.setRecordId(String.valueOf(record.getId()));
            userCardCreditChangeBackRecordDTO.setBackTitle(I18nUtils.transferI18nMessage("退还额度"));
            userCardCreditChangeBackRecordDTO.setBackTime(DateFormatUtil.tradeDateFormat((record.getCreateTime())));
            userCardCreditChangeBackRecordDTO.setBackAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAmount())));
            userCardCreditChangeBackRecordDTO.setOperationTypeDesc(CardFlowOperationTypeEnum.getEnum(record.getOperationType()).getName());
            userCardCreditChangeBackRecordDTO.setApplyTransNo(record.getApplyTransNo());
            userCardCreditChangeBackRecordDTO.setOriApplyTransNo(record.getOriApplyTransNo());
            userCardCreditChangeBackRecordDTO.setPettyType(CardModelEnum.getEnum(record.getCardModel()).getKey());
            userCardCreditChangeBackRecordDTO.setPettyTypeDesc(CardModelEnum.getEnum(record.getCardModel()).getValue());
            userCardCreditChangeBackRecordDTO.setApplyOrderType(1);
            userCardCreditChangeBackRecordDTO.setApplyOrderTypeDesc(I18nUtils.transferI18nMessage(userCardCreditChangeBackRecordDTO.getApplyOrderTypeDesc()));
            userCardCreditChangeBackRecordDTO.setGrantRecords(new ArrayList<>());
            userCardCreditChangeBackRecordDTOS.add(userCardCreditChangeBackRecordDTO);
        }
        return userCardCreditChangeBackRecordDTOS;
    }
}
