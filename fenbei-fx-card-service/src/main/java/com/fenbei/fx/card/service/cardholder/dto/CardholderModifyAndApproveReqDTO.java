package com.fenbei.fx.card.service.cardholder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/18
 */
@Data
public class CardholderModifyAndApproveReqDTO implements Serializable {


    private static final long serialVersionUID = -1382866190280422730L;
    /**
     * 申请单id，如果是编辑，传值
     */
    private String applyId;

    private String fxCardholderId;

    /**
     *  展示状态，根据此状态判断是申请单还是持卡人
     * 1：待审核 2：更新中 4：更新失败3：生效中 5：已失效
     */
    @NotNull
    private Integer showStatus;

    /**
     * 证件的国家:US
     */
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    private Integer identificationType;

    /**
     * 证件号
     */
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    //@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String identificationExpiryDate;

    private Integer identificationExpiryType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birth;

    private String phone;

    private String email;

    /**
     * 地区
     */
    private AddressDto addressDto;


    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;


}
