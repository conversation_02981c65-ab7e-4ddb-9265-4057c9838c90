package com.fenbei.fx.card.common.kafka;

import com.fenbeitong.finhub.kafka.msg.IMessage;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaFxCardAccountMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * KafkaProducer
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/08/01 14:25
 */
@Slf4j
@Component
public class KafkaProducer {

    public static KafkaProducer me() {
        return SpringUtil.getBean(KafkaProducer.class);
    }

    @Autowired
    IKafkaProducerPublisher kafkaProducerPublisher;

    public void publish(IMessage message) {
        kafkaProducerPublisher.publish(message);
    }

    public void publish(IMessage message, String partitionKey) {
        kafkaProducerPublisher.publish(message, partitionKey);
    }

    /**
     * 额度下发 Msg
     *
     * @param companyId
     * @param employeeId
     * @param fxCardId
     * @param amount
     */
    public void sendCardApplyMsg(String companyId, String employeeId, String fxCardId, BigDecimal amount, String companyAccountId) {
        KafkaFxCardAccountMsg kafkaFxCardAccountMsg = buildKafkaFxCardAccountMsg(companyId, employeeId, fxCardId, amount, 1);
        kafkaFxCardAccountMsg.setCompanyAccountId(companyAccountId);
        publish(kafkaFxCardAccountMsg);
    }

    /**
     * 交易授权 Msg
     *
     * @param companyId
     * @param employeeId
     * @param fxCardId
     * @param amount
     */
    public void sendCardAuthorizeMsg(String companyId, String employeeId, String fxCardId, BigDecimal amount) {
        KafkaFxCardAccountMsg kafkaFxCardAccountMsg = buildKafkaFxCardAccountMsg(companyId, employeeId, fxCardId, amount, 2);
        publish(kafkaFxCardAccountMsg);
    }

    /**
     * 交易消费 Msg
     *
     * @param companyId
     * @param employeeId
     * @param fxCardId
     * @param amount
     */
    public void sendCardTradeMsg(String companyId, String employeeId, String fxCardId, BigDecimal amount) {
        KafkaFxCardAccountMsg kafkaFxCardAccountMsg = buildKafkaFxCardAccountMsg(companyId, employeeId, fxCardId, amount, 3);
        publish(kafkaFxCardAccountMsg);
    }

    private KafkaFxCardAccountMsg buildKafkaFxCardAccountMsg(String companyId, String employeeId, String fxCardId, BigDecimal amount, Integer type) {
        KafkaFxCardAccountMsg kafkaFxCardAccountMsg = new KafkaFxCardAccountMsg();

        kafkaFxCardAccountMsg.setCompanyId(companyId);
        kafkaFxCardAccountMsg.setEmployeeId(employeeId);
        kafkaFxCardAccountMsg.setFxCardId(fxCardId);
        kafkaFxCardAccountMsg.setAmount(amount);
        kafkaFxCardAccountMsg.setTime(DateUtil.date());
        kafkaFxCardAccountMsg.setType(type);

        return kafkaFxCardAccountMsg;
    }
}
