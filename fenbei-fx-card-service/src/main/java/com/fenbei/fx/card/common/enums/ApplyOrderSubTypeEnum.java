package com.fenbei.fx.card.common.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 申请单子类型
 */
public enum ApplyOrderSubTypeEnum {
    ALL(0, 0, "全部"),
    VIRTUAL_CARD(1, 15,"海外卡额度申请单"),
    PETTY(2, 19,"海外卡备用金申请单"),
    QUOTA_GRANT(3, 50,"额度发放单"),
    QUOTA_BIZ_GRANT(4, 51,"商旅自定义申请单")
;

    private int type;
    private int typeReal;
    private String desc;

    ApplyOrderSubTypeEnum(int type, int typeReal, String desc) {
        this.type = type;
        this.typeReal = typeReal;
        this.desc = desc;
    }

    public static List<Integer> getAllTypes() {
        List<Integer> allTypes = new ArrayList<>();
        allTypes.add(ApplyOrderSubTypeEnum.QUOTA_GRANT.getTypeReal());
        allTypes.add(ApplyOrderSubTypeEnum.VIRTUAL_CARD.getTypeReal());
        allTypes.add(ApplyOrderSubTypeEnum.PETTY.getTypeReal());
        return allTypes;
    }

    public static String getDescByType(Integer type){
        if (type == null) {
            return "-";
        }

        for (ApplyOrderSubTypeEnum typeEnum : ApplyOrderSubTypeEnum.values()) {
            if (typeEnum.typeReal == type) {
                return typeEnum.desc;
            }
        }
        return "-";
    }

    public int getType() {
        return type;
    }

    public int getTypeReal() {
        return typeReal;
    }

    public String getDesc() {
        return desc;
    }
}
