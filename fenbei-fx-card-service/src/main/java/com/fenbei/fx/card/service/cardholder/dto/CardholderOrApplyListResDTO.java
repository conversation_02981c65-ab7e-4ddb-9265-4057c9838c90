package com.fenbei.fx.card.service.cardholder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 持卡人 列表 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderOrApplyListResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 持卡人信息
     */
    private List<CardholderDetailResDTO> cardholderList;

    /**
     * 持卡人申请信息
     */
    private List<CardholderDetailResDTO> cardholderApplyList;
}
