package com.fenbei.fx.card.service.webservice.dto;

import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.eventbus.event.common.IEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonPushDto {

    private boolean pushAlert;
    private boolean pushEvent;
    private boolean pushSms;

    private PushAlertDto pushAlertDto;
    private IEvent iEvent;
    private SmsContract msgBody;


    /**
     * 发送短信
     *
     * @param pushSmsStr
     * @param msgBody
     */
    public CommonPushDto(String pushSmsStr, SmsContract msgBody) {
        this.pushAlert = false;
        this.pushEvent = false;
        this.pushSms = true;
        this.msgBody = msgBody;

    }


    /**
     * 发送Push
     *
     * @param pushAlertDto
     */
    public CommonPushDto(PushAlertDto pushAlertDto) {
        this.pushAlert = true;
        this.pushEvent = false;
        this.pushSms = false;
        this.pushAlertDto = pushAlertDto;
    }

    /**
     * 发送监听消息
     *
     * @param iEvent
     */
    public CommonPushDto(IEvent iEvent) {
        this.pushAlert = false;
        this.pushEvent = true;
        this.pushSms = false;
        this.iEvent = iEvent;
    }

    /**
     * 发送Push
     *
     * @param pushAlertDto
     */
    public CommonPushDto(PushAlertDto pushAlertDto, IEvent iEvent) {
        this.pushAlert = true;
        this.pushEvent = true;
        this.pushSms = false;
        this.pushAlertDto = pushAlertDto;
        this.iEvent = iEvent;
    }

}
