package com.fenbei.fx.card.service.redis;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


@Component
public class RedissonDao {


    private static final Logger logger = LoggerFactory.getLogger(RedissonDao.class);
    /**
     * Spring Redis 基础操作模板
     */
    @Autowired
    private RedissonClient redissonClient;


    /**
     * 强制解锁时间设置
     */
    private long LOCK_TIME = 500L;

    /**
     * 等待时间
     **/
    private long WAIT_TIME = 600L;

    /**
     * 休眠时间
     **/
    private long SLEEP_TIME = 100L;


    /**
     * 根据key值获取锁
     *
     * @param lockName
     */
    public void lock(String lockName) {
        RLock lock = redissonClient.getLock(lockName);
        //lock提供带timeout参数，timeout结束强制解锁，防止死锁
        lock.lock(LOCK_TIME, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取锁
     *
     * @param lockName
     * @return
     * @throws InterruptedException
     */
    public boolean tryLock(String lockName) throws InterruptedException {
        logger.info("尝试获取锁：" + lockName);
        RLock lock = redissonClient.getLock(lockName);
        //tryLock，第一个参数是等待时间。 第二个参数 强制锁释放时间
        boolean tryLock = lock.tryLock(WAIT_TIME, LOCK_TIME, TimeUnit.MILLISECONDS);
        logger.info("尝试获取锁结果：" + lockName + tryLock + "等待时长ms" + WAIT_TIME);
        return tryLock;
    }

    public boolean tryLock4Trade(String lockName) throws InterruptedException {
        logger.info("尝试获取锁：" + lockName);
        RLock lock = redissonClient.getLock(lockName);
        //tryLock，第一个参数是等待时间。 第二个参数 强制锁释放时间
        boolean tryLock = lock.tryLock(WAIT_TIME, 2000L, TimeUnit.MILLISECONDS);
        logger.info("尝试获取锁结果：" + lockName + tryLock + "等待时长ms" + WAIT_TIME);
        return tryLock;
    }


    /**
     * 获取锁 如果获取失败等待一些时间后，再进行，否者失败
     *
     * @param lockName
     * @return
     * @throws InterruptedException
     */
    public boolean tryLock(long waitTime, long lockTime, TimeUnit timeUnit, String lockName) throws InterruptedException {
        RLock lock = redissonClient.getLock(lockName);
        //tryLock，第一个参数是等待时间。 第二个参数 强制锁释放时间
        return lock.tryLock(waitTime, lockTime, timeUnit);
    }

    /**
     * 解锁
     *
     * @param lockName
     */
    public void unLock(String lockName) {
        RLock lock = redissonClient.getLock(lockName);
        if (lock.isLocked()) {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取锁，一直等待到取到锁后返回
     *
     * @param lockName
     * @throws InterruptedException
     */
    public boolean getUntilHaveLock(String lockName) throws InterruptedException {
        while (true) {
            if (tryLock(lockName)) {
                return true;
            } else {
                Thread.sleep(SLEEP_TIME);
            }
        }
    }

}
