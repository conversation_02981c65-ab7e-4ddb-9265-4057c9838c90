package com.fenbei.fx.card.service.remote.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: java类作用描述
 * @ClassName: BudgetDTO
 * @Author: zhangga
 * @CreateDate: 2019/1/8 3:53 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/8 3:53 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class BudgetDTO {
    /**
     * 公司id
     **/
    @NotNull
    private String company_id;
    /**
     * 用户id
     **/
    @NotNull
    private String employee_id;
    /**
     * 订单类型 3=用车，7=机票，11=酒店，15=火车票，20=采购，30=用餐 50=外卖
     **/
    @NotNull
    private Integer order_category;
    /**
     * 当前订单id
     **/
    @NotNull
    private String order_id;
    /**
     * 订单金额（单位元） 正数为扣款 负数为还款
     **/
    @NotNull
    private BigDecimal order_amount;
    /**
     * 费用归属类型  1=部门，2=项目中心
     **/
    @NotNull
    private Integer cost_attribution_category;
    /**
     * 费用归属id
     **/
    @NotNull
    private String cost_attribution_id;
    /**
     * 父订单id 暂时不用传
     **/
    private String pre_order_id;
    /**
     * 原始订单id 暂时不用传
     **/
    private String origin_order_id;
    /**
     * 订单时间 格式:yyyy-MM-dd HH:mm:ss
     **/
    @NotNull
    private String create_time;


    private List<BudgetCostAttributionDTO> cost_attribution_list;

    /**
     * 费用归属可选范围 1.部门 2.项目 3.部门或项目 4.部门和项目
     */
    private Integer cost_attribution_scope;

    /**
     * 预算扣减类型(仅费用归属为部门和项目有效) 1.部门和项目 2.部门 3.项目
     */
    private Integer budget_cost_attr_type;


    /**
     * 是否占用个人预算 0-不占 1-占用(不传默认占用)
     */
    private Integer use_personal_budget;

}
