package com.fenbei.fx.card.service.cardmodelconfig.dto;

import com.finhub.framework.common.dto.BaseDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡使用模式配置 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardModelConfigDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    private Integer modelType;

    /**
     * 国际卡企业生效模式: 1.普通模式,2.备用金模式
     */
    private Integer activeModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
