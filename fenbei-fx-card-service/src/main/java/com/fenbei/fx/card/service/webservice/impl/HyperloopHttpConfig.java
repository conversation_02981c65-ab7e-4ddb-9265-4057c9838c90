package com.fenbei.fx.card.service.webservice.impl;

import com.fenbei.fx.card.service.webservice.HyperloopHttpService;
import com.jakewharton.retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

/**
 * <AUTHOR>
 * Event为Kafka消息，需要在EventBus中增加IEvent实现对象
 * 参考http://wiki.fenbeijinfu.com/pages/viewpage.action?pageId=3877091
 */
@Configuration
public class HyperloopHttpConfig {

    @Value("${server.hyperloop.route}")//server.hyperloop.route=http://hl-dev.fenbeijinfu.com
    private String serverHyperloop;

    @Value("${okhttp3.logging.level:NONE}") //okhttp3.logging.level=BODY
    private HttpLoggingInterceptor.Level level;

    @Autowired
    @Qualifier("hyperloopHttpRetrofit")
    private Retrofit hyperloopHttpRetrofit;

    @Bean
    public HyperloopHttpService getHyperloopHttpService() {
        return hyperloopHttpRetrofit.create(HyperloopHttpService.class);
    }

    @Bean(name = "hyperloopHttpRetrofit")
    public Retrofit getRetrofit() {
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(level);
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(logging)
                .build();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(serverHyperloop)
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create())
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .build();
        return retrofit;
    }
}
