package com.fenbei.fx.card.service.usercard.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午2:38
 */
@Data
public class UserCardCreditGrantRecordDTO extends BaseDTO implements Serializable {

    private Long totalCount;

    private List<UserCardCreditGrantRecordDetail> dataList;
}
