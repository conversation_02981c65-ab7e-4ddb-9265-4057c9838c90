package com.fenbei.fx.card.service.card.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.service.card.dto.CardAddReqDTO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardListReqDTO;
import com.fenbei.fx.card.service.card.dto.CardListResDTO;
import com.fenbei.fx.card.service.card.dto.CardModifyReqDTO;
import com.fenbei.fx.card.service.card.dto.CardPageReqDTO;
import com.fenbei.fx.card.service.card.dto.CardPageResDTO;
import com.fenbei.fx.card.service.card.dto.CardRemoveReqDTO;
import com.fenbei.fx.card.service.card.dto.CardShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardConverter extends BaseConverter<CardDTO, CardPO> {

    static CardConverter me() {
        return SpringUtil.getBean(CardConverter.class);
    }

    CardDTO convertToCardDTO(CardAddReqDTO cardAddReqDTO);

    CardDTO convertToCardDTO(CardModifyReqDTO cardModifyReqDTO);

    CardDTO convertToCardDTO(CardRemoveReqDTO cardRemoveReqDTO);

    CardDTO convertToCardDTO(CardListReqDTO cardListReqDTO);

    CardDTO convertToCardDTO(CardPageReqDTO cardPageReqDTO);

    CardShowResDTO convertToCardShowResDTO(CardDTO cardDTO);

    List<CardShowResDTO> convertToCardShowResDTOList(List<CardDTO> cardDTOList);

    CardListResDTO convertToCardListResDTO(CardDTO cardDTO);

    List<CardListResDTO> convertToCardListResDTOList(List<CardDTO> cardDTOList);

    List<CardDTO> convertToCardDTOList(List<CardAddReqDTO> cardAddReqDTOList);

    CardPageResDTO convertToCardPageResDTO(CardDTO cardDTO);

    List<CardPageResDTO> convertToCardPageResDTOList(List<CardDTO> cardDTOList);

    Page<CardPageResDTO> convertToCardPageResDTOPage(Page<CardDTO> cardDTOPage);
}
