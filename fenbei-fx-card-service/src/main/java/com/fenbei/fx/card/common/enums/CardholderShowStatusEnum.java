package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 展示给前端的持卡人/申请单状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public enum CardholderShowStatusEnum {

    /**
     * 持卡人 showStatus 3：生效中 5：已失效
     */
    EFFECTIVE(3, "生效中"
        , Lists.newArrayList(CardholderOperTypeEnum.EDIT, CardholderOperTypeEnum.DISABLE, CardholderOperTypeEnum.DETAIL)
        , Lists.newArrayList(CardholderStatusEnum.EFFECTIVE.getKey())),

    EXPIRED(5, "已失效"
        , Lists.newArrayList(CardholderOperTypeEnum.EDIT, CardholderOperTypeEnum.OPEN, CardholderOperTypeEnum.DETAIL)
        , Lists.newArrayList(CardholderStatusEnum.DISABLED.getKey(), CardholderStatusEnum.EXPIRED.getKey())),
   ;

    private final int key;
    private final String desc;
    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("生效中", "cardholder.show.status.effective");
        I18N_KEY_MAP.put("已失效", "cardholder.show.status.expired");
    }

    /**
     * 可操作类型
     */
    private List<CardholderOperTypeEnum> usableOpera;

    /**
     * 对应表中状态类型
     */
    private List<Integer> status;

    public int getKey() {
        return key;
    }

    public String getDesc() {
        String i18nKey = I18N_KEY_MAP.get(desc);
        return i18nKey == null ? desc : I18nUtils.getMessage(i18nKey);
    }

    public List<Integer> getStatus() {
        return status;
    }

    public List<CardholderOperTypeEnum> getUsableOpera() {
        return usableOpera;
    }


    CardholderShowStatusEnum(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }
    CardholderShowStatusEnum(int key, String desc, List<CardholderOperTypeEnum> usableOpera, List<Integer> status) {
        this.key = key;
        this.desc = desc;
        this.usableOpera = usableOpera;
        this.status = status;
    }


    public static CardholderShowStatusEnum getEnum(int key) {
        for (CardholderShowStatusEnum item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static Boolean existKey(int key) {
        return null != getEnum(key);
    }

    public static CardholderShowStatusEnum getEnumWhithHolderStatus(int holderStatus) {
        for (CardholderShowStatusEnum item : values()) {
            for (Integer status : item.getStatus()) {
                if (status == holderStatus) {
                    return item;
                }
            }

        }
        return null;
    }

}
