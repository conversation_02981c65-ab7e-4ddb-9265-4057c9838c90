package com.fenbei.fx.card.service.cardverificationflow.impl;

import com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO;
import com.fenbei.fx.card.service.cardverificationflow.CardVerificationFlowService;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.fenbei.fx.card.service.cardverificationflow.manager.CardVerificationFlowManager;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 核销记录表 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Slf4j
@Service
public class CardVerificationFlowServiceImpl extends BaseServiceImpl<CardVerificationFlowManager, CardVerificationFlowPO, CardVerificationFlowDTO> implements CardVerificationFlowService {

    @Override
    public List<CardVerificationFlowListResDTO> list(final CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO) {
        return manager.list(cardVerificationFlowListReqDTO);
    }

    @Override
    public CardVerificationFlowListResDTO listOne(final CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO) {
        return manager.listOne(cardVerificationFlowListReqDTO);
    }

    @Override
    public Page<CardVerificationFlowPageResDTO> pagination(final CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO, final Integer current,
                                                           final Integer size) {
        return manager.pagination(cardVerificationFlowPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        return manager.add(cardVerificationFlowAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        return manager.addAllColumn(cardVerificationFlowAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList) {
        return manager.addBatchAllColumn(cardVerificationFlowAddReqDTOList);
    }

    @Override
    public CardVerificationFlowShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardVerificationFlowShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        return manager.modify(cardVerificationFlowModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        return manager.modifyAllColumn(cardVerificationFlowModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO) {
        return manager.removeByParams(cardVerificationFlowRemoveReqDTO);
    }
}
