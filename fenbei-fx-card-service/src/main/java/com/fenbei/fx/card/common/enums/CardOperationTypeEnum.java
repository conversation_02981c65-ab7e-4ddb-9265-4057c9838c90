package com.fenbei.fx.card.common.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum CardOperationTypeEnum {

    /**
     *  模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    APPLY(4, "额度申请"),
    REFUND(5, "额度退回")

    ;


    public static CardOperationTypeEnum getEnum(Integer code) {
        for (CardOperationTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
