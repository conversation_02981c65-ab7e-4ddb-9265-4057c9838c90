package com.fenbei.fx.card.service.cardmail;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardmail.dto.CardMailAddReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailRemoveReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailShowResDTO;

import java.util.List;

/**
 * 实体卡邮寄管理表 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
public interface CardMailService extends BaseService<CardMailDTO> {

    static CardMailService me() {
        return SpringUtil.getBean(CardMailService.class);
    }

    /**
     * 列表
     *
     * @param cardMailListReqDTO 入参DTO
     * @return
     */
    List<CardMailListResDTO> list(CardMailListReqDTO cardMailListReqDTO);

    /**
     * First查询
     *
     * @param cardMailListReqDTO 入参DTO
     * @return
     */
    CardMailListResDTO listOne(CardMailListReqDTO cardMailListReqDTO);

    /**
     * 分页
     *
     * @param cardMailPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardMailPageResDTO> pagination(CardMailPageReqDTO cardMailPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardMailAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardMailAddReqDTO cardMailAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardMailAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardMailAddReqDTO cardMailAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardMailAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardMailAddReqDTO> cardMailAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardMailShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardMailShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardMailModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardMailModifyReqDTO cardMailModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardMailModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardMailModifyReqDTO cardMailModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardMailRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardMailRemoveReqDTO cardMailRemoveReqDTO);
}
