package com.fenbei.fx.card.service.cardmail.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.enums.DeleteFlagEnum;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.object.MapUtils;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmail.CardMailDAO;
import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.cardmail.domain.CardMailDO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailAddReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailRemoveReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailShowResDTO;
import com.fenbei.fx.card.service.cardmail.converter.CardMailConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 实体卡邮寄管理表 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Slf4j
@Component
public class CardMailManager extends BaseManagerImpl<CardMailDAO, CardMailPO, CardMailDTO, CardMailConverter> {

    public static CardMailManager me() {
        return SpringUtil.getBean(CardMailManager.class);
    }

    public List<CardMailListResDTO> list(final CardMailListReqDTO cardMailListReqDTO) {
        CardMailDTO paramsDTO = CardMailDO.me().buildListParamsDTO(cardMailListReqDTO);

        List<CardMailDTO> cardMailDTOList = super.findList(paramsDTO);

        return CardMailDO.me().transferCardMailListResDTOList(cardMailDTOList);
    }

    public CardMailListResDTO listOne(final CardMailListReqDTO cardMailListReqDTO) {
        CardMailDTO paramsDTO = CardMailDO.me().buildListParamsDTO(cardMailListReqDTO);

        CardMailDTO cardMailDTO = super.findOne(paramsDTO);

        return CardMailDO.me().transferCardMailListResDTO(cardMailDTO);
    }

    public Page<CardMailPageResDTO> pagination(final CardMailPageReqDTO cardMailPageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardMailPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge(CardMailPO.DB_COL_ID,0);
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getBankCardNo()),CardMailPO.DB_COL_BANK_CARD_NO,cardMailPageReqDTO.getBankCardNo());
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getEmployeeName()),CardMailPO.DB_COL_EMPLOYEE_NAME,cardMailPageReqDTO.getEmployeeName());
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getEmployeePhone()),CardMailPO.DB_COL_EMPLOYEE_PHONE,cardMailPageReqDTO.getEmployeePhone());
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getCardMailNo()),CardMailPO.DB_COL_CARD_MAIL_NO,cardMailPageReqDTO.getCardMailNo());
        queryWrapper.eq(Objects.nonNull(cardMailPageReqDTO.getForwardFlag()),CardMailPO.DB_COL_FORWARD_FLAG,cardMailPageReqDTO.getForwardFlag());
        queryWrapper.eq(Objects.nonNull(cardMailPageReqDTO.getFbtReceiveStatus()),CardMailPO.DB_COL_FBT_RECEIVE_STATUS,cardMailPageReqDTO.getFbtReceiveStatus());
        queryWrapper.eq(Objects.nonNull(cardMailPageReqDTO.getForwardStatus()),CardMailPO.DB_COL_FORWARD_STATUS,cardMailPageReqDTO.getForwardStatus());
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getForwardNo()),CardMailPO.DB_COL_FORWARD_NO,cardMailPageReqDTO.getForwardNo());
        if(Objects.nonNull(cardMailPageReqDTO.getCreateTimeStart())&&Objects.nonNull(cardMailPageReqDTO.getCreateTimeEnd())){
            queryWrapper.between(CardMailPO.DB_COL_CARD_ISSUANCE_TIME,cardMailPageReqDTO.getCreateTimeStart(),cardMailPageReqDTO.getCreateTimeEnd());
        }
        queryWrapper.eq(Objects.nonNull(cardMailPageReqDTO.getSignStatus()),CardMailPO.DB_COL_SIGN_STATUS,cardMailPageReqDTO.getSignStatus());
        queryWrapper.eq(StringUtils.isNotBlank(cardMailPageReqDTO.getCardPlatform()),CardMailPO.DB_COL_CARD_PLATFORM,cardMailPageReqDTO.getCardPlatform());
        queryWrapper.orderByDesc("id");
        Page<CardMailDTO> cardMailDTOPage = super.findPage(queryWrapper, current, size);

        return CardMailDO.me().transferCardMailPageResDTOPage(cardMailDTOPage);
    }

    public CardMailDTO queryOneByfxCardId(String fxCardId){
        QueryWrapper<CardMailPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardMailPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardMailPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        CardMailDTO cardMailDTO =  this.findOne(queryWrapper);
        return cardMailDTO;
    }


    public Boolean add(final CardMailAddReqDTO cardMailAddReqDTO) {
        CardMailDO.me().checkCardMailAddReqDTO(cardMailAddReqDTO);

        CardMailDTO addCardMailDTO = CardMailDO.me().buildAddCardMailDTO(cardMailAddReqDTO);

        return super.saveDTO(addCardMailDTO);
    }

    public Boolean addAllColumn(final CardMailAddReqDTO cardMailAddReqDTO) {
        CardMailDO.me().checkCardMailAddReqDTO(cardMailAddReqDTO);

        CardMailDTO addCardMailDTO = CardMailDO.me().buildAddCardMailDTO(cardMailAddReqDTO);

        return super.saveAllColumn(addCardMailDTO);
    }

    public Boolean addBatchAllColumn(final List<CardMailAddReqDTO> cardMailAddReqDTOList) {
        CardMailDO.me().checkCardMailAddReqDTOList(cardMailAddReqDTOList);

        List<CardMailDTO> addBatchCardMailDTOList = CardMailDO.me().buildAddBatchCardMailDTOList(cardMailAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardMailDTOList);
    }

    public CardMailShowResDTO show(final String id) {
        CardMailDTO cardMailDTO = super.findById(id);

        return CardMailDO.me().transferCardMailShowResDTO(cardMailDTO);
    }

    public List<CardMailShowResDTO> showByIds(final List<String> ids) {
        CardMailDO.me().checkIds(ids);

        List<CardMailDTO> cardMailDTOList = super.findBatchIds(ids);

        return CardMailDO.me().transferCardMailShowResDTOList(cardMailDTOList);
    }

    public Boolean modify(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        CardMailDO.me().checkCardMailModifyReqDTO(cardMailModifyReqDTO);

        CardMailDTO modifyCardMailDTO = CardMailDO.me().buildModifyCardMailDTO(cardMailModifyReqDTO);

        return super.modifyById(modifyCardMailDTO);
    }

    public Boolean modifyAllColumn(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        CardMailDO.me().checkCardMailModifyReqDTO(cardMailModifyReqDTO);

        CardMailDTO modifyCardMailDTO = CardMailDO.me().buildModifyCardMailDTO(cardMailModifyReqDTO);

        return super.modifyAllColumnById(modifyCardMailDTO);
    }

    public Boolean removeByParams(final CardMailRemoveReqDTO cardMailRemoveReqDTO) {
        CardMailDO.me().checkCardMailRemoveReqDTO(cardMailRemoveReqDTO);

        CardMailDTO removeCardMailDTO = CardMailDO.me().buildRemoveCardMailDTO(cardMailRemoveReqDTO);

        return super.remove(removeCardMailDTO);
    }

    @Override
    protected CardMailPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardMailPO();
        }

        return (CardMailPO) MapUtils.toBean(map, CardMailPO.class);
    }

    @Override
    protected CardMailDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardMailDTO();
        }

        return (CardMailDTO) MapUtils.toBean(map, CardMailDTO.class);
    }


}
