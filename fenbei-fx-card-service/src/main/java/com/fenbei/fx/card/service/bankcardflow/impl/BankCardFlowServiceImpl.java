package com.fenbei.fx.card.service.bankcardflow.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.bankcardflow.po.BankCardFlowPO;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import com.fenbei.fx.card.service.bankcardflow.manager.BankCardFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Service
public class BankCardFlowServiceImpl extends BaseServiceImpl<BankCardFlowManager, BankCardFlowPO, BankCardFlowDTO> implements BankCardFlowService {

    @Override
    public List<BankCardFlowListResDTO> list(final BankCardFlowListReqDTO bankCardFlowListReqDTO) {
        return manager.list(bankCardFlowListReqDTO);
    }

    @Override
    public BankCardFlowListResDTO listOne(final BankCardFlowListReqDTO bankCardFlowListReqDTO) {
        return manager.listOne(bankCardFlowListReqDTO);
    }

    @Override
    public Page<BankCardFlowPageResDTO> pagination(final BankCardFlowPageReqDTO bankCardFlowPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(bankCardFlowPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        return manager.add(bankCardFlowAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        return manager.addAllColumn(bankCardFlowAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList) {
        return manager.addBatchAllColumn(bankCardFlowAddReqDTOList);
    }

    @Override
    public BankCardFlowShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<BankCardFlowShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        return manager.modify(bankCardFlowModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        return manager.modifyAllColumn(bankCardFlowModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO) {
        return manager.removeByParams(bankCardFlowRemoveReqDTO);
    }
}
