package com.fenbei.fx.card.service.cardholderoperflow.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholderoperflow.po.CardholderOperFlowPO;
import com.fenbei.fx.card.service.cardholderoperflow.converter.CardholderOperFlowConverter;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 持卡人被操作流水 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardholderOperFlowDO extends BaseDO<CardholderOperFlowDTO, CardholderOperFlowPO, CardholderOperFlowConverter> {

    public static CardholderOperFlowDO me() {
        return SpringUtil.getBean(CardholderOperFlowDO.class);
    }

    public void checkCardholderOperFlowAddReqDTO(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        if (Func.isEmpty(cardholderOperFlowAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkCardholderOperFlowAddReqDTOList(final List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList) {
        if (Func.isEmpty(cardholderOperFlowAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("COLL_SIZE_NOT_EMPTY"));
        }
    }

    public void checkCardholderOperFlowModifyReqDTO(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        if (Func.isEmpty(cardholderOperFlowModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkCardholderOperFlowRemoveReqDTO(final CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO) {
        if (Func.isEmpty(cardholderOperFlowRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public CardholderOperFlowDTO buildListParamsDTO(final CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO) {
        return converter.convertToCardholderOperFlowDTO(cardholderOperFlowListReqDTO);
    }

    public CardholderOperFlowDTO buildPageParamsDTO(final CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO) {
        return converter.convertToCardholderOperFlowDTO(cardholderOperFlowPageReqDTO);
    }

    public CardholderOperFlowDTO buildAddCardholderOperFlowDTO(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        return converter.convertToCardholderOperFlowDTO(cardholderOperFlowAddReqDTO);
    }

    public List<CardholderOperFlowDTO> buildAddBatchCardholderOperFlowDTOList(final List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList) {
        return converter.convertToCardholderOperFlowDTOList(cardholderOperFlowAddReqDTOList);
    }

    public CardholderOperFlowDTO buildModifyCardholderOperFlowDTO(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        return converter.convertToCardholderOperFlowDTO(cardholderOperFlowModifyReqDTO);
    }

    public CardholderOperFlowDTO buildRemoveCardholderOperFlowDTO(final CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO) {
        return converter.convertToCardholderOperFlowDTO(cardholderOperFlowRemoveReqDTO);
    }

    public List<CardholderOperFlowListResDTO> transferCardholderOperFlowListResDTOList(final List<CardholderOperFlowDTO> cardholderOperFlowDTOList) {
        if (Func.isEmpty(cardholderOperFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardholderOperFlowListResDTOList(cardholderOperFlowDTOList);
    }

    public CardholderOperFlowListResDTO transferCardholderOperFlowListResDTO(final CardholderOperFlowDTO cardholderOperFlowDTO) {
        if (Func.isEmpty(cardholderOperFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardholderOperFlowListResDTO(cardholderOperFlowDTO);
    }

    public Page<CardholderOperFlowPageResDTO> transferCardholderOperFlowPageResDTOPage(final Page<CardholderOperFlowDTO> cardholderOperFlowDTOPage) {
        if (Func.isEmpty(cardholderOperFlowDTOPage) || Func.isEmpty(cardholderOperFlowDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardholderOperFlowPageResDTOPage(cardholderOperFlowDTOPage);
    }

    public CardholderOperFlowShowResDTO transferCardholderOperFlowShowResDTO(final CardholderOperFlowDTO cardholderOperFlowDTO) {
        if (Func.isEmpty(cardholderOperFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardholderOperFlowShowResDTO(cardholderOperFlowDTO);
    }

    public List<CardholderOperFlowShowResDTO> transferCardholderOperFlowShowResDTOList(final List<CardholderOperFlowDTO> cardholderOperFlowDTOList) {
        if (Func.isEmpty(cardholderOperFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardholderOperFlowShowResDTOList(cardholderOperFlowDTOList);
    }
}
