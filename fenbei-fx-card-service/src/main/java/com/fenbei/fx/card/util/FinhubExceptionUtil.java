package com.fenbei.fx.card.util;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;

public class FinhubExceptionUtil {

    public static FinhubException exceptionFrom (GlobalCoreResponseCode globalCoreResponseCode) {
        return new FinhubException(globalCoreResponseCode.getCode(), globalCoreResponseCode.getType(), globalCoreResponseCode.getMessage());
    }

    public static FinhubException exceptionFrom (GlobalCoreResponseCode globalCoreResponseCode, String message) {
        return new FinhubException(globalCoreResponseCode.getCode(), globalCoreResponseCode.getType(), message);
    }
}
