package com.fenbei.fx.card.service.cardcreditapplyorder.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.api.card.dto.EmployeeBankInfoReq;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.ActiveModelEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderStateEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderTypeEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderSubTypeEnum;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.constants.RedisKeyConstant;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.*;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyRespDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardmodelconfig.dto.EmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.impl.CardModelConfigServiceImpl;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.service.remote.SaasBudgetManager;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.fenbeitong.saasplus.api.model.dto.budget.VirtualCardControlConf;
import com.fenbeitong.saasplus.api.service.budget.IVirtualCardBudgetService;
import com.fenbeitong.travel.rule.api.model.apply.ApplyDTO;
import com.fenbeitong.travel.rule.api.service.apply.IApplyService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeWithCompanyAndOrgDTO;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.employee.IREmployeeService;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardcreditapplyorder.po.CardCreditApplyOrderPO;
import com.fenbei.fx.card.service.cardcreditapplyorder.CardCreditApplyOrderService;
import com.fenbei.fx.card.service.cardcreditapplyorder.manager.CardCreditApplyOrderManager;
import com.finhub.framework.swift.utils.ObjUtils;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.fenbei.fx.card.common.constant.GlobalCoreResponseCode.APPLY_CREDIT_NOT_EXIST;

/**
 * 国际卡额度发放单 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Slf4j
@Service
public class CardCreditApplyOrderServiceImpl extends BaseServiceImpl<CardCreditApplyOrderManager, CardCreditApplyOrderPO, CardCreditApplyOrderDTO> implements CardCreditApplyOrderService {

    //虚拟卡管控模式RPC
    @DubboReference(interfaceName = "iVirtualCardBudgetService")
    private IVirtualCardBudgetService iVirtualCardBudgetService;

    @DubboReference(interfaceName = "iREmployeeService")
    private IREmployeeService irEmployeeService;

    @DubboReference(interfaceName = "iApplyService")
    private IApplyService iApplyService;

    @DubboReference(interfaceName = "iBaseEmployeeExtService")
    private IBaseEmployeeExtService iBaseEmployeeExtService;

    @Autowired
    private CardService cardService;

    @Autowired
    CardModelConfigServiceImpl cardModelConfigService;

    @Autowired
    protected RedissonService redissonService;


    @Autowired
    protected CardCreditManagerService cardCreditManagerService;


    @Override
    public List<CardCreditApplyOrderListResDTO> list(final CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO) {
        return manager.list(cardCreditApplyOrderListReqDTO);
    }

    @Override
    public CardCreditApplyOrderListResDTO listOne(final CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO) {
        return manager.listOne(cardCreditApplyOrderListReqDTO);
    }

    @Override
    public Page<CardCreditApplyOrderPageResDTO> pagination(final CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardCreditApplyOrderPageReqDTO, current, size);
    }

    @Override
    public Page<CardCreditApplyOrderPageResDTO> paginationByCondition(final CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO, final Integer current,
                                                                      final Integer size) {
        if (StringUtils.isBlank(cardCreditApplyOrderPageReqDTO.getCompanyId())){
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "companyId不能为空");
        }
        return manager.paginationByCondition(cardCreditApplyOrderPageReqDTO, current, size);
    }


    @Override
    public Boolean add(CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        return manager.add(cardCreditApplyOrderAddReqDTO);
    }

    @Override
    public CardCreditApplyOrderDTO createApplyOrder(final CardCreditApplyOrderAddReqVO cardCreditApplyOrderAddReqVO) {
        log.info("开始创建额度发放单, params={}", JSONObject.toJSONString(cardCreditApplyOrderAddReqVO));
        String meaningNo = cardCreditApplyOrderAddReqVO.getMeaningNo();
        //0.幂等加锁
        String lockKey = RedisKeyConstant.CREATE_CARD_CREDIT_ORDER_LOCK +meaningNo;
        try {
            boolean b = redissonService.tryLock(lockKey);
            if (!b) {
                throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST);
            }
            //1:基础合法性参数检查(包括：海外卡模式是否正确)
            checkOrderAddReqDTO(cardCreditApplyOrderAddReqVO);
            String companyId = cardCreditApplyOrderAddReqVO.getCompanyId();
            CardCreditApplyOrderDTO dtoByMeaningNo = manager.findDTOByMeaningNo(meaningNo);
            if (!ObjUtils.isEmpty(dtoByMeaningNo)) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, meaningNo+"已存在,请勿重复创建");
            }
            // 获取虚拟卡的预算管控模式 1申请扣，0核销扣
            Integer deductionMode =1;
            try {
                VirtualCardControlConf virtualCardControlConf = iVirtualCardBudgetService.queryControlConf(companyId);
                if (null == virtualCardControlConf) {
                    log.info("查询企业的预算管控方式错误companyId=" + companyId);
                }
                deductionMode = virtualCardControlConf.getVirtualCardCtrlMode();
            } catch (Exception e) {
                log.error("查询企业的预算管控方式错误companyId=" + companyId, e);
            }
            cardCreditApplyOrderAddReqVO.setDeductionMode(deductionMode);
            //保存单据
            CardCreditApplyOrderAddResVO cardCreditApplyOrderAddResVO = saveApplyOrder(companyId,cardCreditApplyOrderAddReqVO);
            //调用发放接口
            CardCreditApplyOrderDTO cardCreditApplyOrderDTO = pushApplyAndUpdateStatus(cardCreditApplyOrderAddResVO.getApplyOrderId());
            log.info("创建额度发放单成功, companyId={},cardCreditApplyOrderAddResVO={}", companyId,cardCreditApplyOrderAddResVO);
            return cardCreditApplyOrderDTO;
        } catch (FxCardException e) {
            log.error("创建额度发放单失败, 业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("创建额度发放单失败, 系统异常", e);
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "创建额度发放单失败, 系统异常");
        } finally {
            try {
                redissonService.unLock(lockKey);
            } catch (Exception e) {
                FinhubLogger.error("createApplyOrder unlock|err=", e);
            }
        }
    }



    @Override
    public CardCreditApplyOrderDTO modifyByApplyOrderId(String companyId, CardCreditApplyOrderModifyReqVO creditApplyOrderModifyReqVO) {
        log.info("更新额度发放单, params={}", JSONObject.toJSONString(creditApplyOrderModifyReqVO));
        // 1. 参数校验，字段缺失直接抛异常
        validateModifyReqVO(creditApplyOrderModifyReqVO);

        String applyOrderId = creditApplyOrderModifyReqVO.getApplyOrderId();

        // 2. 通过applyOrderId查找对象是否存在
        CardCreditApplyOrderShowResDTO existingOrder = manager.showByApplyOrderId(companyId, applyOrderId);
        if (existingOrder == null) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "发放单不存在");
        }

        // 3. 构建修改DTO
        CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO = buildModifyReqDTO(creditApplyOrderModifyReqVO, existingOrder);
        Date nowDate = new Date();
        checkModifyApplyDetail(companyId, creditApplyOrderModifyReqVO, existingOrder, cardCreditApplyOrderModifyReqDTO, nowDate);
        // 4. 保存更新数据
        Boolean updateResult = manager.modify(cardCreditApplyOrderModifyReqDTO);
        if (!updateResult) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION, "更新发放单失败");
        }
        // 5. 如果无错误，调用CardCreditManagerManager.me().apply完成数据推送
        //调用发放接口
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = pushApplyAndUpdateStatus(applyOrderId);
        log.info("更新额度发放单成功,cardCreditApplyOrderDTO={}",cardCreditApplyOrderDTO);
        return cardCreditApplyOrderDTO;

    }

    private void checkModifyApplyDetail(String companyId, CardCreditApplyOrderModifyReqVO creditApplyOrderModifyReqVO, CardCreditApplyOrderShowResDTO existingOrder, CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO, Date nowDate) {
        // 如果是备用金模式，检查申请人是否有发放中的发放单
        if (Objects.equals(creditApplyOrderModifyReqVO.getActiveModel(), ActiveModelEnum.PETTY.getCode())) {
            if (StringUtils.isAnyBlank(companyId,  existingOrder.getApplicantId())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "公司ID和申请人ID不能为空");
            }
            boolean hasGrantingOrder = manager.hasGrantingApplyOrder(companyId, existingOrder.getApplicantId());
            if (hasGrantingOrder) {
                cardCreditApplyOrderModifyReqDTO.setIssuedTime(nowDate);
                cardCreditApplyOrderModifyReqDTO.setUpdateTime(nowDate);
                cardCreditApplyOrderModifyReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
                cardCreditApplyOrderModifyReqDTO.setApplyResultDesc("备用金模式下申请人存在待发放、发放中的发放单，修改失败");
                log.warn("备用金模式下申请人存在待发放、发放中的发放单，修改失败。companyId={}, applicantId={}", companyId, existingOrder.getApplicantId());
                return ;
            }

            List<CardCreditManagerDTO> uncheckApplylist = cardCreditManagerService.queryUncheckApply(existingOrder.getFxCardId());
            if (CollectionUtils.isNotEmpty(uncheckApplylist)) {
                cardCreditApplyOrderModifyReqDTO.setIssuedTime(nowDate);
                cardCreditApplyOrderModifyReqDTO.setUpdateTime(nowDate);
                cardCreditApplyOrderModifyReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
                cardCreditApplyOrderModifyReqDTO.setApplyResultDesc("备用金模式下申请人存在未核销的发放单，修改失败");
                log.warn("备用金模式下申请人存在未核销的发放单，修改失败。companyId={}, applicantId={}", companyId, existingOrder.getApplicantId());
                return ;
            }
        }

        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(companyId, existingOrder.getApplicantId());
        //当前生效模式
        Integer activeModel = employeeModelConfigDTO.getActiveModel();
        log.info("申请人使用模式与配置。employeeModelConfigDTO.activeModel={}, creditApplyOrderModifyReqVO.activeModel={}", activeModel, creditApplyOrderModifyReqVO.getActiveModel());
        if (!activeModel.equals(creditApplyOrderModifyReqVO.getActiveModel())) {
            cardCreditApplyOrderModifyReqDTO.setIssuedTime(nowDate);
            cardCreditApplyOrderModifyReqDTO.setUpdateTime(nowDate);
            cardCreditApplyOrderModifyReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
            cardCreditApplyOrderModifyReqDTO.setApplyResultDesc("申请人使用模式与配置不符，变更失败");
            return;
        }
        cardCreditApplyOrderModifyReqDTO.setUpdateTime(nowDate);
        cardCreditApplyOrderModifyReqDTO.setApplyState(ApplyOrderStateEnum.WAITING.getCode());
        cardCreditApplyOrderModifyReqDTO.setApplyResultDesc("");
    }

    @Override
    public Boolean removeByApplyOrderId(String companyId, String applyOrderId) {
        if (StringUtils.isAnyBlank(companyId, applyOrderId)) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "公司ID和发放单ID不能为空");
        }
        // 1. 通过applyOrderId查找对象是否存在
        CardCreditApplyOrderShowResDTO existingOrder = manager.showByApplyOrderId(companyId,applyOrderId);
        if (existingOrder == null) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "发放单不存在");
        }
        // 2. 校验发放单状态是否允许删除
        if (!ApplyOrderStateEnum.canRemove(existingOrder.getApplyState())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "发放单状态不允许删除");
        }
        // 3. 校验申请单状态是否允许删除
        List<ApplyDTO> applyDTOS = iApplyService.batchQueryApply(Lists.newArrayList(existingOrder.getApplyId()));
        if (!applyDTOS.isEmpty()){
            ApplyDTO applyDTO = applyDTOS.get(0);
            //8是作废可以删除其他都不行
            if(applyDTO.getState()!=8){
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "有关联申请单不允许删除。");
            }

        }
        // 4. 调用删除方法
        Boolean removeResult = manager.removeByApplyOrderId(applyOrderId);
        if (!removeResult) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION, "删除发放单失败");
        }
        return removeResult;
    }


    public CardCreditApplyOrderAddResVO saveApplyOrder(String companyId, CardCreditApplyOrderAddReqVO creditApplyOrderAddReqVO) {

        CardCreditApplyOrderAddResVO cardCreditApplyOrderAddResVO = new CardCreditApplyOrderAddResVO();
        Date nowDate = new Date();

        //创建中
        CardCreditApplyOrderAddReqDTO applyOrderAddReqDTO = buildOrderAddReqListDTO(companyId, creditApplyOrderAddReqVO);

        checkApplyOrderDetailInfo(companyId, applyOrderAddReqDTO, nowDate);
        //保存
        Boolean added = manager.add(applyOrderAddReqDTO);
        if (!added) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "额度发放单入库失败");
        }
        BeanUtils.copyProperties(applyOrderAddReqDTO, cardCreditApplyOrderAddResVO);
        return cardCreditApplyOrderAddResVO;
    }

    private void checkApplyOrderDetailInfo(String companyId, CardCreditApplyOrderAddReqDTO applyOrderAddReqDTO, Date nowDate) {
        // 如果是备用金模式，检查申请人是否有发放中的发放单
        if (Objects.equals(applyOrderAddReqDTO.getActiveModel(), ActiveModelEnum.PETTY.getCode())) {
            if (StringUtils.isAnyBlank(companyId,  applyOrderAddReqDTO.getApplicantId())) {
                throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "公司ID和申请人ID不能为空");
            }
            boolean hasGrantingOrder = manager.hasGrantingApplyOrder(companyId, applyOrderAddReqDTO.getApplicantId());
            if (hasGrantingOrder) {
                applyOrderAddReqDTO.setIssuedTime(nowDate);
                applyOrderAddReqDTO.setUpdateTime(nowDate);
                applyOrderAddReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
                applyOrderAddReqDTO.setApplyResultDesc("备用金模式下申请人存在待发放、发放中的发放单，创建失败");
                log.warn("备用金模式下申请人存在发放中的发放单，创建失败。companyId={}, applicantId={}", companyId, applyOrderAddReqDTO.getApplicantId());
                return;
            }
            List<CardCreditManagerDTO> uncheckApplylist = cardCreditManagerService.queryUncheckApply(applyOrderAddReqDTO.getFxCardId());
            if (CollectionUtils.isNotEmpty(uncheckApplylist)) {
                applyOrderAddReqDTO.setIssuedTime(nowDate);
                applyOrderAddReqDTO.setUpdateTime(nowDate);
                applyOrderAddReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
                applyOrderAddReqDTO.setApplyResultDesc("备用金模式下申请人存在未核销的发放单，创建失败");
                log.warn("备用金模式下申请人存在未核销的发放单，创建失败。companyId={}, applicantId={}", companyId, applyOrderAddReqDTO.getApplicantId());
                return ;
            }
        }

        //模式不否和创建失败
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(companyId, applyOrderAddReqDTO.getApplicantId());
        //当前生效模式
        Integer activeModel = employeeModelConfigDTO.getActiveModel();
        log.info("申请人使用模式与配置。employeeModelConfigDTO.activeModel={}, applyOrderAddReqDTO.activeModel={}", activeModel, applyOrderAddReqDTO.getActiveModel());
        if (!activeModel.equals(applyOrderAddReqDTO.getActiveModel())) {
            applyOrderAddReqDTO.setIssuedTime(nowDate);
            applyOrderAddReqDTO.setUpdateTime(nowDate);
            applyOrderAddReqDTO.setApplyState(ApplyOrderStateEnum.CREATE_FAIL.getCode());
            applyOrderAddReqDTO.setApplyResultDesc("申请人使用模式与配置不符，创建失败");
            return ;
        }
        applyOrderAddReqDTO.setUpdateTime(nowDate);
        applyOrderAddReqDTO.setApplyState(ApplyOrderStateEnum.WAITING.getCode());
        applyOrderAddReqDTO.setApplyResultDesc("");
    }


    public CardCreditApplyOrderDTO pushApplyAndUpdateStatus(String applyOrderId){
        CardCreditApplyOrderDTO dtoByApplyOrderId = manager.findDTOByApplyOrderId(applyOrderId);
        Date nowDate = new Date();
        if(!ApplyOrderStateEnum.canGrant(dtoByApplyOrderId.getApplyState())){
            return dtoByApplyOrderId;
        }
        //请求额度下发
        CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO = convertToCardCreditManagerApplyReqDTO(dtoByApplyOrderId);
        log.info("海外卡额度申请：{}", JsonUtils.toJson(cardCreditApplyRpcReqDTO));
        CardCreditManagerApplyRespDTO cardCreditManagerApplyRespDTO;
        try {
            //调用发放接口
            cardCreditManagerApplyRespDTO = CardCreditManagerManager.me().apply(cardCreditApplyRpcReqDTO);
            log.info("海外卡额度申请结果：request={},response={}", JsonUtils.toJson(cardCreditApplyRpcReqDTO),JsonUtils.toJson(cardCreditManagerApplyRespDTO));
            if(ObjUtils.isEmpty(cardCreditManagerApplyRespDTO)){
                dtoByApplyOrderId.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                dtoByApplyOrderId.setApplyResultDesc("发放返回为空");
            }else{
                if(cardCreditManagerApplyRespDTO.getApplyStatus()){
                    dtoByApplyOrderId.setIssuedTime(nowDate);
                    dtoByApplyOrderId.setApplyState(ApplyOrderStateEnum.GRANT_SUCCESS.getCode());
                    dtoByApplyOrderId.setApplyResultDesc(cardCreditManagerApplyRespDTO.getFailedReason());
                }else{
                    dtoByApplyOrderId.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                    dtoByApplyOrderId.setApplyResultDesc(cardCreditManagerApplyRespDTO.getFailedReason());
                }
            }
        } catch (FxCardException fxCardException) {
            log.error("海外卡额度申请失败：{}", JsonUtils.toJson(cardCreditApplyRpcReqDTO),fxCardException);
            dtoByApplyOrderId.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
            dtoByApplyOrderId.setApplyResultDesc(fxCardException.getMessage());
        } catch (Exception e) {
            log.error("海外卡额度申请失败：{}", JsonUtils.toJson(cardCreditApplyRpcReqDTO),e);
            dtoByApplyOrderId.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
            dtoByApplyOrderId.setApplyResultDesc(e.getMessage());
        }
        dtoByApplyOrderId.setUpdateTime(nowDate);
        //更新申请结果
        CardCreditApplyOrderPO cardCreditApplyOrderPO = new CardCreditApplyOrderPO();
        BeanUtils.copyProperties(dtoByApplyOrderId, cardCreditApplyOrderPO);;
        boolean update = manager.updateById(cardCreditApplyOrderPO);
        if(!update){
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "额度发放单更新失败");
        }
        return dtoByApplyOrderId;
    }




    private CardCreditApplyOrderAddReqDTO buildOrderAddReqListDTO(String companyId, CardCreditApplyOrderAddReqVO orderAddListReqDTO) {
        //1: 生成批量导入批次号
        String applyBatchOrderId = BizIdUtils.getFxCreditApplyBatchOrderId();
        // 构建发放单列表
        CardCreditApplyOrderAddReqDTO applyOrderAddReqDTO = makeSingleApplyOrder(companyId, orderAddListReqDTO, applyBatchOrderId);
        return applyOrderAddReqDTO;
    }

    private List<EmployeeWithCompanyAndOrgDTO> getEmployeesOrg(List<String> employeeIds) {
        List<EmployeeWithCompanyAndOrgDTO> employeeList = irEmployeeService.listIdMatchOrgUnitEmployees(employeeIds);
        if (ObjUtils.isEmpty(employeeList)) {
            log.info("查询员工部门为空 param:" + JSONObject.toJSONString(employeeIds));
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "查询员工所在部门为空!");
        }
        if (employeeList.size() != employeeIds.size()) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "传入员工集合和查询结果不一致!");
        }
        return employeeList;
    }

    private CardCreditApplyOrderAddReqDTO makeSingleApplyOrder(String companyId, CardCreditApplyOrderAddReqVO orderAddListReqVO, String applyBatchNo) {
        CardCreditApplyOrderAddReqDTO applyOrderAddReqDTO = new CardCreditApplyOrderAddReqDTO();
        List<EmployeeBankInfoReq> employeeBankInfoReqs = orderAddListReqVO.getUserMap();
        EmployeeBankInfoReq employeeBankInfoReq = employeeBankInfoReqs.get(0);
        //卡信息
        CardDTO cardDTO = cardService.cardDetailByBankAccountNo(employeeBankInfoReq.getBankAccountNo());
        if(ObjUtils.isEmpty(cardDTO)){
            cardDTO = cardService.cardDetailByFxCardId(employeeBankInfoReq.getFxCardId());
            if(ObjUtils.isEmpty(cardDTO)) {
                throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "卡信息不存在!");
            }
        }
        EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(cardDTO.getEmployeeId(), companyId);
        //申请人
        applyOrderAddReqDTO.setApplicantId(cardDTO.getEmployeeId());
        applyOrderAddReqDTO.setApplicantName(ObjUtils.isEmpty(employeeContract)?cardDTO.getNameOnCard():employeeContract.getName());
        applyOrderAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
        applyOrderAddReqDTO.setFxCardId(cardDTO.getFxCardId());
        applyOrderAddReqDTO.setBankName(cardDTO.getCardPlatform());
        applyOrderAddReqDTO.setCurrency(cardDTO.getCurrency());

        applyOrderAddReqDTO.setApplyOrderId(orderAddListReqVO.getApplyOrderId());
        applyOrderAddReqDTO.setApplyBatchNo(applyBatchNo);
        applyOrderAddReqDTO.setApplyId(orderAddListReqVO.getApplyId());
        applyOrderAddReqDTO.setMeaningNo(orderAddListReqVO.getMeaningNo());
        applyOrderAddReqDTO.setApplyOrderSubType(orderAddListReqVO.getApplyOrderSubType());
        applyOrderAddReqDTO.setApplyOrderType(orderAddListReqVO.getApplyOrderType());

        applyOrderAddReqDTO.setActiveModel(orderAddListReqVO.getActiveModel());

        applyOrderAddReqDTO.setApplyState(ApplyOrderStateEnum.WAITING.getCode());
        applyOrderAddReqDTO.setApplyResultDesc("");
        applyOrderAddReqDTO.setCreateTime(new Date());
        applyOrderAddReqDTO.setUpdateTime(new Date());

        applyOrderAddReqDTO.setCompanyId(companyId);
        applyOrderAddReqDTO.setTitle(orderAddListReqVO.getTitle());
        applyOrderAddReqDTO.setApplyAmount(BigDecimalUtils.yuan2fen(orderAddListReqVO.getApplyAmount()));

        applyOrderAddReqDTO.setApplicantOrgId(ObjUtils.isEmpty(employeeContract) ? null : employeeContract.getOrg_id());
        applyOrderAddReqDTO.setApplicantOrgName(ObjUtils.isEmpty(employeeContract) ? null : employeeContract.getOrg_name());

        applyOrderAddReqDTO.setApplyReason(orderAddListReqVO.getApplyReason());
        applyOrderAddReqDTO.setApplyReasonId(orderAddListReqVO.getApplyReasonId());

        applyOrderAddReqDTO.setCreaterId(orderAddListReqVO.getCreaterId());
        applyOrderAddReqDTO.setCreaterName(orderAddListReqVO.getCreaterName());

        applyOrderAddReqDTO.setIssuedId(orderAddListReqVO.getIssuedId());
        applyOrderAddReqDTO.setIssuedName(orderAddListReqVO.getIssuedName());

        applyOrderAddReqDTO.setDeductionMode(orderAddListReqVO.getDeductionMode());
        applyOrderAddReqDTO.setCreateTime(new Date());
        applyOrderAddReqDTO.setUpdateTime(new Date());

        //费用归属相关
        applyOrderAddReqDTO.setCostCategoryId(orderAddListReqVO.getCostCategoryId());
        applyOrderAddReqDTO.setCostCategoryName(orderAddListReqVO.getCostCategoryName());
        applyOrderAddReqDTO.setCostAttributions(JsonUtils.toJson(orderAddListReqVO.getCostInfo()));
        applyOrderAddReqDTO.setCostAttributionOpt(orderAddListReqVO.getCostAttributionOpt());
        applyOrderAddReqDTO.setEmployeeDept(orderAddListReqVO.isEmployeeDept()?1:0);


        return applyOrderAddReqDTO;
    }

    public void  checkOrderAddReqDTO(CardCreditApplyOrderAddReqVO cardCreditApplyOrderAddReqVO){
        if (ObjUtils.isEmpty(cardCreditApplyOrderAddReqVO)) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "申请参数不能为空");
        }
        if (ObjUtils.isEmpty(cardCreditApplyOrderAddReqVO.getUserMap())) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "申请人列表不能为空");
        }
        if (BigDecimalUtils.hasNoPrice(cardCreditApplyOrderAddReqVO.getApplyAmount())) {
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "申请金额必须大于0");
        }
        if(null == cardCreditApplyOrderAddReqVO.getApplyOrderType() || StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getTitle()) || StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getApplyReason())){
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL);
        }
        if(cardCreditApplyOrderAddReqVO.getTitle().length()>30){
            throw new FxCardException(GlobalCoreResponseCode.TITLE_ORDER_LENGTH_MAX);
        }
        if(cardCreditApplyOrderAddReqVO.getApplyReason().length()>200){
            throw new FxCardException(GlobalCoreResponseCode.REASON_ORDER_LENGTH_MAX);
        }

        // 校验活动模式
        if (cardCreditApplyOrderAddReqVO.getActiveModel() == null ||
                !ActiveModelEnum.isActiveModel(cardCreditApplyOrderAddReqVO.getActiveModel())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "生效模式参数错误");
        }
    }

    @Override
    public Boolean addAllColumn(final CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO) {
        return manager.addAllColumn(cardCreditApplyOrderAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList) {
        return manager.addBatchAllColumn(cardCreditApplyOrderAddReqDTOList);
    }

    @Override
    public CardCreditApplyOrderShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public CardCreditApplyOrderShowResDTO showByApplyOrderId(String companyId, final String applyOrderId) {
        if (StringUtils.isAnyBlank(companyId, applyOrderId)) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "公司ID和发放单ID不能为空");
        }
        CardCreditApplyOrderShowResDTO cardCreditApplyOrderShowResDTO = manager.showByApplyOrderId(companyId, applyOrderId);
        if(ObjUtils.isEmpty(cardCreditApplyOrderShowResDTO)){
            return null;
        }
        cardCreditApplyOrderShowResDTO.setApplyAmount(BigDecimalUtils.fen2yuan(cardCreditApplyOrderShowResDTO.getApplyAmount()));
        cardCreditApplyOrderShowResDTO.setApplyOrderSubTypeDesc(ApplyOrderSubTypeEnum.getDescByType(cardCreditApplyOrderShowResDTO.getApplyOrderSubType()));
        cardCreditApplyOrderShowResDTO.setApplyOrderTypeDesc(ApplyOrderTypeEnum.getDescByType(cardCreditApplyOrderShowResDTO.getApplyOrderType()));
        cardCreditApplyOrderShowResDTO.setApplyStateDesc(ApplyOrderStateEnum.getDescByCode(cardCreditApplyOrderShowResDTO.getApplyState()));
        cardCreditApplyOrderShowResDTO.setApproveStateDesc("-");
        cardCreditApplyOrderShowResDTO.setActiveModelDesc(ActiveModelEnum.getEnum(cardCreditApplyOrderShowResDTO.getActiveModel()).getName());
        String costAttributions = cardCreditApplyOrderShowResDTO.getCostAttributions();
        if (com.luastar.swift.base.utils.ObjUtils.isEmpty(costAttributions)) {
            log.warn("费用归属为空, detailVo = {}", JSONObject.toJSON(costAttributions));
            cardCreditApplyOrderShowResDTO.setCostAttributeName("-");
        } else {
            CostInfo costInfo = JsonUtils.toObj(costAttributions, CostInfo.class);
            List<CostAttributionGroup> costAttributionGroupList = costInfo.getCostAttributionGroupList();
            if (com.luastar.swift.base.utils.ObjUtils.isNotEmpty(costAttributionGroupList)) {
                StringBuffer splitName = new StringBuffer();
                for (CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                    if(costAttributionGroup!=null){
                        List<CostAttribution> costAttributionList = costAttributionGroup.getCostAttributionList();
                        if (com.luastar.swift.base.utils.ObjUtils.isNotEmpty(costAttributionList)) {
                            splitName.append(costAttributionGroup.getCategoryName());
                            splitName.append(":");
                            splitName.append(costAttributionList.get(0).getName());
                            splitName.append(";");
                        }
                    }
                }
                cardCreditApplyOrderShowResDTO.setCostAttributeName(org.apache.commons.lang3.StringUtils.substring(splitName.toString(), 0, splitName.length() - 1));
            }else{
                cardCreditApplyOrderShowResDTO.setCostAttributeName("-");
            }
        }

        return cardCreditApplyOrderShowResDTO;
    }

    @Override
    public List<CardCreditApplyOrderShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }



    @Override
    public Boolean modify(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        return manager.modify(cardCreditApplyOrderModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO) {
        return manager.modifyAllColumn(cardCreditApplyOrderModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO) {
        return manager.removeByParams(cardCreditApplyOrderRemoveReqDTO);
    }

    private CardCreditManagerApplyReqDTO convertToCardCreditManagerApplyReqDTO(CardCreditApplyOrderDTO creditApplyOrderDTO) {
        CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO = new CardCreditManagerApplyReqDTO();

        // 设置基本信息
        cardCreditApplyRpcReqDTO.setCompanyId(creditApplyOrderDTO.getCompanyId());
        cardCreditApplyRpcReqDTO.setEmployeeId(creditApplyOrderDTO.getApplicantId());
        cardCreditApplyRpcReqDTO.setBankName(creditApplyOrderDTO.getBankName());
        cardCreditApplyRpcReqDTO.setFxCardId(creditApplyOrderDTO.getFxCardId());

        // 设置申请金额和原因
        cardCreditApplyRpcReqDTO.setApplyCreditAmount(creditApplyOrderDTO.getApplyAmount());
        cardCreditApplyRpcReqDTO.setApplyReason(creditApplyOrderDTO.getApplyReason());
        cardCreditApplyRpcReqDTO.setApplyReasonDesc(creditApplyOrderDTO.getApplyResultDesc());


        // 设置发放单号
        cardCreditApplyRpcReqDTO.setApplyOrderType(creditApplyOrderDTO.getApplyOrderType());
        cardCreditApplyRpcReqDTO.setSaasApplyNo(creditApplyOrderDTO.getApplyOrderId());
        cardCreditApplyRpcReqDTO.setSaasApplyMeaningNo(creditApplyOrderDTO.getMeaningNo());

        // 设置扣款模式
        cardCreditApplyRpcReqDTO.setDeductionMode(creditApplyOrderDTO.getDeductionMode());

        // 设置币种
        cardCreditApplyRpcReqDTO.setCurrency(creditApplyOrderDTO.getCurrency()); // 默认美元，可根据实际情况修改

        // 设置备用金信息
        cardCreditApplyRpcReqDTO.setPettyName(creditApplyOrderDTO.getTitle());

        cardCreditApplyRpcReqDTO.setCostAttributionId(creditApplyOrderDTO.getCostCategoryId());
        cardCreditApplyRpcReqDTO.setCostAttributionName(creditApplyOrderDTO.getCostCategoryName());
        cardCreditApplyRpcReqDTO.setCostAttributionOpt(creditApplyOrderDTO.getCostAttributionOpt());

        String costAttributions = creditApplyOrderDTO.getCostAttributions();
        List<BudgetCostAttributionDTO> budgetCostAttributionDTOList = new ArrayList<>();
        if(StringUtils.isBlank(costAttributions)){
            CostInfo costInfo = JsonUtils.toObj(costAttributions, CostInfo.class);
            costInfo.getCostAttributionGroupList().forEach(costAttribution -> {
                BudgetCostAttributionDTO budgetCostAttributionDTO = new BudgetCostAttributionDTO();
                budgetCostAttributionDTO.setCost_attribution_category(costAttribution.getCategory());
                budgetCostAttributionDTO.setCost_attribution_name(costAttribution.getCostAttributionList().get(0).getName());
                budgetCostAttributionDTO.setCost_attribution_id(costAttribution.getCostAttributionList().get(0).getId());
                budgetCostAttributionDTOList.add(budgetCostAttributionDTO);
            });
        }

        cardCreditApplyRpcReqDTO.setAttributions(budgetCostAttributionDTOList);

        return cardCreditApplyRpcReqDTO;
    }

    private CardCreditManagerApplyReqDTO convertShowResToCardCreditManagerApplyReqDTO(CardCreditApplyOrderShowResDTO orderInfo) {
        CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO = new CardCreditManagerApplyReqDTO();

        // 设置基本信息
        cardCreditApplyRpcReqDTO.setCompanyId(orderInfo.getCompanyId());
        cardCreditApplyRpcReqDTO.setEmployeeId(orderInfo.getApplicantId());
        cardCreditApplyRpcReqDTO.setBankName(orderInfo.getBankName());
        cardCreditApplyRpcReqDTO.setFxCardId(orderInfo.getFxCardId());

        // 设置申请金额和原因
        cardCreditApplyRpcReqDTO.setApplyCreditAmount(orderInfo.getApplyAmount());
        cardCreditApplyRpcReqDTO.setApplyReason(orderInfo.getApplyReason());
        cardCreditApplyRpcReqDTO.setApplyReasonDesc(orderInfo.getApplyResultDesc());

        // 设置发放单号
        cardCreditApplyRpcReqDTO.setSaasApplyNo(orderInfo.getApplyOrderId());
        cardCreditApplyRpcReqDTO.setSaasApplyMeaningNo(orderInfo.getMeaningNo());

        cardCreditApplyRpcReqDTO.setApplyOrderType(orderInfo.getApplyOrderType());

        // 设置扣款模式
        cardCreditApplyRpcReqDTO.setDeductionMode(orderInfo.getDeductionMode());

        // 设置币种
        cardCreditApplyRpcReqDTO.setCurrency(orderInfo.getCurrency());

        // 设置备用金信息
        cardCreditApplyRpcReqDTO.setPettyName(orderInfo.getTitle());

        // 设置费用归属信息（如果有）
        cardCreditApplyRpcReqDTO.setCostAttributionId(orderInfo.getCostCategoryId());
        cardCreditApplyRpcReqDTO.setCostAttributionName(orderInfo.getCostCategoryName());
        cardCreditApplyRpcReqDTO.setCostAttributionOpt(orderInfo.getCostAttributionOpt());

        String costAttributions = orderInfo.getCostAttributions();
        List<BudgetCostAttributionDTO> budgetCostAttributionDTOList = new ArrayList<>();
        if(StringUtils.isBlank(costAttributions)){
            CostInfo costInfo = JsonUtils.toObj(costAttributions, CostInfo.class);
            costInfo.getCostAttributionGroupList().forEach(costAttribution -> {
                BudgetCostAttributionDTO budgetCostAttributionDTO = new BudgetCostAttributionDTO();
                budgetCostAttributionDTO.setCost_attribution_category(costAttribution.getCategory());
                budgetCostAttributionDTO.setCost_attribution_name(costAttribution.getCostAttributionList().get(0).getName());
                budgetCostAttributionDTO.setCost_attribution_id(costAttribution.getCostAttributionList().get(0).getId());
                budgetCostAttributionDTOList.add(budgetCostAttributionDTO);
            });
        }

        cardCreditApplyRpcReqDTO.setAttributions(budgetCostAttributionDTOList);

        return cardCreditApplyRpcReqDTO;
    }

    @Override
    public CardCreditApplyOrderTrySendResVO trySend(String companyId, CardCreditApplyOrderTrySendReqVO trySendReqDTO) {
        log.info("尝试发放额度发放单, params={}", JSONObject.toJSONString(trySendReqDTO));
        CardCreditApplyOrderTrySendResVO resVO = new CardCreditApplyOrderTrySendResVO();
        String applyOrderId = trySendReqDTO.getApplyOrderId();
        // 1. 查询发放单信息
        CardCreditApplyOrderShowResDTO cardCreditApplyOrderShowResDTO = manager.showByApplyOrderId(companyId, applyOrderId);
        if (cardCreditApplyOrderShowResDTO == null) {
            log.error("额度发放单不存在, applyOrderId={}", applyOrderId);
            throw new FxCardException(APPLY_CREDIT_NOT_EXIST);
        }
        try {
            BeanUtils.copyProperties(cardCreditApplyOrderShowResDTO, resVO);
            // 2. 检查发放单状态
            if (ApplyOrderStateEnum.isSuccess(cardCreditApplyOrderShowResDTO.getApplyState())) {
                log.info("额度发放单已发放成功，无需重复发放, applyOrderId={}", applyOrderId);
                return resVO;
            }
            // 2. 检查发放单状态
            if (!ApplyOrderStateEnum.canTryAgainGrant(cardCreditApplyOrderShowResDTO.getApplyState())) {
                log.info("额度发放单，无需重复发放, applyOrderId={}", applyOrderId);
                resVO.setApplyStateDesc(ApplyOrderStateEnum.getDescByCode(cardCreditApplyOrderShowResDTO.getApplyState())+",当前状态不可再重新发放");
                return resVO;
            }

            // 3. 构建发放请求参数 - 使用转换方法
            CardCreditManagerApplyReqDTO applyReqDTO = convertShowResToCardCreditManagerApplyReqDTO(cardCreditApplyOrderShowResDTO);
            log.info("海外卡额度申请：{}", JsonUtils.toJson(applyReqDTO));

            // 4. 调用发放接口
            Date nowDate = new Date();
            CardCreditManagerApplyRespDTO respDTO = null;

            // 更新发放单对象

            try {
                // 调用发放接口
                respDTO = CardCreditManagerManager.me().apply(applyReqDTO);
                if (respDTO.getApplyStatus()) {
                    // 发放成功
                    cardCreditApplyOrderShowResDTO.setApplyState(ApplyOrderStateEnum.GRANT_SUCCESS.getCode());
                    cardCreditApplyOrderShowResDTO.setApplyResultDesc(respDTO.getFailedReason());
                    cardCreditApplyOrderShowResDTO.setIssuedTime(nowDate);
                    log.info("海外卡额度发放成功：applyOrderId={}", applyOrderId);
                } else {
                    // 发放失败
                    cardCreditApplyOrderShowResDTO.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                    cardCreditApplyOrderShowResDTO.setApplyResultDesc(respDTO.getFailedReason());
                    log.error("海外卡额度发放失败：applyOrderId={}, reason={}", applyOrderId, respDTO.getFailedReason());
                }
            } catch (FxCardException fxCardException) {
                log.error("海外卡额度申请失败：{}", JsonUtils.toJson(applyReqDTO), fxCardException);
                cardCreditApplyOrderShowResDTO.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                cardCreditApplyOrderShowResDTO.setApplyResultDesc(fxCardException.getMessage());
            } catch (Exception e) {
                log.error("海外卡额度申请失败：{}", JsonUtils.toJson(applyReqDTO), e);
                cardCreditApplyOrderShowResDTO.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                cardCreditApplyOrderShowResDTO.setApplyResultDesc(e.getMessage());
            }
            CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO = new CardCreditApplyOrderModifyReqDTO();
            BeanUtils.copyProperties(cardCreditApplyOrderShowResDTO, cardCreditApplyOrderModifyReqDTO);
            cardCreditApplyOrderModifyReqDTO.setUpdateTime(nowDate);
            // 5. 更新发放单状态
            boolean update = this.modify(cardCreditApplyOrderModifyReqDTO);
            if (!update) {
                log.error("额度发放单状态更新失败, applyOrderId={}", applyOrderId);
                throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "额度发放单状态更新失败");
            }
            // 6. 返回结果
            BeanUtils.copyProperties(cardCreditApplyOrderShowResDTO, resVO);
            return resVO;
        } catch (FxCardException e) {
            log.error("尝试发放额度发放单失败, 业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("尝试发放额度发放单失败, 系统异常", e);
            throw new FxCardException(GlobalCoreResponseCode.EXCEPTION.getCode(), "系统异常，请稍后重试");
        }
    }

    /**
     * 校验修改请求参数
     */
    private void validateModifyReqVO(CardCreditApplyOrderModifyReqVO creditApplyOrderModifyReqVO) {
        if (creditApplyOrderModifyReqVO == null) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "请求参数不能为空");
        }

        if (StringUtils.isBlank(creditApplyOrderModifyReqVO.getApplyOrderId())) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "发放单ID不能为空");
        }

        if (ObjUtils.isEmpty(creditApplyOrderModifyReqVO.getUserMap())) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL.getCode(), "申请人列表不能为空");
        }

        // 校验必填字段
        if (creditApplyOrderModifyReqVO.getApplyOrderSubType() == null) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "发放单子类型不能为空");
        }

        if (StringUtils.isBlank(creditApplyOrderModifyReqVO.getTitle())) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "标题不能为空");
        }

        if (BigDecimalUtils.hasNoPrice(creditApplyOrderModifyReqVO.getApplyAmount())) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL.getCode(), "申请金额必须大于0");
        }

        if (StringUtils.isBlank(creditApplyOrderModifyReqVO.getApplyReason())) {
            throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "申请事由不能为空");
        }

        // 校验字段长度
        if (creditApplyOrderModifyReqVO.getTitle().length() > 30) {
            throw new FxCardException(GlobalCoreResponseCode.TITLE_ORDER_LENGTH_MAX);
        }

        if (creditApplyOrderModifyReqVO.getApplyReason().length() > 200) {
            throw new FxCardException(GlobalCoreResponseCode.REASON_ORDER_LENGTH_MAX);
        }

        // 校验活动模式
        if (creditApplyOrderModifyReqVO.getActiveModel() != null &&
            !ActiveModelEnum.isActiveModel(creditApplyOrderModifyReqVO.getActiveModel())) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "生效模式参数错误");
        }
    }

    /**
     * 构建修改请求DTO
     */
    private CardCreditApplyOrderModifyReqDTO buildModifyReqDTO(CardCreditApplyOrderModifyReqVO reqVO,
                                                               CardCreditApplyOrderShowResDTO existingOrder) {
        CardCreditApplyOrderModifyReqDTO modifyReqDTO = new CardCreditApplyOrderModifyReqDTO();

        // 设置ID和基本信息
        modifyReqDTO.setId(existingOrder.getId());
        // 从请求参数设置可修改字段
        modifyReqDTO.setActiveModel(reqVO.getActiveModel());
        modifyReqDTO.setTitle(reqVO.getTitle());
        modifyReqDTO.setApplyAmount(BigDecimalUtils.yuan2fen(reqVO.getApplyAmount()));
        modifyReqDTO.setApplyReason(reqVO.getApplyReason());
        modifyReqDTO.setApplyReasonId(reqVO.getApplyReasonId());

        // 设置费用信息
        modifyReqDTO.setCostCategoryId(reqVO.getCostCategoryId());
        modifyReqDTO.setCostCategoryName(reqVO.getCostCategoryName());
        modifyReqDTO.setCostAttributionOpt(reqVO.getCostAttributionOpt());
        modifyReqDTO.setCostAttributions(JsonUtils.toJson(reqVO.getCostInfo()));

        // 设置更新时间
        modifyReqDTO.setUpdateTime(new Date());
        return modifyReqDTO;
    }

    @Override
    public CardCreditApplyOrderBatchTrySendResVO batchTrySend(String companyId, CardCreditApplyOrderBatchTrySendReqVO batchTrySendReqVO) {
        log.info("批量发放额度开始, companyId={}, applyOrderIds={}", companyId, batchTrySendReqVO.getApplyOrderIds());
        //防止重复下发申请额度
        //0.幂等加锁
        String lockKey = RedisKeyConstant.CREATE_CARD_TRY_BATCH_ORDER_LOCK +companyId;
        try {
            boolean b = redissonService.tryLock(15, 10,TimeUnit.SECONDS, lockKey);
            if (!b) {
                throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST_MORE);
            }
            // 参数校验
            if (StringUtils.isBlank(companyId)) {
                throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "公司ID不能为空");
            }

            if (ObjUtils.isEmpty(batchTrySendReqVO.getApplyOrderIds())) {
                throw new FxCardException(GlobalCoreResponseCode.PARAMS_NULL, "申请单ID列表不能为空");
            }

            CardCreditApplyOrderBatchTrySendResVO resVO = new CardCreditApplyOrderBatchTrySendResVO();
            List<CardCreditApplyOrderBatchTrySendResVO.BatchTrySendResult> results = new ArrayList<>();

            List<String> applyOrderIds = batchTrySendReqVO.getApplyOrderIds();
            int totalCount = applyOrderIds.size();
            int successCount = 0;
            int failCount = 0;

            // 批量处理发放
            for (String applyOrderId : applyOrderIds) {
                CardCreditApplyOrderBatchTrySendResVO.BatchTrySendResult result =
                    new CardCreditApplyOrderBatchTrySendResVO.BatchTrySendResult();
                result.setApplyOrderId(applyOrderId);
                try {
                    // 先查询申请单信息，获取基本信息
                    CardCreditApplyOrderShowResDTO orderInfo = manager.showByApplyOrderId(companyId,applyOrderId);
                    if (orderInfo != null) {
                        result.setApplicantId(orderInfo.getApplicantId());
                        result.setApplicantName(orderInfo.getApplicantName());
                        result.setApplyAmount(orderInfo.getApplyAmount());
                        result.setCurrency(orderInfo.getCurrency());
                    }

                    // 构建单个发放请求
                    CardCreditApplyOrderTrySendReqVO singleTrySendReq = new CardCreditApplyOrderTrySendReqVO();
                    singleTrySendReq.setApplyOrderId(applyOrderId);

                    // 调用单个发放方法
                    CardCreditApplyOrderTrySendResVO singleResult = this.trySend(companyId, singleTrySendReq);

                    // 判断发放结果
                    boolean isSuccess = ApplyOrderStateEnum.isSuccess(singleResult.getApplyState());
                    result.setSuccess(isSuccess);
                    result.setApplyState(singleResult.getApplyState());
                    result.setApplyStateDesc(singleResult.getApplyStateDesc());
                    result.setMessage(isSuccess ? "发放成功" :
                        (!StringUtils.isBlank(singleResult.getApplyResultDesc()) ?
                            singleResult.getApplyResultDesc() : "发放失败"));

                    if (isSuccess) {
                        successCount++;
                    } else {
                        failCount++;
                    }

                } catch (FxCardException fxCardException) {
                    log.error("批量发放单个申请单业务异常, applyOrderId={}, message={}", applyOrderId, fxCardException.getMessage());
                    result.setSuccess(false);
                    result.setMessage(fxCardException.getMessage());
                    result.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                    result.setApplyStateDesc("发放失败");
                    failCount++;
                } catch (Exception e) {
                    log.error("批量发放单个申请单系统异常, applyOrderId={}", applyOrderId, e);
                    result.setSuccess(false);
                    result.setMessage("系统异常: " + e.getMessage());
                    result.setApplyState(ApplyOrderStateEnum.GRANT_FAIL.getCode());
                    result.setApplyStateDesc("发放失败");
                    failCount++;
                }

                results.add(result);
            }

            // 设置批量发放结果
            resVO.setTotalCount(totalCount);
            resVO.setSuccessCount(successCount);
            resVO.setFailCount(failCount);
            resVO.setResults(results);

            log.info("批量发放额度完成, 总数={}, 成功={}, 失败={}", totalCount, successCount, failCount);
            return resVO;
        } catch (InterruptedException e) {
            log.error("批量发放额度系统异常加锁失败,  companyId={}, batchTrySendReqVO={}", companyId, batchTrySendReqVO,e);
            throw new FxCardException(GlobalCoreResponseCode.SERVER_REQUEST_FAST);
        } finally {
                try {
                    redissonService.unLock(lockKey);
                } catch (Exception e) {
                    log.error("批量发放额度完成 unlock|err=", e);
                }
            }
        }


}
