package com.fenbei.fx.card.common.enums;

import com.finhub.framework.core.locale.LocaleUtils;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * state简称映射
 * airwallex 需要US美国和AU澳洲的state简写信息
 */
@AllArgsConstructor
public enum StateEnum {
    //------------美洲-------------------
    Alabama("US", "亚拉巴马州", "Alabama", "AL"),
    Alaska("US", "阿拉斯加州", "Alaska", "AK"),
    Arizona("US", "亚利桑那州", "Arizona", "AZ"),
    Arkansas("US", "阿肯色州", "Arkansas", "AR"),
    California("US", "加利福尼亚州", "California", "CA"),
    Colorado("US", "科罗拉多州", "Colorado", "CO"),
    Connecticut("US", "康涅狄格州", "Connecticut", "CT"),
    Delaware("US", "特拉华州", "Delaware", "DE"),
    District_of_Columbia("US", "哥伦比亚特区", "District of Columbia", "DC"),
    Florida("US", "佛罗里达州", "Florida", "FL"),
    Georgia("US", "佐治亚州", "Georgia", "GA"),
    Guam("US", "关岛", "Guam", "GU"),
    Hawaii("US", "夏威夷", "Hawaii", "HI"),
    Idaho("US", "爱达荷州", "Idaho", "ID"),
    Illinois("US", "伊利诺伊州", "Illinois", "IL"),
    Indiana("US", "印第安纳州", "Indiana", "IN"),
    Iowa("US", "爱荷华州", "Iowa", "IA"),
    Kansas("US", "堪萨斯州", "Kansas", "KS"),
    Kentucky("US","肯塔基州","Kentucky","KY"),
    Louisiana("US","路易斯安那州","Louisiana","LA"),
    Maine("US","缅因州","Maine","ME"),
    Maryland("US","马里兰州","Maryland","MD"),
    Massachusetts("US","马萨诸塞州","Massachusetts","MA"),
    Michigan("US","密歇根州","Michigan","MI"),
    Minnesota("US","明尼苏达州","Minnesota","MN"),
    Mississippi("US","密西西比州","Mississippi","MS"),
    Missouri("US","密苏里州","Missouri","MO"),
    Montana("US","蒙大拿州","Montana","MT"),
    Nebraska("US","内布拉斯加州","Nebraska","NE"),
    Nevada("US","内华达州","Nevada","NV"),
    New_Hampshire("US","新罕布什尔州","New Hampshire","NH"),
    New_Jersey("US","新泽西州","New Jersey","NJ"),
    New_Mexico("US","新墨西哥州","New Mexico","NM"),
    New_York("US","纽约州","New York State","NY"),
    North_Carolina("US","北卡罗莱纳州","North Carolina","NC"),
    North_Dakota("US","北达科他州","North Dakota","ND"),
    Northern_Mariana_Islands("US","北马里亚纳群岛","Northern Mariana Islands","MP"),
    Ohio("US","俄亥俄州","Ohio","OH"),
    Oklahoma("US","俄克拉何马州","Oklahoma","OK"),
    Oregon("US","俄勒冈州","Oregon","OR"),
    Pennsylvania("US","宾夕法尼亚州","Pennsylvania","PA"),
    Puerto_Rico("US","","Puerto Rico","PR"),
    Rhode_Island("US","罗得岛州","Rhode Island","RI"),
    South_Carolina("US","南卡罗来纳州","South Carolina","SC"),
    South_Dakota("US","南达科他州","South Dakota","SD"),
    Tennessee("US","田纳西州","Tennessee","TN"),
    Texas("US","德克萨斯州","Texas","TX"),
    Utah("US","犹他州","Utah","UT"),
    Vermont("US","佛蒙特州","Vermont","VT"),
    Virginia ("US","弗吉尼亚州","Virginia","VA"),
    Washington ("US","华盛顿州","Washington","WA"),
    West_Virginia ("US","西弗吉尼亚州","West Virginia","WV"),
    Wisconsin("US","威斯康星州","Wisconsin","WI"),
    Wyoming  ("US","怀俄明州","Wyoming","WY"),
    //分贝通测暂无以下州
    Trust_Territories("US","","Trust Territories","TT"),
    Virgin_Islands("US","维尔京群岛","Virgin Islands","VI"),
    American_Samoa("US", "美属萨摩亚", "American Samoa", "AS"),


    //------------澳洲-------------------
    Australian_Capital_Territory("AU","澳大利亚首都领地","Australian Capital Territory","ACT"),
    New_South_Wales ("AU","新南威尔士州","New South Wales","NSW"),
    Northern_Territory ("AU","北领地","Northern Territory","NT"),
    Queensland ("AU","昆士兰州","Queensland","QLD"),
    South_Australia("AU","南澳大利亚州","South Australia","SA"),
    Tasmania  ("AU","塔斯马尼亚州","Tasmania","TAS"),
    Victoria("AU","维多利亚州","Victoria","VIC"),
    Western_Australia  ("AU","西澳大利亚州","Western Australia","WA"),

    ;

    public static StateEnum getStateEnum(String enName) {
        for (StateEnum item : values()) {
            if (Objects.equals(item.getEnName(), enName)) {
                return item;
            }
        }
        return null;
    }


    private String country;

    private String name;

    private String enName;

    private String shortEnName;

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getName() {
        if (LocaleUtils.getLang().equalsIgnoreCase("zh_CN")) {
            return name;
        }
        return enName;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getShortEnName() {
        return shortEnName;
    }

    public void setShortEnName(String shortEnName) {
        this.shortEnName = shortEnName;
    }

}
