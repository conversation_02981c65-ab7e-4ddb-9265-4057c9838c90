package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 国际卡额度发放单 修改 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderModifyResVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 单据主键ID
     */
    private String applyOrderId;

    /**
     * 单据编号
     */
    private String meaningNo;

    /**
     * 审批单类型  40虚拟卡发放单(大类别)
     */
    private Integer applyOrderType;

    /**
     * 40:虚拟卡发放单(小类别)
     */
    private Integer applyOrderSubType;

    /**
     * 1: 普通模式申请单
     * 3: 循环备用金申请单;
     * 4: 普通备用金申请单;
     * 5: 一次性备用金申请单;
     */
    private Integer applyModelType;

    /**
     * 发放状态 1.待发放 2.已发放 3.发放失败 4.发放中
     */
    private Integer applyState;

    /**
     * 申请人id
     */
    private String applicantId;

    /**
     * 申请人姓名
     */
    private String applicantName;

    private String fxCardId;

    private String bankName;

    /**
     * 制单人ID
     */
    private String createrId;

    /**
     * 制单人名称
     */
    private String createrName;


    /**
     * 发放人ID
     */
    private String issuedId;

    /**
     * 发放人名称
     */
    private String issuedName;





}
