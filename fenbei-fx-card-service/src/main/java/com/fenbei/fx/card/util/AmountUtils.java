package com.fenbei.fx.card.util;

import com.luastar.swift.base.utils.ObjUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-23 上午11:16
 */

public class AmountUtils {

    private static String dollar = "$";

    public static String formatDollar(BigDecimal amount){
        if(ObjUtils.isNull(amount)){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(dollar);
        stringBuilder.append(amount.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
        return stringBuilder.toString();
    }
}
