package com.fenbei.fx.card.service.cardorder.converter;

import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.util.MoneyNumberFormatUtil;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.converter.MapstructContextHolder;
import com.finhub.framework.core.page.Page;

import com.fenbeitong.finhub.common.constant.CheckStatusEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.common.enums.CardFormFactorEnum;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.dao.cardorder.po.CardOrderPO;
import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import com.fenbei.fx.card.util.I18nUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 国际卡订单 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardOrderConverter extends BaseConverter<CardOrderDTO, CardOrderPO> {

    static CardOrderConverter me() {
        return SpringUtil.getBean(CardOrderConverter.class);
    }

    CardOrderDTO convertToCardOrderDTO(CardOrderAddReqDTO cardOrderAddReqDTO);

    CardOrderDTO convertToCardOrderDTO(CardOrderModifyReqDTO cardOrderModifyReqDTO);

    CardOrderDTO convertToCardOrderDTO(CardOrderRemoveReqDTO cardOrderRemoveReqDTO);

    CardOrderDTO convertToCardOrderDTO(CardOrderListReqDTO cardOrderListReqDTO);

    CardOrderDTO convertToCardOrderDTO(CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO);

    CardOrderShowResDTO convertToCardOrderShowResDTO(CardOrderDTO cardOrderDTO);

    List<CardOrderShowResDTO> convertToCardOrderShowResDTOList(List<CardOrderDTO> cardOrderDTOList);

    CardOrderListResDTO convertToCardOrderListResDTO(CardOrderDTO cardOrderDTO);

    List<CardOrderListResDTO> convertToCardOrderListResDTOList(List<CardOrderDTO> cardOrderDTOList);

    List<CardOrderDTO> convertToCardOrderDTOList(List<CardOrderAddReqDTO> cardOrderAddReqDTOList);

    @Mappings({
        @Mapping(source = "cardOrderDTO.bizNo", target = "transactionId"),
        @Mapping(expression = "java(convertToCardHolderName(cardOrderDTO.getFxCardId()))", target = "cardHolderName"),
        @Mapping(expression = "java(convertToBankCardType(cardOrderDTO.getFxCardId()))", target = "bankCardType"),
        @Mapping(source = "cardOrderDTO.tradeName", target = "merchantName"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionType", qualifiedByName = "convertToTransactionType"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionTypeCode"),
        @Mapping(source = "cardOrderDTO.tradeTime", target = "transactionDate"),
        @Mapping(source = "cardOrderDTO.tradeCurrency", target = "transactionCurrency"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getTradeAmount()))", target = "transactionAmount"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "transactionAmountDesc"),
        @Mapping(source = "cardOrderDTO.tradeAddress", target = "transactionAddress"),
        @Mapping(source = "cardOrderDTO.billTradeCurrency", target = "obversionCurrType"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getBillTradeAmount()))", target = "obversionTotalPrice"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getBillTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getBillTradeCurrency()))", target = "obversionTotalPriceDesc"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatus", qualifiedByName = "convertToCheckStatus"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatusCode"),
        @Mapping(source = "cardOrderDTO.tradeRemark", target = "remark"),
        @Mapping(source = "cardOrderDTO.checkedAmount", target = "checkedConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getCheckedAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "checkedConsumeDesc"),
        @Mapping(source = "cardOrderDTO.refundAmount", target = "refundConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getRefundAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "refundConsumeDesc"),
        @Mapping(source = "cardOrderDTO.needNotCheckAmount", target = "wrongPaidConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "wrongPaidConsumeDesc"),
        @Mapping(source = "cardOrderDTO.checkingAmount", target = "checkingConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "checkingConsumeDesc"),
        @Mapping(source = "cardOrderDTO.uncheckedAmount", target = "uncheckConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getUncheckedAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "uncheckConsumeDesc"),
        @Mapping(source = "cardOrderDTO.needNotCheckAmount", target = "unNeedCheckConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "unNeedCheckConsumeDesc"),
    })
    CardTradeInfoWebPageResDTO convertToCardTradeInfoWebPageResDTO(CardOrderDTO cardOrderDTO);

    List<CardTradeInfoWebPageResDTO> convertToCardTradeInfoWebPageResDTOList(List<CardOrderDTO> cardOrderDTOList);

    Page<CardTradeInfoWebPageResDTO> convertToCardTradeInfoWebPageResDTOPage(Page<CardOrderDTO> cardOrderDTOPage);

    @Mappings({
        @Mapping(source = "cardOrderDTO.bizNo", target = "transactionId"),
        @Mapping(expression = "java(convertToCardHolderName(cardOrderDTO.getFxCardId()))", target = "cardHolderName"),
        @Mapping(expression = "java(convertToBankCardType(cardOrderDTO.getFxCardId()))", target = "bankCardType"),
        @Mapping(expression = "java(convertToMaskedCardNumber(cardOrderDTO.getFxCardId()))", target = "maskedCardNumber"),
        @Mapping(source = "cardOrderDTO.tradeName", target = "merchantName"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionType", qualifiedByName = "convertToTransactionType"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionTypeCode"),
        @Mapping(source = "cardOrderDTO.tradeTime", target = "transactionDate"),
        @Mapping(source = "cardOrderDTO.createTime", target = "createTime"),
        @Mapping(source = "cardOrderDTO.tradeCurrency", target = "transactionCurrency"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getTradeAmount()))", target = "transactionAmount"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "transactionAmountDesc"),
        @Mapping(source = "cardOrderDTO.tradeAddress", target = "transactionAddress"),
        @Mapping(source = "cardOrderDTO.billTradeCurrency", target = "obversionCurrType"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getBillTradeAmount()))", target = "obversionTotalPrice"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getBillTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getBillTradeCurrency()))", target = "obversionTotalPriceDesc"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatus", qualifiedByName = "convertToCheckStatus"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatusCode"),
        @Mapping(source = "cardOrderDTO.tradeRemark", target = "remark"),
        @Mapping(source = "cardOrderDTO.checkedAmount", target = "checkedConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getCheckedAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "checkedConsumeDesc"),
        @Mapping(source = "cardOrderDTO.refundAmount", target = "refundConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getRefundAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "refundConsumeDesc"),
        @Mapping(source = "cardOrderDTO.needNotCheckAmount", target = "wrongPaidConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "wrongPaidConsumeDesc"),
        @Mapping(source = "cardOrderDTO.checkingAmount", target = "checkingConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "checkingConsumeDesc"),
        @Mapping(source = "cardOrderDTO.uncheckedAmount", target = "uncheckConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getUncheckedAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "uncheckConsumeDesc"),
        @Mapping(source = "cardOrderDTO.needNotCheckAmount", target = "unNeedCheckConsume"),
        @Mapping(expression = "java(convertToTotalPrice4WrongPaid(cardOrderDTO.getNeedNotCheckAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "unNeedCheckConsumeDesc"),
    })
    CardTradeInfoStereoPageResDTO convertToCardTradeInfoStereoPageResDTO(CardOrderDTO cardOrderDTO);

    Page<CardTradeInfoStereoPageResDTO> convertToCardTradeInfoStereoPageResDTOPage(Page<CardOrderDTO> cardOrderDTOPage);

    @Mappings({
        @Mapping(source = "cardOrderDTO.bizNo", target = "transactionId"),
        @Mapping(source = "cardOrderDTO.id", target = "virtualId"),
        @Mapping(source = "cardOrderDTO.oriBizNo", target = "oriBizNo"),
        @Mapping(expression = "java(convertToCardHolderName(cardOrderDTO.getFxCardId()))", target = "cardHolderName"),
        @Mapping(expression = "java(convertToBankCardType(cardOrderDTO.getFxCardId()))", target = "bankCardType"),
        @Mapping(source = "cardOrderDTO.tradeName", target = "merchantName"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionType", qualifiedByName = "convertToTransactionType"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionTypeCode"),
        @Mapping(source = "cardOrderDTO.tradeTime", target = "transactionDate"),
        @Mapping(source = "cardOrderDTO.tradeCurrency", target = "transactionCurrency"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getTradeAmount()))", target = "transactionAmount"),
        @Mapping(expression = "java(convertToTotalPrice(cardOrderDTO.getTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "transactionAmountDesc"),
        @Mapping(source = "cardOrderDTO.tradeAddress", target = "transactionAddress"),
        @Mapping(source = "cardOrderDTO.billTradeCurrency", target = "obversionCurrType"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getBillTradeAmount()))", target = "obversionTotalPrice"),
        @Mapping(expression = "java(convertToTotalPrice(cardOrderDTO.getBillTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getBillTradeCurrency()))", target = "obversionTotalPriceDesc"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatus", qualifiedByName = "convertToCheckStatus"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatusCode"),
        @Mapping(source = "cardOrderDTO.tradeRemark", target = "remark"),
        @Mapping(expression = "java(convertToCnyTotalPrice(cardOrderDTO.getCnyTradeAmount(), cardOrderDTO.getType()))", target = "cnyTradePrice")
    })
    CardTradeInfoAppPageResDTO convertToCardTradeInfoAppPageResDTO(CardOrderDTO cardOrderDTO);

    List<CardTradeInfoAppPageResDTO> convertToCardTradeInfoAppPageResDTOList(List<CardOrderDTO> cardOrderDTOList);

    Page<CardTradeInfoAppPageResDTO> convertToCardTradeInfoAppPageResDTOPage(Page<CardOrderDTO> cardOrderDTOPage);

    @Mappings({
        @Mapping(source = "cardOrderDTO.bizNo", target = "transactionId"),
        @Mapping(expression = "java(convertToCardHolderName(cardOrderDTO.getFxCardId()))", target = "cardHolderName"),
        @Mapping(expression = "java(convertToBankCardType(cardOrderDTO.getFxCardId()))", target = "bankCardType"),
        @Mapping(source = "cardOrderDTO.tradeName", target = "merchantName"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionType", qualifiedByName = "convertToTransactionType"),
        @Mapping(source = "cardOrderDTO.type", target = "transactionTypeCode"),
        @Mapping(source = "cardOrderDTO.tradeTime", target = "transactionDate"),
        @Mapping(source = "cardOrderDTO.tradeCurrency", target = "transactionCurrency"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getTradeAmount()))", target = "transactionAmount"),
        @Mapping(expression = "java(convertToTotalPrice(cardOrderDTO.getTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))", target = "transactionAmountDesc"),
        @Mapping(source = "cardOrderDTO.tradeAddress", target = "transactionAddress"),
        @Mapping(source = "cardOrderDTO.billTradeCurrency", target = "obversionCurrType"),
        @Mapping(expression = "java(convertToAmount(cardOrderDTO.getBillTradeAmount()))", target = "obversionTotalPrice"),
        @Mapping(expression = "java(convertToTotalPrice(cardOrderDTO.getBillTradeAmount(), cardOrderDTO.getType(), cardOrderDTO.getBillTradeCurrency()))", target = "obversionTotalPriceDesc"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatus", qualifiedByName = "convertToCheckStatus"),
        @Mapping(source = "cardOrderDTO.checkStatus", target = "checkStatusCode"),
        @Mapping(source = "cardOrderDTO.tradeRemark", target = "remark"),
        @Mapping(source = "cardOrderDTO.maskedCardNumber",target = "payMethod"),
        @Mapping(source = "cardOrderDTO.uncheckedAmount",target = "uncheckConsume"),
        @Mapping(expression = "java(convertToTotalPrice(cardOrderDTO.getUncheckedAmount(), cardOrderDTO.getType(), cardOrderDTO.getTradeCurrency()))",target = "uncheckConsumeDesc"),
        @Mapping(source = "cardOrderDTO.cardPlatform",target = "cardPlatform"),
    })
    CardTradeInfoAppShowResDTO convertToCardTradeInfoAppShowResDTO(CardOrderDTO cardOrderDTO);

    @Named("convertToCardHolderName")
    default String convertToCardHolderName(String fxCardId) {
        Map<String, CardPO> fxCardIdAndCardPOMap = (Map<String, CardPO>) MapstructContextHolder.getContext();
        CardPO cardPO = fxCardIdAndCardPOMap.get(fxCardId);
        return Func.isNull(cardPO) ? "未知" : cardPO.getNameOnCard();
    }

    @Named("convertToMaskedCardNumber")
    default String convertToMaskedCardNumber(String fxCardId) {
        Map<String, CardPO> fxCardIdAndCardPOMap = (Map<String, CardPO>) MapstructContextHolder.getContext();
        CardPO cardPO = fxCardIdAndCardPOMap.get(fxCardId);
        return Func.isNull(cardPO) ? "未知" : cardPO.getBankCardNo();
    }

    @Named("convertToBankCardType")
    default String convertToBankCardType(String fxCardId) {
        Map<String, CardPO> fxCardIdAndCardPOMap = (Map<String, CardPO>) MapstructContextHolder.getContext();
        CardPO cardPO = fxCardIdAndCardPOMap.get(fxCardId);
        if (Func.isNull(cardPO) || Func.isNull(cardPO.getCardFormFactor())) {
            return "未知";
        }
        CardFormFactorEnum cardFormFactorEnum = CardFormFactorEnum.getEnum(cardPO.getCardFormFactor());
        return Func.isNull(cardFormFactorEnum) ? "未知" : cardFormFactorEnum.getName();
    }

    @Named("convertToTransactionType")
    default String convertToTransactionType(Integer type) {
        if (Func.isNull(type)) {
            return "未知";
        }
        TransactionTypeEnum transactionTypeEnum = TransactionTypeEnum.getEnum(type);
        return I18nUtils.transferI18nMessage(Func.isNull(transactionTypeEnum) ? "未知" : transactionTypeEnum.getValue());
    }

    @Named("convertToCheckStatus")
    default String convertToCheckStatus(Integer tradeWriteOffStatus) {
        if (Func.isNull(tradeWriteOffStatus)) {
            return "未知";
        }
        CheckStatusEnum checkStatusEnum = CheckStatusEnum.getEnum(tradeWriteOffStatus);
        if (checkStatusEnum.getKey() == 0){
            return I18nUtils.transferI18nMessage("无需核销");
        }
        return I18nUtils.transferI18nMessage(Func.isNull(checkStatusEnum) ? "未知" : checkStatusEnum.getMsg());
    }

    @Named("convertToTotalPrice")
    default TotalPrice convertToTotalPrice(BigDecimal amount, Integer type, String currency) {
        if (Func.isBlank(currency)) {
            currency = "USD";
        }
        amount = convertToAmount(amount);
        TotalPrice totalPrice = new TotalPrice();
        totalPrice.setPrice(amount);
        totalPrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(type) + CurrencyEnum.getCurrencyByCode(currency).getSymbol() + NumberUtil.decimalFormat(",##0.00", amount));
        totalPrice.setColor("#333333");
        return totalPrice;
    }

    @Named("convertToTotalPrice4WrongPaid")
    default TotalPrice convertToTotalPrice4WrongPaid(BigDecimal amount, Integer type, String currency) {
        if (Func.isBlank(currency)) {
            currency = "USD";
        }
        amount = convertToAmount(amount);
        TotalPrice totalPrice = new TotalPrice();
        totalPrice.setPrice(amount);
        totalPrice.setShowPrice(CurrencyEnum.getCurrencyByCode(currency).getSymbol() + NumberUtil.decimalFormat(",##0.00", amount));
        totalPrice.setColor("#333333");
        return totalPrice;
    }

    @Named("convertToCnyTotalPrice")
    default TotalPrice convertToCnyTotalPrice(BigDecimal amount, Integer type) {
        TotalPrice cnyTradePrice = new TotalPrice();
        cnyTradePrice.setPrice(TransactionTypeEnum.getPlusOrMinusValue4Saas(type,BigDecimalUtils.fen2yuan(amount)));
        cnyTradePrice.setShowPrice(TransactionTypeEnum.getPlusOrMinusValue(type) + CurrencyEnum.CNY.getSymbol() + MoneyNumberFormatUtil.formart(BigDecimalUtils.fen2yuan(amount)));
        return cnyTradePrice;
    }

    @Named("convertToAmount")
    default BigDecimal convertToAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return NumberUtil.div(amount, BigDecimal.valueOf(100), 2);
    }
}
