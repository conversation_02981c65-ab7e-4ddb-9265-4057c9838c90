package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import lombok.Data;

@Data
public class CostInfoAttrDto {
    //费用归属ID
    private String costAttributionId;
    //费用归名称
    private String costAttributionName;
    //费用归属扩展属性
    private String costAttributionExt;
    //费用归属类型 1：部门 2：项目 3：自定义档案
    private Integer category;
    /**
     * 自定义档案类型ID( cost_attribution_category 是 3 时候使用，生成财务凭证要用)
     */
    private String recordId;

    //费用类别ID
    private String costCategoryId;
    //费用类别名称
    private String costCategoryName;
    /**
     * 项目编码
     */
    private String code;


}
