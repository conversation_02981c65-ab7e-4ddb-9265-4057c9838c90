package com.fenbei.fx.card.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class USDNumberFormatUtil {
    private static String USD = "$";
    private static String USD_SUB = "-$";

    public USDNumberFormatUtil() {
    }

    public static String moneyFormart(BigDecimal price, Boolean type) {
        DecimalFormat format = new DecimalFormat("##,##0.00");
        if (null == price) {
            return USD + format.format(new BigDecimal(0));
        } else {
            return type ? USD + format.format(price) : USD_SUB + format.format(price);
        }
    }

    public static String moneyFormart(BigDecimal price) {
        DecimalFormat format = new DecimalFormat("##,##0.00");
        if (null == price) {
            return USD + format.format(new BigDecimal(0));
        } else if (price.compareTo(BigDecimal.ZERO) >= 0) {
            return USD + format.format(price);
        } else {
            BigDecimal priceABS = price.abs();
            return USD_SUB + format.format(priceABS);
        }
    }
}
