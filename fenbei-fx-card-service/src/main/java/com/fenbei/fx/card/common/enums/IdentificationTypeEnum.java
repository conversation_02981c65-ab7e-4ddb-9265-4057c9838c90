package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 证件类型
 */
@AllArgsConstructor
public enum IdentificationTypeEnum {

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     * PASSPORT, DRIVERS_LICENSE, or ID_CARD
     */
    ID_CARD(1, "身份证", "ID_CARD"),
    PASSPORT(2, "护照", "PASSPORT"),
    DRIVERS_LICENSE(3, "驾照", "DRIVERS_LICENSE"),
    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("身份证", "identification.type.id.card");
        I18N_KEY_MAP.put("护照", "identification.type.passport");
        I18N_KEY_MAP.put("驾照", "identification.type.drivers.license");
    }

    public static IdentificationTypeEnum getEnum(Integer code) {
        for (IdentificationTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    private String airCode;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAirCode() {
        return airCode;
    }

    public void setAirCode(String airCode) {
        this.airCode = airCode;
    }
}
