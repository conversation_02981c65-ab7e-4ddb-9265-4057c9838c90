package com.fenbei.fx.card.service.cardcreditmanagerrelation.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditmanagerrelation.po.CardCreditManagerRelationPO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationShowResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.converter.CardCreditManagerRelationConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 关联申请单记录 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Slf4j
@Component
public class CardCreditManagerRelationDO extends BaseDO<CardCreditManagerRelationDTO, CardCreditManagerRelationPO, CardCreditManagerRelationConverter> {

    public static CardCreditManagerRelationDO me() {
        return SpringUtil.getBean(CardCreditManagerRelationDO.class);
    }

    public void checkCardCreditManagerRelationAddReqDTO(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        if (Func.isEmpty(cardCreditManagerRelationAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditManagerRelationAddReqDTOList(final List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList) {
        if (Func.isEmpty(cardCreditManagerRelationAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardCreditManagerRelationModifyReqDTO(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        if (Func.isEmpty(cardCreditManagerRelationModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditManagerRelationRemoveReqDTO(final CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO) {
        if (Func.isEmpty(cardCreditManagerRelationRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardCreditManagerRelationDTO buildListParamsDTO(final CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO) {
        return converter.convertToCardCreditManagerRelationDTO(cardCreditManagerRelationListReqDTO);
    }

    public CardCreditManagerRelationDTO buildPageParamsDTO(final CardCreditManagerRelationPageReqDTO cardCreditManagerRelationPageReqDTO) {
        return converter.convertToCardCreditManagerRelationDTO(cardCreditManagerRelationPageReqDTO);
    }

    public CardCreditManagerRelationDTO buildAddCardCreditManagerRelationDTO(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        return converter.convertToCardCreditManagerRelationDTO(cardCreditManagerRelationAddReqDTO);
    }

    public List<CardCreditManagerRelationDTO> buildAddBatchCardCreditManagerRelationDTOList(final List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList) {
        return converter.convertToCardCreditManagerRelationDTOList(cardCreditManagerRelationAddReqDTOList);
    }

    public CardCreditManagerRelationDTO buildModifyCardCreditManagerRelationDTO(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        return converter.convertToCardCreditManagerRelationDTO(cardCreditManagerRelationModifyReqDTO);
    }

    public CardCreditManagerRelationDTO buildRemoveCardCreditManagerRelationDTO(final CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO) {
        return converter.convertToCardCreditManagerRelationDTO(cardCreditManagerRelationRemoveReqDTO);
    }

    public List<CardCreditManagerRelationListResDTO> transferCardCreditManagerRelationListResDTOList(final List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList) {
        if (Func.isEmpty(cardCreditManagerRelationDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardCreditManagerRelationListResDTOList(cardCreditManagerRelationDTOList);
    }

    public CardCreditManagerRelationListResDTO transferCardCreditManagerRelationListResDTO(final CardCreditManagerRelationDTO cardCreditManagerRelationDTO) {
        if (Func.isEmpty(cardCreditManagerRelationDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardCreditManagerRelationListResDTO(cardCreditManagerRelationDTO);
    }

    public Page<CardCreditManagerRelationPageResDTO> transferCardCreditManagerRelationPageResDTOPage(final Page<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOPage) {
        if (Func.isEmpty(cardCreditManagerRelationDTOPage) || Func.isEmpty(cardCreditManagerRelationDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardCreditManagerRelationPageResDTOPage(cardCreditManagerRelationDTOPage);
    }

    public CardCreditManagerRelationShowResDTO transferCardCreditManagerRelationShowResDTO(final CardCreditManagerRelationDTO cardCreditManagerRelationDTO) {
        if (Func.isEmpty(cardCreditManagerRelationDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardCreditManagerRelationShowResDTO(cardCreditManagerRelationDTO);
    }

    public List<CardCreditManagerRelationShowResDTO> transferCardCreditManagerRelationShowResDTOList(final List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList) {
        if (Func.isEmpty(cardCreditManagerRelationDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardCreditManagerRelationShowResDTOList(cardCreditManagerRelationDTOList);
    }
}
