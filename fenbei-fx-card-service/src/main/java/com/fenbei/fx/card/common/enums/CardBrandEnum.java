package com.fenbei.fx.card.common.enums;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 发卡的品牌
 */
@AllArgsConstructor
public enum CardBrandEnum {

    /**
     * 发卡的品牌 VISA,MASTER
     */
    NULL_BRAND("", "", ""),
    VISA("VISA", "VISA", "https://stc.fenbeitong.com/icon/bank/visa.png"),
    MASTER("MASTER", "MASTER", "https://stc.fenbeitong.com/icon/bank/<EMAIL>"),
    ;

    public static CardBrandEnum getBrand(String code) {
        for (CardBrandEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    public static CardBrandEnum getBrandAndNoBrand(String code) {
        if(StringUtils.isBlank(code)){
            return NULL_BRAND;
        }
        for (CardBrandEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return NULL_BRAND;
    }

    private String code;

    private String name;

    private String brandIcon;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBrandIcon() {
        return brandIcon;
    }

    public void setBrandIcon(String brandIcon) {
        this.brandIcon = brandIcon;
    }

}
