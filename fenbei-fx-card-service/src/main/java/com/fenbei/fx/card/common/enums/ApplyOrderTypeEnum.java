package com.fenbei.fx.card.common.enums;

import java.util.ArrayList;
import java.util.List;


/**
 * 申请单类型
 */
public enum ApplyOrderTypeEnum {
    ALL(0, 0, "全部"),
    VIRTUAL_CARD(1, 15,"海外卡额度申请单"),
    PETTY(2, 19,"备用金申请单"),
    QUOTA_GRANT(3, 50,"额度发放单");

    private int type;
    private int typeReal;
    private String desc;

    ApplyOrderTypeEnum(int type, int typeReal, String desc) {
        this.type = type;
        this.typeReal = typeReal;
        this.desc = desc;
    }

    public static List<Integer> getAllTypes() {
        List<Integer> allTypes = new ArrayList<>();
        for (ApplyOrderTypeEnum item : values()) {
            if (item == ALL) {
                continue;
            }
            allTypes.add(item.type);
        }
        return allTypes;
    }

    public static String getDescByType(Integer type){
        if (type == null) {
            return "-";
        }

        for (ApplyOrderTypeEnum typeEnum : ApplyOrderTypeEnum.values()) {
            if (typeEnum.typeReal == type) {
                return typeEnum.desc;
            }
        }
        return "-";
    }

    public int getType() {
        return type;
    }

    public int getTypeReal() {
        return typeReal;
    }

    public String getDesc() {
        return desc;
    }
}
