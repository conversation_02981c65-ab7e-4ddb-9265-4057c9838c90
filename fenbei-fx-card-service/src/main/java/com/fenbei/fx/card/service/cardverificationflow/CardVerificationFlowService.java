package com.fenbei.fx.card.service.cardverificationflow;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import java.util.List;

/**
 * 核销记录表 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
public interface CardVerificationFlowService extends BaseService<CardVerificationFlowDTO> {

    static CardVerificationFlowService me() {
        return SpringUtil.getBean(CardVerificationFlowService.class);
    }

    /**
     * 列表
     *
     * @param cardVerificationFlowListReqDTO 入参DTO
     * @return
     */
    List<CardVerificationFlowListResDTO> list(CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO);

    /**
     * First查询
     *
     * @param cardVerificationFlowListReqDTO 入参DTO
     * @return
     */
    CardVerificationFlowListResDTO listOne(CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO);

    /**
     * 分页
     *
     * @param cardVerificationFlowPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardVerificationFlowPageResDTO> pagination(CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardVerificationFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardVerificationFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardVerificationFlowAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardVerificationFlowShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardVerificationFlowShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardVerificationFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardVerificationFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardVerificationFlowRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO);
}
