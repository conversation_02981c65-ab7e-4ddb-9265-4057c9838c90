package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 申请类型
 */
@AllArgsConstructor
public enum ApplyTypeEnum {

    /**
     * 申请类型 1.创建 2.更新
     */
    ADD(1, "创建"),
    UPDATE(2, "更新"),

    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("创建", "apply.type.add");
        I18N_KEY_MAP.put("更新", "apply.type.update");
    }

    public static ApplyTypeEnum getApplyType(Integer code) {
        for (ApplyTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    public static boolean isAdd(Integer code) {
        return Objects.equals(code, ADD.code);
    }

    public static boolean isUpdate(Integer code) {
        return Objects.equals(code, UPDATE.code);
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }

}
