package com.fenbei.fx.card.service.cardauthorize.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardauthorize.CardAuthorizeDAO;
import com.fenbei.fx.card.dao.cardauthorize.po.CardAuthorizePO;
import com.fenbei.fx.card.service.cardauthorize.converter.CardAuthorizeConverter;
import com.fenbei.fx.card.service.cardauthorize.domain.CardAuthorizeDO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 国际卡授权表 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardAuthorizeManager extends BaseManagerImpl<CardAuthorizeDAO, CardAuthorizePO, CardAuthorizeDTO, CardAuthorizeConverter> {

    public static CardAuthorizeManager me() {
        return SpringUtil.getBean(CardAuthorizeManager.class);
    }

    public List<CardAuthorizeListResDTO> list(final CardAuthorizeListReqDTO cardAuthorizeListReqDTO) {
        CardAuthorizeDTO paramsDTO = CardAuthorizeDO.me().buildListParamsDTO(cardAuthorizeListReqDTO);

        List<CardAuthorizeDTO> cardAuthorizeDTOList = super.findList(paramsDTO);

        return CardAuthorizeDO.me().transferCardAuthorizeListResDTOList(cardAuthorizeDTOList);
    }

    public CardAuthorizeListResDTO listOne(final CardAuthorizeListReqDTO cardAuthorizeListReqDTO) {
        CardAuthorizeDTO paramsDTO = CardAuthorizeDO.me().buildListParamsDTO(cardAuthorizeListReqDTO);

        CardAuthorizeDTO cardAuthorizeDTO = super.findOne(paramsDTO);

        return CardAuthorizeDO.me().transferCardAuthorizeListResDTO(cardAuthorizeDTO);
    }

    public Page<CardAuthorizePageResDTO> pagination(final CardAuthorizePageReqDTO cardAuthorizePageReqDTO, final Integer current, final Integer size) {
        CardAuthorizeDTO paramsDTO = CardAuthorizeDO.me().buildPageParamsDTO(cardAuthorizePageReqDTO);

        Page<CardAuthorizeDTO> cardAuthorizeDTOPage = super.findPage(paramsDTO, current, size);

        return CardAuthorizeDO.me().transferCardAuthorizePageResDTOPage(cardAuthorizeDTOPage);
    }

    public Boolean add(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        CardAuthorizeDO.me().checkCardAuthorizeAddReqDTO(cardAuthorizeAddReqDTO);

        CardAuthorizeDTO addCardAuthorizeDTO = CardAuthorizeDO.me().buildAddCardAuthorizeDTO(cardAuthorizeAddReqDTO);

        return super.saveDTO(addCardAuthorizeDTO);
    }

    public Boolean addAllColumn(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        CardAuthorizeDO.me().checkCardAuthorizeAddReqDTO(cardAuthorizeAddReqDTO);

        CardAuthorizeDTO addCardAuthorizeDTO = CardAuthorizeDO.me().buildAddCardAuthorizeDTO(cardAuthorizeAddReqDTO);

        return super.saveAllColumn(addCardAuthorizeDTO);
    }

    public Boolean addBatchAllColumn(final List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList) {
        CardAuthorizeDO.me().checkCardAuthorizeAddReqDTOList(cardAuthorizeAddReqDTOList);

        List<CardAuthorizeDTO> addBatchCardAuthorizeDTOList = CardAuthorizeDO.me().buildAddBatchCardAuthorizeDTOList(cardAuthorizeAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardAuthorizeDTOList);
    }

    public CardAuthorizeShowResDTO show(final String id) {
        CardAuthorizeDTO cardAuthorizeDTO = super.findById(id);

        return CardAuthorizeDO.me().transferCardAuthorizeShowResDTO(cardAuthorizeDTO);
    }

    public List<CardAuthorizeShowResDTO> showByIds(final List<String> ids) {
        CardAuthorizeDO.me().checkIds(ids);

        List<CardAuthorizeDTO> cardAuthorizeDTOList = super.findBatchIds(ids);

        return CardAuthorizeDO.me().transferCardAuthorizeShowResDTOList(cardAuthorizeDTOList);
    }

    public Boolean modify(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        CardAuthorizeDO.me().checkCardAuthorizeModifyReqDTO(cardAuthorizeModifyReqDTO);

        CardAuthorizeDTO modifyCardAuthorizeDTO = CardAuthorizeDO.me().buildModifyCardAuthorizeDTO(cardAuthorizeModifyReqDTO);

        return super.modifyById(modifyCardAuthorizeDTO);
    }

    public Boolean modifyAllColumn(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        CardAuthorizeDO.me().checkCardAuthorizeModifyReqDTO(cardAuthorizeModifyReqDTO);

        CardAuthorizeDTO modifyCardAuthorizeDTO = CardAuthorizeDO.me().buildModifyCardAuthorizeDTO(cardAuthorizeModifyReqDTO);

        return super.modifyAllColumnById(modifyCardAuthorizeDTO);
    }

    public Boolean removeByParams(final CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO) {
        CardAuthorizeDO.me().checkCardAuthorizeRemoveReqDTO(cardAuthorizeRemoveReqDTO);

        CardAuthorizeDTO removeCardAuthorizeDTO = CardAuthorizeDO.me().buildRemoveCardAuthorizeDTO(cardAuthorizeRemoveReqDTO);

        return super.remove(removeCardAuthorizeDTO);
    }

    @Override
    protected CardAuthorizePO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardAuthorizePO();
        }

        return BeanUtil.toBean(map, CardAuthorizePO.class);
    }

    @Override
    protected CardAuthorizeDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardAuthorizeDTO();
        }

        return BeanUtil.toBean(map, CardAuthorizeDTO.class);
    }

    public CardAuthorizeDTO findByTradeId(String tradeId,String tradeType) {
        QueryWrapper<CardAuthorizePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_ID,tradeId);
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_TYPE,tradeType);
        queryWrapper.last("limit 1");
        return super.findOne(queryWrapper);
    }

    public List<CardAuthorizeDTO> findListByTradeId(String tradeId,String tradeType) {
        QueryWrapper<CardAuthorizePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_ID,tradeId);
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_TYPE,tradeType);
        return super.findList(queryWrapper);
    }

    public CardAuthorizeDTO findByTradeIdAndSub(String tradeId, String subTradeId, String tradeType) {
        QueryWrapper<CardAuthorizePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_ID,tradeId);
        queryWrapper.eq(CardAuthorizePO.DB_COL_TRADE_TYPE,tradeType);
        queryWrapper.eq(CardAuthorizePO.DB_COL_SUB_TRADE_ID,subTradeId);
        queryWrapper.last("limit 1");
        return super.findOne(queryWrapper);
    }
}
