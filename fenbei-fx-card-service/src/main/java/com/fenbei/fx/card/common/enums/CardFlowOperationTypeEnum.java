package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 生效模式
 */
@AllArgsConstructor
public enum CardFlowOperationTypeEnum {

    /**
     * 卡流水操作类型
     */
    UNKNOWN(-1,"未知"),
    CREDIT_APPLY(4, "申请额度"),
    CREDIT_RETURN(5, "主动退还"),
    FROZEN(6,"冻结"),
    UNFROZEN(7,"解冻"),
    CONSUME(11, "消费"),
    REFUND(12, "退款"),
    RECTIFICATION(16, "冲正"),
    REPAYMENT(41, "还款"),
    CREDIT_COMPANY_RECYCLE(51, "企业回收"),
    CREDIT_SYSTEM_RECYCLE(52, "系统回收"),
    REPAYMENT_RETURN(53, "还款退回"),
    ;

    public static CardFlowOperationTypeEnum getEnum(Integer code) {
        for (CardFlowOperationTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return UNKNOWN;
    }

    public static String getEnumName(Integer code) {
        for (CardFlowOperationTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                if (Objects.equals(FROZEN.getCode(),code)){
                    return "预授权";
                }
                if (Objects.equals(UNFROZEN.getCode(),code)){
                    return "预授权撤销";
                }
                return item.getName();
            }
        }
        return UNKNOWN.getName();
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return I18nUtils.getMessage("CardFlowOperationTypeEnum." + this.name(), this.name);
    }

    public void setName(String name) {
        this.name = name;
    }

}
