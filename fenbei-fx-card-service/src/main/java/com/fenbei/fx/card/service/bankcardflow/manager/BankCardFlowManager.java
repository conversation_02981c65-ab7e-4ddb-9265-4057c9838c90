package com.fenbei.fx.card.service.bankcardflow.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.bankcardflow.BankCardFlowDAO;
import com.fenbei.fx.card.dao.bankcardflow.po.BankCardFlowPO;
import com.fenbei.fx.card.service.bankcardflow.domain.BankCardFlowDO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import com.fenbei.fx.card.service.bankcardflow.converter.BankCardFlowConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class BankCardFlowManager extends BaseManagerImpl<BankCardFlowDAO, BankCardFlowPO, BankCardFlowDTO, BankCardFlowConverter> {

    public static BankCardFlowManager me() {
        return SpringUtil.getBean(BankCardFlowManager.class);
    }

    public List<BankCardFlowListResDTO> list(final BankCardFlowListReqDTO bankCardFlowListReqDTO) {
        BankCardFlowDTO paramsDTO = BankCardFlowDO.me().buildListParamsDTO(bankCardFlowListReqDTO);

        List<BankCardFlowDTO> bankCardFlowDTOList = super.findList(paramsDTO);

        return BankCardFlowDO.me().transferBankCardFlowListResDTOList(bankCardFlowDTOList);
    }

    public BankCardFlowListResDTO listOne(final BankCardFlowListReqDTO bankCardFlowListReqDTO) {
        BankCardFlowDTO paramsDTO = BankCardFlowDO.me().buildListParamsDTO(bankCardFlowListReqDTO);

        BankCardFlowDTO bankCardFlowDTO = super.findOne(paramsDTO);

        return BankCardFlowDO.me().transferBankCardFlowListResDTO(bankCardFlowDTO);
    }

    public Page<BankCardFlowPageResDTO> pagination(final BankCardFlowPageReqDTO bankCardFlowPageReqDTO, final Integer current, final Integer size) {
        BankCardFlowDTO paramsDTO = BankCardFlowDO.me().buildPageParamsDTO(bankCardFlowPageReqDTO);

        Page<BankCardFlowDTO> bankCardFlowDTOPage = super.findPage(paramsDTO, current, size);

        return BankCardFlowDO.me().transferBankCardFlowPageResDTOPage(bankCardFlowDTOPage);
    }

    public Boolean add(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        BankCardFlowDO.me().checkBankCardFlowAddReqDTO(bankCardFlowAddReqDTO);

        BankCardFlowDTO addBankCardFlowDTO = BankCardFlowDO.me().buildAddBankCardFlowDTO(bankCardFlowAddReqDTO);

        return super.saveDTO(addBankCardFlowDTO);
    }

    public Boolean addAllColumn(final BankCardFlowAddReqDTO bankCardFlowAddReqDTO) {
        BankCardFlowDO.me().checkBankCardFlowAddReqDTO(bankCardFlowAddReqDTO);

        BankCardFlowDTO addBankCardFlowDTO = BankCardFlowDO.me().buildAddBankCardFlowDTO(bankCardFlowAddReqDTO);

        return super.saveAllColumn(addBankCardFlowDTO);
    }

    public Boolean addBatchAllColumn(final List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList) {
        BankCardFlowDO.me().checkBankCardFlowAddReqDTOList(bankCardFlowAddReqDTOList);

        List<BankCardFlowDTO> addBatchBankCardFlowDTOList = BankCardFlowDO.me().buildAddBatchBankCardFlowDTOList(bankCardFlowAddReqDTOList);

        return super.saveBatchAllColumn(addBatchBankCardFlowDTOList);
    }

    public BankCardFlowShowResDTO show(final String id) {
        BankCardFlowDTO bankCardFlowDTO = super.findById(id);

        return BankCardFlowDO.me().transferBankCardFlowShowResDTO(bankCardFlowDTO);
    }

    public List<BankCardFlowShowResDTO> showByIds(final List<String> ids) {
        BankCardFlowDO.me().checkIds(ids);

        List<BankCardFlowDTO> bankCardFlowDTOList = super.findBatchIds(ids);

        return BankCardFlowDO.me().transferBankCardFlowShowResDTOList(bankCardFlowDTOList);
    }

    public Boolean modify(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        BankCardFlowDO.me().checkBankCardFlowModifyReqDTO(bankCardFlowModifyReqDTO);

        BankCardFlowDTO modifyBankCardFlowDTO = BankCardFlowDO.me().buildModifyBankCardFlowDTO(bankCardFlowModifyReqDTO);

        return super.modifyById(modifyBankCardFlowDTO);
    }

    public Boolean modifyAllColumn(final BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO) {
        BankCardFlowDO.me().checkBankCardFlowModifyReqDTO(bankCardFlowModifyReqDTO);

        BankCardFlowDTO modifyBankCardFlowDTO = BankCardFlowDO.me().buildModifyBankCardFlowDTO(bankCardFlowModifyReqDTO);

        return super.modifyAllColumnById(modifyBankCardFlowDTO);
    }

    public Boolean removeByParams(final BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO) {
        BankCardFlowDO.me().checkBankCardFlowRemoveReqDTO(bankCardFlowRemoveReqDTO);

        BankCardFlowDTO removeBankCardFlowDTO = BankCardFlowDO.me().buildRemoveBankCardFlowDTO(bankCardFlowRemoveReqDTO);

        return super.remove(removeBankCardFlowDTO);
    }

    @Override
    protected BankCardFlowPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new BankCardFlowPO();
        }

        return BeanUtil.toBean(map, BankCardFlowPO.class);
    }

    @Override
    protected BankCardFlowDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new BankCardFlowDTO();
        }

        return BeanUtil.toBean(map, BankCardFlowDTO.class);
    }
}
