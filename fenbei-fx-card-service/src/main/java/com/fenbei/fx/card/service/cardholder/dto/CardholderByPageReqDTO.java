package com.fenbei.fx.card.service.cardholder.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
@Data
public class CardholderByPageReqDTO implements Serializable {

    private static final long serialVersionUID = 7993041966674140800L;
    /**
     * 和前端约定，区分申请单表还是持卡人表
     */
    @NotNull
    private Integer showStatus;

    /**
     * 员工id
     */
    private String employeeId;

    private String name;

    private String phone;

    private String companyId;

    private Integer pageNo;

    private Integer pageSize;

    private String currentUserId;

    public Integer getOffset() {
        return (this.pageNo - 1) * this.pageSize;
    }



}
