package com.fenbei.fx.card.util;

import com.fenbeitong.finhub.common.utils.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateTimeUtil {

    public static String toUtc(String dateParam) throws ParseException {
        SimpleDateFormat sdfLocal = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdfLocal.parse(dateParam);
        //格式化时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
        //解析时间 2016-01-05T15:06:58+0800
        return sdf.format(date);
    }
    public static String fromUtc(String dateParam,String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date = sdf.parse(dateParam);
        SimpleDateFormat sdfLocal = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdfLocal.format(date);
    }
    public static Date fromUtcToDate(String dateParam,String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
        //        SimpleDateFormat sdfLocal = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.parse(dateParam);
    }

    public static Date fromUtcToUtcString(String dateParam) {
        String dateString = dateParam.substring(0,19).replace("T"," ");
        return DateUtils.parse(dateString,"yyyy-MM-dd HH:mm:ss");
    }
    public static void main(final String... args) throws ParseException {
//        System.out.println("Now: " + now);
//        System.out.println("UTC: " + utc.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
//        System.out.println(toUtc("2023-05-18 20:07:12"));
//        System.out.println(fromUtc("2023-05-18T20:07:12.123+0800","yyyy-MM-dd'T'HH:mm:ss.SSSZ"));
        System.out.println(fromUtcToUtcString("2023-07-03T09:58:29.892+0000"));
    }
}
