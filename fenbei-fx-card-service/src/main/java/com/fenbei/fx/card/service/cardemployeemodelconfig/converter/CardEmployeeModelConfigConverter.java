package com.fenbei.fx.card.service.cardemployeemodelconfig.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardemployeemodelconfig.po.CardEmployeeModelConfigPO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡员工使用模式配置 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardEmployeeModelConfigConverter extends BaseConverter<CardEmployeeModelConfigDTO, CardEmployeeModelConfigPO> {

    static CardEmployeeModelConfigConverter me() {
        return SpringUtil.getBean(CardEmployeeModelConfigConverter.class);
    }

    CardEmployeeModelConfigDTO convertToCardEmployeeModelConfigDTO(CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO);

    CardEmployeeModelConfigDTO convertToCardEmployeeModelConfigDTO(CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO);

    CardEmployeeModelConfigDTO convertToCardEmployeeModelConfigDTO(CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO);

    CardEmployeeModelConfigDTO convertToCardEmployeeModelConfigDTO(CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO);

    CardEmployeeModelConfigDTO convertToCardEmployeeModelConfigDTO(CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO);

    CardEmployeeModelConfigShowResDTO convertToCardEmployeeModelConfigShowResDTO(CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO);

    List<CardEmployeeModelConfigShowResDTO> convertToCardEmployeeModelConfigShowResDTOList(List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList);

    CardEmployeeModelConfigListResDTO convertToCardEmployeeModelConfigListResDTO(CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO);

    List<CardEmployeeModelConfigListResDTO> convertToCardEmployeeModelConfigListResDTOList(List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList);

    List<CardEmployeeModelConfigDTO> convertToCardEmployeeModelConfigDTOList(List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList);

    CardEmployeeModelConfigPageResDTO convertToCardEmployeeModelConfigPageResDTO(CardEmployeeModelConfigDTO cardEmployeeModelConfigDTO);

    List<CardEmployeeModelConfigPageResDTO> convertToCardEmployeeModelConfigPageResDTOList(List<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOList);

    Page<CardEmployeeModelConfigPageResDTO> convertToCardEmployeeModelConfigPageResDTOPage(Page<CardEmployeeModelConfigDTO> cardEmployeeModelConfigDTOPage);
}
