package com.fenbei.fx.card.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class MoneyNumberFormatUtil {

    public static String formart(BigDecimal price) {
        DecimalFormat format = new DecimalFormat("##,##0.00");
        if (null == price) {
            return format.format(new BigDecimal(0));
        } else if (price.compareTo(BigDecimal.ZERO) >= 0) {
            return format.format(price);
        } else {
            BigDecimal priceABS = price.abs();
            return format.format(priceABS);
        }
    }

    /**
     * 金额千分位
     * @param text 文本
     * @return String
     */
    /*public static String fmtMicrometer(String text) {
        DecimalFormat df = null;
        if (text.indexOf(".") > 0) {
            if (text.length() - text.indexOf(".") - 1 == 0) {
                df = new DecimalFormat("###,##0.");
            } else if (text.length() - text.indexOf(".") - 1 == 1) {
                df = new DecimalFormat("###,##0.00");
            } else {
                df = new DecimalFormat("###,##0.00");
            }
        } else {
            df = new DecimalFormat("###,##0");
        }
        double number = 0.0;
        try {
            number = Double.parseDouble(text);
        } catch (Exception e) {
            number = 0.0;
        }
        return df.format(number);
    }*/

    /*public static void main(String[] args) {
        System.out.println(PriceNumberFormatUtil.formart(new BigDecimal("0.2")));
    }*/
}
