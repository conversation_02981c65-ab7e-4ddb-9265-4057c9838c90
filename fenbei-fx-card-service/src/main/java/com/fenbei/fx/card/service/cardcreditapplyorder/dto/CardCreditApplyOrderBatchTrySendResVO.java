package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 国际卡额度发放单 批量发放 ResVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderBatchTrySendResVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 批量发放结果详情
     */
    private List<BatchTrySendResult> results;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchTrySendResult implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 申请单ID
         */
        private String applyOrderId;

        /**
         * 是否成功
         */
        private Boolean success;

        /**
         * 结果消息
         */
        private String message;

        /**
         * 发放状态
         */
        private Integer applyState;

        /**
         * 发放状态描述
         */
        private String applyStateDesc;

        /**
         * 申请人id
         */
        private String applicantId;

        /**
         * 申请人姓名
         */
        private String applicantName;

        /**
         * 申请金额
         */
        private java.math.BigDecimal applyAmount;

        /**
         * 币种
         */
        private String currency;
    }

}
