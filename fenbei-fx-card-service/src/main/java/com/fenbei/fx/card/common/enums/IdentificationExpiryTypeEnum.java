package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 证件到期类型
 */
@AllArgsConstructor
public enum IdentificationExpiryTypeEnum {

    SCOPE_TIME(1, "时间范围"),
    LONG_TIME(2, "长期有效"),
    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("时间范围", "identification.expiry.type.scope.time");
        I18N_KEY_MAP.put("长期有效", "identification.expiry.type.long.time");
    }

    public static IdentificationExpiryTypeEnum getEnum(Integer code) {
        for (IdentificationExpiryTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    public static boolean isLongTime(Integer code) {
        return Objects.equals(IdentificationExpiryTypeEnum.LONG_TIME.code, code);
    }

    public static boolean isScopeTime(Integer code) {
        return Objects.equals(IdentificationExpiryTypeEnum.SCOPE_TIME.code, code);
    }

    private Integer code;

    private String name;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        String i18nKey = I18N_KEY_MAP.get(name);
        return i18nKey == null ? name : I18nUtils.getMessage(i18nKey);
    }

    public void setName(String name) {
        this.name = name;
    }
}
