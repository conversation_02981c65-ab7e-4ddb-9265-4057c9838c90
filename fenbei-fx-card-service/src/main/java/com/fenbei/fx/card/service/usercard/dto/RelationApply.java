package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class RelationApply implements Serializable {

    /**
     * 额度管理biz_no
     */
    private String recordId;

    /**
     * 申请单ID
     */
    private String applyTransNo;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    private String applyAmountShow;

    /**
     * 可核销金额
     */
    private BigDecimal uncheckedAmount;

    private String uncheckedAmountShow;

    /**
     * 提交时间
     */
    private Date applyTime;

    private String applyTimeShow;

    /**
     * 关联金额
     */
    private BigDecimal relationAmount;

    private String relationAmountShow;
}
