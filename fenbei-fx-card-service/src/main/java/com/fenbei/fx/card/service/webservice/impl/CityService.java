package com.fenbei.fx.card.service.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.fenbei.fx.card.service.webservice.dto.HarmonyCityRespDto;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

@Service
public class CityService {

    @Value("${host.harmony}")
    private String harmonyHost;

    public HarmonyCityRespDto queryCity(String provinceId, String cityId, String districtId) {
        try {
            String cityUrl = harmonyHost + "/city/cgb/toBankCity";
            Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("provinceId", provinceId);
            bodyMap.put("cityId", cityId);
            bodyMap.put("districtId",districtId);
            String cityBody = JSON.toJSONString(bodyMap);
            FinhubLogger.info("查询城市URL:{},内容：{}",cityUrl, cityBody);
            String result = HttpClientUtils.postBody(cityUrl, cityBody);
            FinhubLogger.info("查询城市URL:{},结果：{}",cityUrl,  result);
            return JsonUtils.toObj(result,HarmonyCityRespDto.class);
        } catch (Exception e) {
            FinhubLogger.error("查询城市异常：" + e.getMessage(), e);
        }
        return null;
    }
}
