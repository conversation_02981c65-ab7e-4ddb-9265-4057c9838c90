package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 模式类型
 */
@AllArgsConstructor
public enum CreditApplyStatusEnum {
    FAILED(0, "下发失败"),
    /**
     *  额度状态: 1.下发成功,2.下发失败 3 下发中 4 代下发
     */
    SUCCESS(1, "下发成功"),
    FAIL(2, "下发失败"),
    PROCESS(3, "下发中"),
    INIT(4, "待下发"),

    ;

    public static CreditApplyStatusEnum getEnum(Integer code) {
        for (CreditApplyStatusEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return I18nUtils.getMessage("CreditApplyStatusEnum." + this.name(), this.name);
    }

    public void setName(String name) {
        this.name = name;
    }

}
