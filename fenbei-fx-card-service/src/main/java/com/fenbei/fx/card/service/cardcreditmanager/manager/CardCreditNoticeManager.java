package com.fenbei.fx.card.service.cardcreditmanager.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.enums.ApplyOrderTypeEnum;
import com.fenbei.fx.card.common.enums.CreditApplyTypeEnum;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerReturnReqDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.msg.voucher.VouSourceReimburseKafkaMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupReceiverVO;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupVO;
import com.fenbeitong.saas.api.service.message.setting.IMessageSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CardCreditNoticeManager {

    @Autowired
    CardService cardService;

    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;
    @Autowired
    public MessageService messageService;
    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;
    @DubboReference
    private IMessageSettingService iMessageSettingService;

    public static String OVERSEA_CREDIT_FAIL_REMIND = "oversea_credit_fail_remind";

    public static String CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID = "67569ddf59aac212705316a4";

    public static final String VOUCHER_MSG_TYPE = "CREATE_FX_CARD_APPLY";
    public static final String VOUCHER_MSG_REFUND_TYPE = "CREATE_FX_CARD_REFUND";





    public void sendNoticeMsgForSuccess(String fxCardId, BigDecimal operAmount, String bizNo) {
        CardDTO cardDTO = cardService.cardDetailByFxCardId(fxCardId);
        try {
            String title = "海外卡额度发放成功";
           //默认发给本人, 默认勾选配置：  - 消息类型：App/Web消息；
            sendPersonNoticeForSuccess(cardDTO, operAmount, bizNo, title, true, true, false);
        } catch (Exception e) {
            FinhubLogger.warn("额度发放发送通知消息推送异常，employeeId={}, applyAmount={},bizNo={}", cardDTO.getEmployeeId(), operAmount, bizNo, e);
        }
    }



    private String convertMsgForSuccess4Holder(CardDTO cardDTO, BigDecimal operAmount) {
        String currency = cardDTO.getCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String lang = RpcContext.getContext().getAttachment("lang");

        String message = MessageFormat.format("企业给您发放了一笔{0}{1}的海外卡额度,点击查看发放单详情", currencyByCode.getSymbol(), BigDecimalUtils.fen2yuan(operAmount));
        return message;
    }

    private String convertMsgForSuccess4HolderSMS(CardDTO cardDTO, BigDecimal operAmount) {
        String currency = cardDTO.getCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String lang = RpcContext.getContext().getAttachment("lang");
        String fromLang = RpcContext.getContext().getAttachment("from_lang");
        if ((ObjUtils.isNotBlank(lang) && lang.equals(MultiLanguageEnum.EN_US.getCode()))||(ObjUtils.isNotBlank(fromLang) && fromLang.equals(MultiLanguageEnum.EN_US.getCode()))){
            log.info("lang:{},fromLang:{}",lang,fromLang);
            return MessageFormat.format("The company has issued you an overseas card limit of {0}{1}", currencyByCode.getSymbol(), BigDecimalUtils.fen2yuan(operAmount));
        }
        return MessageFormat.format("企业给您发放了一笔{0}{1}的海外卡额度", currencyByCode.getSymbol(), BigDecimalUtils.fen2yuan(operAmount));
    }

    private void sendWebPush4CreditApplySuccess(String companyId, String employeeId, String bankName, String bankAccountNo, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(85);
            kafkaWebMessageMsg.setBizType(85);
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(employeeId);
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", bankName);
            json.put("bankAccountNo", bankAccountNo);
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            FinhubLogger.info("海外卡额度发放成功推送WebPush 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放成功消息发送失败");
            e.printStackTrace();
        }
    }


    private void sendMsgCenter4CreditApplySuccess(String companyId, String employeeId, String bizNo, String msg, String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(85);
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡额度发放成功推送 MsgCenter 参数={}", JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放成功消息中心发送失败！！！");
            e.printStackTrace();
        }
    }



    private void pushAlert4CreditApplySuccess(String companyId, String employeeId, String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal()));
            msgInfo.put("biz_type", String.valueOf(85));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0 + "");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡额度发放成功push消息参数kafkaPushMsg:{}", kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放成功推送失败:{}", msg, e);
        }
    }



    private void sendMsg4CreditApplySuccess(String phone, String tempId, String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡额度发放成功,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡额度发放成功，短信发送失败！！！");
            e2.printStackTrace();
        }
    }

    public void sendPersonNoticeForSuccess(CardDTO cardDTO, BigDecimal operAmount, String bizNo, String title,
                                           Boolean appWebFlag, Boolean smsFlag, Boolean emailFlag) {
        String message = convertMsgForSuccess4Holder(cardDTO, operAmount);
        if (appWebFlag) {
            sendWebPush4CreditApplySuccess(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getCardPlatform(), cardDTO.getBankCardNo(), message, title);
            sendMsgCenter4CreditApplySuccess(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), bizNo, message, title);
            pushAlert4CreditApplySuccess(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), bizNo, message, title, null);
        }
        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardDTO.getEmployeeId()), cardDTO.getCompanyId());
        EmployeeCompanyDto employeeOrgUnitDTO = employeeCompanyDtos.get(0);
        if (smsFlag) {
            message = convertMsgForSuccess4HolderSMS(cardDTO, operAmount);
            sendMsg4CreditApplySuccess(employeeOrgUnitDTO.getUserPhone(), CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID, message);
        }
    }
    /**
     * this.setIs_check(setup.getIsChecked());
     * this.setApp_notice(setup.getIntVal1());
     * this.setMail_notice(setup.getIntVal2());
     * this.setPhone_notice(setup.getIntVal3());
     * this.setAuth_manager(Integer.parseInt(setup.getStrVal1()));
     * this.setCard_holder(Integer.parseInt(setup.getStrVal2()));
     */

    public void sendNoticeMsgForFailed(String fxCardId, BigDecimal operAmount, String bizNo) {
        CardDTO cardDTO = cardService.cardDetailByFxCardId(fxCardId);
        try {
            String title = "海外卡额度发放失败";
            //查询配置
            FinhubLogger.info("海外卡额度发放失败 查询消息配置信息 companyId = {}", cardDTO.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(cardDTO.getCompanyId(), Collections.singletonList(OVERSEA_CREDIT_FAIL_REMIND));
            FinhubLogger.info("海外卡额度发放失败 查询消息配置信息 companyId = {}，res = {}", cardDTO.getCompanyId(), messageSetupVOS);
            if (messageSetupVOS != null && messageSetupVOS.size() > 0) {
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0) {
                    String cardHolder = messageSetupVO.getStrVal1();
                    boolean appWebFlag = (1 == messageSetupVO.getIntVal1());
                    boolean emailFlag = (1 == messageSetupVO.getIntVal2());
                    boolean smsFlag = (1 == messageSetupVO.getIntVal3());
                    if ("1".equals(cardHolder)) {
                        //指定持卡人
                        sendPersonNoticeForFailed(cardDTO, operAmount, bizNo, title, appWebFlag, smsFlag, emailFlag);
                    }
                    //额外配置人员
                    sendCompanyNoticeFailed(messageSetupVO, cardDTO, operAmount, bizNo, title);
                } else {
                    //默认发给本人, 默认勾选配置：  - 消息类型：App/Web消息；
                    sendPersonNoticeForFailed(cardDTO, operAmount, bizNo, title, true, false, false);
                }
            }
        } catch (Exception e) {
            FinhubLogger.warn("额度回收发送通知消息推送异常，employeeId={}, applyAmount={},bizNo={}", cardDTO.getEmployeeId(), operAmount, bizNo, e);
        }
    }



    private void sendPersonNoticeForFailed(CardDTO cardDTO, BigDecimal operAmount, String bizNo, String title,
                                           Boolean appWebFlag, Boolean smsFlag, Boolean emailFlag) {
        String message = convertMsgForFailed4Holder(cardDTO, operAmount);
        if (appWebFlag) {
            sendWebPush4CreditApplyFail(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), cardDTO.getCardPlatform(), cardDTO.getBankCardNo(), message, title);
            sendMsgCenter4CreditApplyFail(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), bizNo, message, title);
            pushAlert4CreditApplyFail(cardDTO.getCompanyId(), cardDTO.getEmployeeId(), bizNo, message, title, null);
        }
        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardDTO.getEmployeeId()), cardDTO.getCompanyId());
        EmployeeCompanyDto employeeOrgUnitDTO = employeeCompanyDtos.get(0);
        if (emailFlag) {
            FinhubLogger.info("海外卡额度发放失败发送邮件给本人信息 employeeId = {}, email = {}", cardDTO.getEmployeeId(), employeeOrgUnitDTO.getUserEmail());
            if (StringUtils.isNotBlank(employeeOrgUnitDTO.getUserEmail())) {
                sendMail4CreditApplyFail(Collections.singleton(employeeOrgUnitDTO.getUserEmail()), message);
            }
        }
        if (smsFlag) {
            sendMsg4CreditApplyFail(employeeOrgUnitDTO.getUserPhone(), CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID, message);
        }
    }

    private void sendCompanyNoticeFailed(MessageSetupVO messageSetupVO, CardDTO cardDTO, BigDecimal operAmount, String bizNo, String title) {
        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(cardDTO.getCompanyId(), OVERSEA_CREDIT_FAIL_REMIND);

        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)) {
            return;
        }
        List<String> userIdList = messageSetupReceiverVOS.stream().map(MessageSetupReceiverVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardDTO.getEmployeeId()), cardDTO.getCompanyId());
        EmployeeCompanyDto companyEmployeeHolder = employeeCompanyDtos.get(0);
        String message = convertMsgForFailed4Other(cardDTO, operAmount, companyEmployeeHolder);
        if (messageSetupVO.getIntVal1() > 0) {//APP通知
            userIdList.forEach(userId -> {
                try {
                    FinhubLogger.info("海外卡额度发放失败发送站内信给配置人员  入参 req = {},msg = {}", userId, message);
                    sendWebPush4CreditApplyFail(cardDTO.getCompanyId(), userId, cardDTO.getCardPlatform(), cardDTO.getBankCardNo(), message, title);
                    sendMsgCenter4CreditApplyFail(cardDTO.getCompanyId(), userId, bizNo, message, title);
                    pushAlert4CreditApplyFail(cardDTO.getCompanyId(), userId, bizNo, message, title, null);
                } catch (Exception e) {
                    FinhubLogger.info("海外卡额度发放失败发送站内通知给配置人员失败 userId = {} , message = {}", userId, message, e);
                }
            });
        }
        List<EmployeeCompanyDto> employeeOrgUnitDTOSOther = baseEmployeeExtService.queryEmployeeCompanyInfoListById(userIdList, cardDTO.getCompanyId());
        if (messageSetupVO.getIntVal2() > 0) {//邮件通知
            Set<String> mailSet = employeeOrgUnitDTOSOther.stream().map(EmployeeCompanyDto::getUserEmail).collect(Collectors.toSet());
            FinhubLogger.info("海外卡额度发放失败发送邮件给配置人员  入参 req = {},msg = {}", mailSet, message);
            sendMail4CreditApplyFail(mailSet, message);
        }
        if (messageSetupVO.getIntVal3() > 0) {//短信通知
            FinhubLogger.info("海外卡额度发放失败发送短信给配置人员  入参 req = {},msg = {}", userIdList, message);
            employeeOrgUnitDTOSOther.forEach(employee -> {
                try {
                    sendMsg4CreditApplyFail(employee.getUserPhone(), CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID, message);
                } catch (Exception e) {
                    FinhubLogger.info("海外卡额度发放失败发送短信给配置人员失败 employeeId = {} , message = {}", employee.getId(), message, e);
                }
            });
        }
    }


    private String convertMsgForFailed4Holder(CardDTO cardDTO, BigDecimal operAmount) {
        String currency = cardDTO.getCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String message = MessageFormat.format("由于企业账户余额不足，您有{0}{1}海外卡额度下发失败，请联系管理员进行充值，余额充足后将为您重新下发额度。", currencyByCode.getSymbol(), BigDecimalUtils.fen2yuan(operAmount));
        return message;
    }

    private String convertMsgForFailed4Other(CardDTO cardDTO, BigDecimal operAmount, EmployeeCompanyDto companyEmployeeHolder) {
        String currency = cardDTO.getCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String message = MessageFormat.format("由于企业账户余额不足，{0}申请的{1}{2}海外卡额度下发失败，请联系管理员进行充值，余额充足后将自动重新下发额度。", companyEmployeeHolder.getUserName(), currencyByCode.getSymbol(), BigDecimalUtils.fen2yuan(operAmount));
        return message;
    }


    private void sendWebPush4CreditApplyFail(String companyId, String employeeId, String bankName, String bankAccountNo, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.FxCardCreditApply.getCode());
            kafkaWebMessageMsg.setBizType(BizType.FxCardCreditApply.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(employeeId);
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", bankName);
            json.put("bankAccountNo", bankAccountNo);
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            FinhubLogger.info("海外卡额度发放失败推送WebPush 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放失败消息发送失败");
            e.printStackTrace();
        }
    }

    private void sendMsg4CreditApplyFail(String phone, String tempId, String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡额度发放失败,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡额度发放失败，短信发送失败！！！");
            e2.printStackTrace();
        }
    }


    private void sendMsgCenter4CreditApplyFail(String companyId, String employeeId, String bizNo, String msg, String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.FxCardCreditApply.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡额度发放失败推送 MsgCenter 参数={}", JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放失败消息中心发送失败！！！");
            e.printStackTrace();
        }
    }

    public void sendMail4CreditApplyFail(Set<String> emailSet, String msg) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email -> {
                if (EmailCheckUtils.emailFormat(email)) {
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)) {
                return;
            }
            FinhubLogger.info("sendCreditRefundMail req = {},msg = {}", emailSet, msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】企业海外卡额度下发失败";
            emailContract.setSubject(subject);
            String text = "您好!" + "\n" + "感谢您使用分贝通的服务" + "\n" + msg;
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        } catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}", emailSet, msg, e);
        }
    }

    private void pushAlert4CreditApplyFail(String companyId, String employeeId, String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.FxCardCreditApply.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0 + "");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡额度发放失败push消息参数kafkaPushMsg:{}", kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放失败push消息参数msg失败:{}", msg, e);
        }
    }


    public void sendNoticeMsgForVoucher(CardCreditManagerApplyReqDTO apply, CardDTO card) {
        try {
            FinhubLogger.info("海外卡额度发放完成push消息参数kafkaPushMsg:{},card={}", apply, card);
            VouSourceReimburseKafkaMsg msg = new VouSourceReimburseKafkaMsg();
            msg.setMsgType(VOUCHER_MSG_TYPE);
            msg.setCompanyId(apply.getCompanyId());
            msg.setBillId(apply.getSaasApplyNo());
            msg.setBillNo(apply.getSaasApplyMeaningNo());
            BigDecimal billAmount = apply.getApplyCreditAmount();
            msg.setBillAmount(billAmount);
            msg.setBillTypeId(apply.getBankName());
            msg.setEmployeeId(apply.getEmployeeId());
            msg.setEmployeeName(card.getNameOnCard());
            DateTime date = DateUtil.date();
            msg.setSubmitTime(date);
            msg.setApproveTime(date);
            msg.setPayTime(date);
            msg.setBillReason(apply.getApplyReasonDesc());
            log.info("发送凭证消息:{}", JSONObject.toJSONString(msg));
            kafkaProducerPublisher.publish(msg, apply.getSaasApplyNo());
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度发放完成push消息参数msg失败:", e);
        }
    }

    public void sendNoticeMsgForVoucherRefund(CardCreditManagerReturnReqDTO Refund, List<CardCreditManagerDTO> creditManagerDTOS, CardDTO card, Map<String, String> applyIdMap) {
        try {
            FinhubLogger.info("海外卡额度退回完成push消息 参数kafkaPushMsg:{},card={}", Refund, card);
            Map<String, BigDecimal> returnAmount = Refund.getReturnAmount();
            for (String key : returnAmount.keySet()) {
                CardCreditManagerDTO apply = creditManagerDTOS.stream().filter(
                        i -> i.getBizNo().equals(key) && Objects.equals(i.getApplyType(), CreditApplyTypeEnum.APPLY.getCode())
                ).findFirst().get();
                if (ObjectUtil.isEmpty(apply)) {
                    FinhubLogger.info("海外卡额度退回完成push消息 未找到申请单 key={}", key);
                    continue;
                }
                String refundId = applyIdMap.get(key);
                if(StringUtils.isBlank(refundId)){
                    continue;
                }
                VouSourceReimburseKafkaMsg msg = new VouSourceReimburseKafkaMsg();
                msg.setMsgType(VOUCHER_MSG_REFUND_TYPE);
                msg.setCompanyId(card.getCompanyId());
                msg.setBillId(refundId);
                msg.setBillNo(key);
                BigDecimal billAmount = returnAmount.get(key);
                if (billAmount == null || billAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    FinhubLogger.info("海外卡额度退回完成push消息 金额有误 key={}", key);
                    continue;
                }
                msg.setBillAmount(billAmount);
                msg.setEmployeeId(apply.getEmployeeId());
                msg.setEmployeeName(card.getNameOnCard());
                msg.setBillTypeId(card.getCardPlatform());
                DateTime date = DateUtil.date();
                msg.setSubmitTime(date);
                msg.setApproveTime(date);
                msg.setPayTime(date);
                msg.setBillReason(Refund.getApplyReasonDesc());
                log.info("发送凭证消息:{}", JSONObject.toJSONString(msg));
                kafkaProducerPublisher.publish(msg, key);
            }
        } catch (Exception e) {
            FinhubLogger.error("海外卡额度退回完成push消息:", e);
        }
    }
}
