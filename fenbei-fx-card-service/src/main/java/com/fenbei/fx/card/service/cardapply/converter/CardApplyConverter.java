package com.fenbei.fx.card.service.cardapply.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardapply.po.CardApplyPO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyAddReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyListResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyModifyReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageResDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyRemoveReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡操作申请 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardApplyConverter extends BaseConverter<CardApplyDTO, CardApplyPO> {

    static CardApplyConverter me() {
        return SpringUtil.getBean(CardApplyConverter.class);
    }

    CardApplyDTO convertToCardApplyDTO(CardApplyAddReqDTO cardApplyAddReqDTO);

    CardApplyDTO convertToCardApplyDTO(CardApplyModifyReqDTO cardApplyModifyReqDTO);

    CardApplyDTO convertToCardApplyDTO(CardApplyRemoveReqDTO cardApplyRemoveReqDTO);

    CardApplyDTO convertToCardApplyDTO(CardApplyListReqDTO cardApplyListReqDTO);

    CardApplyDTO convertToCardApplyDTO(CardApplyPageReqDTO cardApplyPageReqDTO);

    CardApplyShowResDTO convertToCardApplyShowResDTO(CardApplyDTO cardApplyDTO);

    List<CardApplyShowResDTO> convertToCardApplyShowResDTOList(List<CardApplyDTO> cardApplyDTOList);

    CardApplyListResDTO convertToCardApplyListResDTO(CardApplyDTO cardApplyDTO);

    List<CardApplyListResDTO> convertToCardApplyListResDTOList(List<CardApplyDTO> cardApplyDTOList);

    List<CardApplyDTO> convertToCardApplyDTOList(List<CardApplyAddReqDTO> cardApplyAddReqDTOList);

    CardApplyPageResDTO convertToCardApplyPageResDTO(CardApplyDTO cardApplyDTO);

    List<CardApplyPageResDTO> convertToCardApplyPageResDTOList(List<CardApplyDTO> cardApplyDTOList);

    Page<CardApplyPageResDTO> convertToCardApplyPageResDTOPage(Page<CardApplyDTO> cardApplyDTOPage);
}
