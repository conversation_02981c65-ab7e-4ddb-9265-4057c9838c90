package com.fenbei.fx.card.service.cardholderapply.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholderapply.po.CardholderApplyPO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyAddReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyListReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyListResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyModifyReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyPageReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyPageResDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 持卡人操作申请 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardholderApplyConverter extends BaseConverter<CardholderApplyDTO, CardholderApplyPO> {

    static CardholderApplyConverter me() {
        return SpringUtil.getBean(CardholderApplyConverter.class);
    }

    CardholderApplyDTO convertToCardholderApplyDTO(CardholderApplyAddReqDTO cardholderApplyAddReqDTO);

    CardholderApplyDTO convertToCardholderApplyDTO(CardholderApplyModifyReqDTO cardholderApplyModifyReqDTO);

    CardholderApplyDTO convertToCardholderApplyDTO(CardholderApplyRemoveReqDTO cardholderApplyRemoveReqDTO);

    CardholderApplyDTO convertToCardholderApplyDTO(CardholderApplyListReqDTO cardholderApplyListReqDTO);

    CardholderApplyDTO convertToCardholderApplyDTO(CardholderApplyPageReqDTO cardholderApplyPageReqDTO);

    CardholderApplyShowResDTO convertToCardholderApplyShowResDTO(CardholderApplyDTO cardholderApplyDTO);

    List<CardholderApplyShowResDTO> convertToCardholderApplyShowResDTOList(List<CardholderApplyDTO> cardholderApplyDTOList);

    CardholderApplyListResDTO convertToCardholderApplyListResDTO(CardholderApplyDTO cardholderApplyDTO);

    List<CardholderApplyListResDTO> convertToCardholderApplyListResDTOList(List<CardholderApplyDTO> cardholderApplyDTOList);

    List<CardholderApplyDTO> convertToCardholderApplyDTOList(List<CardholderApplyAddReqDTO> cardholderApplyAddReqDTOList);

    CardholderApplyPageResDTO convertToCardholderApplyPageResDTO(CardholderApplyDTO cardholderApplyDTO);

    List<CardholderApplyPageResDTO> convertToCardholderApplyPageResDTOList(List<CardholderApplyDTO> cardholderApplyDTOList);

    Page<CardholderApplyPageResDTO> convertToCardholderApplyPageResDTOPage(Page<CardholderApplyDTO> cardholderApplyDTOPage);
}
