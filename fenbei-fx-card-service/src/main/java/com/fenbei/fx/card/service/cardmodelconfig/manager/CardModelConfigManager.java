package com.fenbei.fx.card.service.cardmodelconfig.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmodelconfig.CardModelConfigDAO;
import com.fenbei.fx.card.dao.cardmodelconfig.po.CardModelConfigPO;
import com.fenbei.fx.card.service.cardmodelconfig.converter.CardModelConfigConverter;
import com.fenbei.fx.card.service.cardmodelconfig.domain.CardModelConfigDO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigShowResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 国际卡使用模式配置 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardModelConfigManager extends BaseManagerImpl<CardModelConfigDAO, CardModelConfigPO, CardModelConfigDTO, CardModelConfigConverter> {

    public static CardModelConfigManager me() {
        return SpringUtil.getBean(CardModelConfigManager.class);
    }

    public List<CardModelConfigListResDTO> list(final CardModelConfigListReqDTO cardModelConfigListReqDTO) {
        CardModelConfigDTO paramsDTO = CardModelConfigDO.me().buildListParamsDTO(cardModelConfigListReqDTO);

        List<CardModelConfigDTO> cardModelConfigDTOList = super.findList(paramsDTO);

        return CardModelConfigDO.me().transferCardModelConfigListResDTOList(cardModelConfigDTOList);
    }

    public CardModelConfigListResDTO listOne(final CardModelConfigListReqDTO cardModelConfigListReqDTO) {
        CardModelConfigDTO paramsDTO = CardModelConfigDO.me().buildListParamsDTO(cardModelConfigListReqDTO);

        CardModelConfigDTO cardModelConfigDTO = super.findOne(paramsDTO);

        return CardModelConfigDO.me().transferCardModelConfigListResDTO(cardModelConfigDTO);
    }

    public Page<CardModelConfigPageResDTO> pagination(final CardModelConfigPageReqDTO cardModelConfigPageReqDTO, final Integer current, final Integer size) {
        CardModelConfigDTO paramsDTO = CardModelConfigDO.me().buildPageParamsDTO(cardModelConfigPageReqDTO);

        Page<CardModelConfigDTO> cardModelConfigDTOPage = super.findPage(paramsDTO, current, size);

        return CardModelConfigDO.me().transferCardModelConfigPageResDTOPage(cardModelConfigDTOPage);
    }

    public Boolean add(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        CardModelConfigDO.me().checkCardModelConfigAddReqDTO(cardModelConfigAddReqDTO);

        CardModelConfigDTO addCardModelConfigDTO = CardModelConfigDO.me().buildAddCardModelConfigDTO(cardModelConfigAddReqDTO);

        return super.saveDTO(addCardModelConfigDTO);
    }

    public Boolean addAllColumn(final CardModelConfigAddReqDTO cardModelConfigAddReqDTO) {
        CardModelConfigDO.me().checkCardModelConfigAddReqDTO(cardModelConfigAddReqDTO);

        CardModelConfigDTO addCardModelConfigDTO = CardModelConfigDO.me().buildAddCardModelConfigDTO(cardModelConfigAddReqDTO);

        return super.saveAllColumn(addCardModelConfigDTO);
    }

    public Boolean addBatchAllColumn(final List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList) {
        CardModelConfigDO.me().checkCardModelConfigAddReqDTOList(cardModelConfigAddReqDTOList);

        List<CardModelConfigDTO> addBatchCardModelConfigDTOList = CardModelConfigDO.me().buildAddBatchCardModelConfigDTOList(cardModelConfigAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardModelConfigDTOList);
    }

    public CardModelConfigShowResDTO show(final String id) {
        CardModelConfigDTO cardModelConfigDTO = super.findById(id);

        return CardModelConfigDO.me().transferCardModelConfigShowResDTO(cardModelConfigDTO);
    }

    public List<CardModelConfigShowResDTO> showByIds(final List<String> ids) {
        CardModelConfigDO.me().checkIds(ids);

        List<CardModelConfigDTO> cardModelConfigDTOList = super.findBatchIds(ids);

        return CardModelConfigDO.me().transferCardModelConfigShowResDTOList(cardModelConfigDTOList);
    }

    public Boolean modify(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        CardModelConfigDO.me().checkCardModelConfigModifyReqDTO(cardModelConfigModifyReqDTO);

        CardModelConfigDTO modifyCardModelConfigDTO = CardModelConfigDO.me().buildModifyCardModelConfigDTO(cardModelConfigModifyReqDTO);

        return super.modifyById(modifyCardModelConfigDTO);
    }

    public Boolean modifyAllColumn(final CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO) {
        CardModelConfigDO.me().checkCardModelConfigModifyReqDTO(cardModelConfigModifyReqDTO);

        CardModelConfigDTO modifyCardModelConfigDTO = CardModelConfigDO.me().buildModifyCardModelConfigDTO(cardModelConfigModifyReqDTO);

        return super.modifyAllColumnById(modifyCardModelConfigDTO);
    }

    public Boolean removeByParams(final CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO) {
        CardModelConfigDO.me().checkCardModelConfigRemoveReqDTO(cardModelConfigRemoveReqDTO);

        CardModelConfigDTO removeCardModelConfigDTO = CardModelConfigDO.me().buildRemoveCardModelConfigDTO(cardModelConfigRemoveReqDTO);

        return super.remove(removeCardModelConfigDTO);
    }

    @Override
    protected CardModelConfigPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardModelConfigPO();
        }

        return BeanUtil.toBean(map, CardModelConfigPO.class);
    }

    @Override
    protected CardModelConfigDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardModelConfigDTO();
        }

        return BeanUtil.toBean(map, CardModelConfigDTO.class);
    }
}
