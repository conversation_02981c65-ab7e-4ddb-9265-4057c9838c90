package com.fenbei.fx.card.service.cardcreditmanager.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.common.kafka.KafkaProducer;
import com.fenbei.fx.card.dao.cardcreditmanager.CardCreditManagerDAO;
import com.fenbei.fx.card.dao.cardcreditmanager.po.CardCreditManagerPO;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.converter.CardCreditManagerConverter;
import com.fenbei.fx.card.service.cardcreditmanager.domain.CardCreditManagerDO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.*;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.CardCreditManagerRelationService;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.EmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.impl.CardModelConfigServiceImpl;
import com.fenbei.fx.card.service.remote.FxPayAcctManager;
import com.fenbei.fx.card.service.remote.SaasBudgetManager;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.service.remote.dto.SaasBugetDto;
import com.fenbei.fx.card.service.usercard.dto.UserCardCreditGrantRecordDetail;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianCardBalanceAdjustReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianCardDetailReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianCardBalanceAdjustRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianCardDetailRespDTO;
import com.fenbeitong.dech.api.service.lianlian.ILianLianCardService;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.AcctOverseaRespDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaAcctRefundAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.req.OverseaCardApplyAmountReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.na.resp.AccountSubOperationRespRPCDTO;
import com.fenbeitong.fenbeipay.api.service.acct.trade.AcctOverseaCardService;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundPlatformEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCompanyCardAcctChangeMsg;
import com.fenbeitong.fxpay.api.enums.CommonConfigEnum;
import com.fenbeitong.fxpay.api.enums.CommonConfigGrantEnum;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.fxpay.api.enums.OperatorRoleEnum;
import com.fenbeitong.fxpay.api.interfaces.ICompanyAcctService;
import com.fenbeitong.fxpay.api.vo.CompanyAcctRes;
import com.fenbeitong.fxpay.api.vo.FundChangingReq;
import com.fenbeitong.fxpay.api.vo.ResponseVo;
import com.fenbeitong.fxpay.api.vo.acct.CommonConfigReq;
import com.fenbeitong.fxpay.api.vo.acct.CompanyBusinessConfigDTO;
import com.fenbeitong.saasplus.api.model.dto.budget.ApplyOrderReturnAmount;
import com.fenbeitong.saasplus.api.model.dto.budget.VirtualCardControlConf;
import com.fenbeitong.saasplus.api.service.budget.IVirtualCardBudgetService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeSimpleDTO;
import com.fenbeitong.usercenter.api.service.employee.IREmployeeService;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 国际卡额度申请退回管理表 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Slf4j
@Component
public class CardCreditManagerManager extends BaseManagerImpl<CardCreditManagerDAO, CardCreditManagerPO, CardCreditManagerDTO, CardCreditManagerConverter> {
    @Autowired
    CardService cardService;
    @Autowired
    FxPayAcctManager fxPayAcctManager;
    @Autowired
    CardModelConfigServiceImpl cardModelConfigService;
    @Autowired
    BankCardFlowService bankCardFlowService;
    @Autowired
    CardCreditManagerService cardCreditManagerService;
    @Autowired
    SaasBudgetManager saasBudgetManager;
    @DubboReference
    IVirtualCardBudgetService iVirtualCardBudgetService;

    @DubboReference
    ICompanyAcctService iCompanyAcctService;

    @DubboReference
    private IREmployeeService irEmployeeService;

    @DubboReference
    private AcctOverseaCardService acctOverseaCardService;

    @DubboReference
    private ILianLianCardService iLianLianCardService;

    @Autowired
    private CardCreditNoticeManager cardCreditNoticeManager;

    public static CardCreditManagerManager me() {
        return SpringUtil.getBean(CardCreditManagerManager.class);
    }

    public List<CardCreditManagerListResDTO> list(final CardCreditManagerListReqDTO cardCreditManagerListReqDTO) {
        CardCreditManagerDTO paramsDTO = CardCreditManagerDO.me().buildListParamsDTO(cardCreditManagerListReqDTO);

        List<CardCreditManagerDTO> cardCreditManagerDTOList = super.findList(paramsDTO);

        return CardCreditManagerDO.me().transferCardCreditManagerListResDTOList(cardCreditManagerDTOList);
    }

    public CardCreditManagerListResDTO listOne(final CardCreditManagerListReqDTO cardCreditManagerListReqDTO) {
        CardCreditManagerDTO paramsDTO = CardCreditManagerDO.me().buildListParamsDTO(cardCreditManagerListReqDTO);

        CardCreditManagerDTO cardCreditManagerDTO = super.findOne(paramsDTO);

        return CardCreditManagerDO.me().transferCardCreditManagerListResDTO(cardCreditManagerDTO);
    }

    public Page<CardCreditManagerPageResDTO> pagination(final CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardCreditManagerPO> cardCreditManagerPOQueryWrapper = new QueryWrapper<>();

        cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, cardCreditManagerPageReqDTO.getApplyType());
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getCompanyId())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_COMPANY_ID, cardCreditManagerPageReqDTO.getCompanyId());
        }

        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getFxCardId())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_FX_CARD_ID, cardCreditManagerPageReqDTO.getFxCardId());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getApplyTransNo())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_TRANS_NO, cardCreditManagerPageReqDTO.getApplyTransNo());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getApplyMeaningNo())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_MEANING_NO, cardCreditManagerPageReqDTO.getApplyMeaningNo());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getApplyReason())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_REASON, cardCreditManagerPageReqDTO.getApplyReason());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getApplyTitle())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_TITLE, cardCreditManagerPageReqDTO.getApplyTitle());
        }
        if (Objects.nonNull(cardCreditManagerPageReqDTO.getBeginTime()) && Objects.nonNull(cardCreditManagerPageReqDTO.getEndTime())) {
            cardCreditManagerPOQueryWrapper.between(CardCreditManagerPO.DB_COL_CREATE_TIME, cardCreditManagerPageReqDTO.getBeginTime(), cardCreditManagerPageReqDTO.getEndTime());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getOperationUserName())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_OPERATION_USER_NAME, cardCreditManagerPageReqDTO.getOperationUserName());
        }
        if (StringUtils.isNotBlank(cardCreditManagerPageReqDTO.getCostAttribution())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_COST_ATTRIBUTION, cardCreditManagerPageReqDTO.getCostAttribution());
        }
        if (Objects.nonNull(cardCreditManagerPageReqDTO.getApplyStatus())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS, cardCreditManagerPageReqDTO.getApplyStatus());
        }
        //
        if (cardCreditManagerPageReqDTO.getAvalibleAmountFlag()) {
            cardCreditManagerPOQueryWrapper.gt(CardCreditManagerPO.DB_COL_AVALIBLE_AMOUNT, BigDecimal.ZERO);
        }
        cardCreditManagerPOQueryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        Page<CardCreditManagerDTO> cardCreditManagerDTOPage = super.findPage(cardCreditManagerPOQueryWrapper, current, size);
        return CardCreditManagerDO.me().transferCardCreditManagerPageResDTOPage(cardCreditManagerDTOPage);
    }


    public Page<CardCreditManagerPageResDTO> pagination(final CardCreditStereoPageReqDTO pageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardCreditManagerPO> cardCreditManagerPOQueryWrapper = new QueryWrapper<>();
        cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, pageReqDTO.getApplyType());
        if (StringUtils.isNotBlank(pageReqDTO.getCompanyId())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_COMPANY_ID, pageReqDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getFxCardId())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_FX_CARD_ID, pageReqDTO.getFxCardId());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyTransNo())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_TRANS_NO, pageReqDTO.getApplyTransNo());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyMeaningNo())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_MEANING_NO, pageReqDTO.getApplyMeaningNo());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyReason())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_REASON, pageReqDTO.getApplyReason());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getApplyTitle())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_APPLY_TITLE, pageReqDTO.getApplyTitle());
        }
        if (Objects.nonNull(pageReqDTO.getBeginTime()) && Objects.nonNull(pageReqDTO.getEndTime())) {
            cardCreditManagerPOQueryWrapper.between(CardCreditManagerPO.DB_COL_CREATE_TIME, pageReqDTO.getBeginTime(), pageReqDTO.getEndTime());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getOperationUserName())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_OPERATION_USER_NAME, pageReqDTO.getOperationUserName());
        }
        if (StringUtils.isNotBlank(pageReqDTO.getCostAttribution())) {
            cardCreditManagerPOQueryWrapper.like(CardCreditManagerPO.DB_COL_COST_ATTRIBUTION, pageReqDTO.getCostAttribution());
        }
        if (Objects.nonNull(pageReqDTO.getApplyStatus())) {
            cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS, pageReqDTO.getApplyStatus());
        }

        if (pageReqDTO.getAvalibleAmountFlag()) {
            cardCreditManagerPOQueryWrapper.gt(CardCreditManagerPO.DB_COL_AVALIBLE_AMOUNT, BigDecimal.ZERO);
        }
        cardCreditManagerPOQueryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        Page<CardCreditManagerDTO> cardCreditManagerDTOPage = super.findPage(cardCreditManagerPOQueryWrapper, current, size);
        return CardCreditManagerDO.me().transferCardCreditManagerPageResDTOPage(cardCreditManagerDTOPage);
    }

    public Boolean add(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        CardCreditManagerDO.me().checkCardCreditManagerAddReqDTO(cardCreditManagerAddReqDTO);

        CardCreditManagerDTO addCardCreditManagerDTO = CardCreditManagerDO.me().buildAddCardCreditManagerDTO(cardCreditManagerAddReqDTO);

        return super.saveDTO(addCardCreditManagerDTO);
    }

    public Boolean addAllColumn(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        CardCreditManagerDO.me().checkCardCreditManagerAddReqDTO(cardCreditManagerAddReqDTO);

        CardCreditManagerDTO addCardCreditManagerDTO = CardCreditManagerDO.me().buildAddCardCreditManagerDTO(cardCreditManagerAddReqDTO);

        return super.saveAllColumn(addCardCreditManagerDTO);
    }

    public Boolean addBatchAllColumn(final List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList) {
        CardCreditManagerDO.me().checkCardCreditManagerAddReqDTOList(cardCreditManagerAddReqDTOList);

        List<CardCreditManagerDTO> addBatchCardCreditManagerDTOList = CardCreditManagerDO.me().buildAddBatchCardCreditManagerDTOList(cardCreditManagerAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardCreditManagerDTOList);
    }

    public CardCreditManagerShowResDTO show(final String id) {
        CardCreditManagerDTO cardCreditManagerDTO = super.findById(id);

        return CardCreditManagerDO.me().transferCardCreditManagerShowResDTO(cardCreditManagerDTO);
    }

    public List<CardCreditManagerShowResDTO> showByIds(final List<String> ids) {
        CardCreditManagerDO.me().checkIds(ids);

        List<CardCreditManagerDTO> cardCreditManagerDTOList = super.findBatchIds(ids);

        return CardCreditManagerDO.me().transferCardCreditManagerShowResDTOList(cardCreditManagerDTOList);
    }

    public Boolean modify(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        CardCreditManagerDO.me().checkCardCreditManagerModifyReqDTO(cardCreditManagerModifyReqDTO);

        CardCreditManagerDTO modifyCardCreditManagerDTO = CardCreditManagerDO.me().buildModifyCardCreditManagerDTO(cardCreditManagerModifyReqDTO);

        return super.modifyById(modifyCardCreditManagerDTO);
    }

    public Boolean modifyAllColumn(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        CardCreditManagerDO.me().checkCardCreditManagerModifyReqDTO(cardCreditManagerModifyReqDTO);

        CardCreditManagerDTO modifyCardCreditManagerDTO = CardCreditManagerDO.me().buildModifyCardCreditManagerDTO(cardCreditManagerModifyReqDTO);

        return super.modifyAllColumnById(modifyCardCreditManagerDTO);
    }

    public Boolean removeByParams(final CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO) {
        CardCreditManagerDO.me().checkCardCreditManagerRemoveReqDTO(cardCreditManagerRemoveReqDTO);

        CardCreditManagerDTO removeCardCreditManagerDTO = CardCreditManagerDO.me().buildRemoveCardCreditManagerDTO(cardCreditManagerRemoveReqDTO);

        return super.remove(removeCardCreditManagerDTO);
    }

    @Override
    protected CardCreditManagerPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditManagerPO();
        }

        return BeanUtil.toBean(map, CardCreditManagerPO.class);
    }

    @Override
    protected CardCreditManagerDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditManagerDTO();
        }

        return BeanUtil.toBean(map, CardCreditManagerDTO.class);
    }

    public List<CardCreditManagerDTO> queryByApplyTransNos(String employeeId, List<String> applyTransNos) {
        if (CollectionUtils.isEmpty(applyTransNos)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.in(CardCreditManagerPO.DB_COL_APPLY_TRANS_NO, applyTransNos);
        queryWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, 0);
        queryWrapper.orderByAsc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        return findList(queryWrapper);
    }

    public CardCreditManagerDTO getById(final String id) {
        return super.findById(id);
    }


    /***
     * 修改核销金额    1：核销 2：回滚状态
     * @param id
     * @param checkedAmount
     * @param checkIngAmount
     * @param type
     * @return
     */
    public boolean updateCheckAmount(String id, BigDecimal checkedAmount, BigDecimal checkIngAmount, Integer type) {
        CardCreditManagerShowResDTO creditManagerShowResDTO = this.show(id);

        UpdateWrapper<CardCreditManagerPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardCreditManagerPO.DB_COL_ID, id);

        if (checkedAmount != null) {
            if (CheckTypeEnum.CHECK.getCode() == type) {
                updateWrapper.set(CardCreditManagerPO.DB_COL_WRITEN_OFF_AMOUNT, creditManagerShowResDTO.getWritenOffAmount().add(checkedAmount));
                updateWrapper.set(CardCreditManagerPO.DB_COL_WRITING_OFF_AMOUNT, creditManagerShowResDTO.getWritingOffAmount().subtract(checkedAmount));
                updateWrapper.gt(CardCreditManagerPO.DB_COL_WRITING_OFF_AMOUNT, checkedAmount);
            } else if (CheckTypeEnum.ROLLBACK.getCode() == type) {
                updateWrapper.set(CardCreditManagerPO.DB_COL_WRITEN_OFF_AMOUNT, creditManagerShowResDTO.getWritenOffAmount().subtract(checkedAmount));
                updateWrapper.set(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, creditManagerShowResDTO.getUncheckedAmount().add(checkedAmount));
                updateWrapper.gt(CardCreditManagerPO.DB_COL_WRITEN_OFF_AMOUNT, checkedAmount);
            }
        }
        if (checkIngAmount != null) {
            if (CheckTypeEnum.SUBMIT.getCode() == type) {
                updateWrapper.set(CardCreditManagerPO.DB_COL_WRITING_OFF_AMOUNT, creditManagerShowResDTO.getWritingOffAmount().add(checkIngAmount));
                updateWrapper.set(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, creditManagerShowResDTO.getUncheckedAmount().subtract(checkIngAmount));
                updateWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, checkIngAmount);
            } else if (CheckTypeEnum.REFUSE.getCode() == type) {
                updateWrapper.set(CardCreditManagerPO.DB_COL_WRITING_OFF_AMOUNT, creditManagerShowResDTO.getWritingOffAmount().subtract(checkIngAmount));
                updateWrapper.set(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, creditManagerShowResDTO.getUncheckedAmount().add(checkIngAmount));
                updateWrapper.gt(CardCreditManagerPO.DB_COL_WRITING_OFF_AMOUNT, checkIngAmount);
            }
        }
        return this.update(updateWrapper);
    }

    public Page<UserCardCreditGrantRecordDetail> queryAvailableRecord(final String fxCardId, final Integer current, final Integer size) {
        QueryWrapper<CardCreditManagerPO> cardCreditManagerPOQueryWrapper = new QueryWrapper<>();
        cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_FX_CARD_ID, fxCardId);
        cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        cardCreditManagerPOQueryWrapper.gt(CardCreditManagerPO.DB_COL_AVALIBLE_AMOUNT, BigDecimal.ZERO);
        cardCreditManagerPOQueryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS,1);
        Page<CardCreditManagerDTO> cardCreditManagerDTOPage = super.findPage(cardCreditManagerPOQueryWrapper, current, size);
        List<CardCreditManagerDTO> cardCreditManagerDTOS = cardCreditManagerDTOPage.getRecords();
        Page<UserCardCreditGrantRecordDetail> page = new Page<>();
//        CurrencyEnum currencyEnum = CurrencyEnum.USD;
        if (!CollectionUtils.isEmpty(cardCreditManagerDTOS)) {
            List<UserCardCreditGrantRecordDetail> list = new ArrayList<>();
            for (CardCreditManagerDTO cardCreditManagerDTO : cardCreditManagerDTOS) {
//                currencyEnum = CurrencyEnum.getCurrencyByCode(cardCreditManagerDTO.getCurrency());
                UserCardCreditGrantRecordDetail userCardCreditGrantRecordDetail = new UserCardCreditGrantRecordDetail();
                userCardCreditGrantRecordDetail.setApplyAmount(cardCreditManagerDTO.getAmount());
                userCardCreditGrantRecordDetail.setApplyAmountShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(cardCreditManagerDTO.getCardPlatform()).getCurrencyEnum(), BigDecimalUtils.fen2yuan(cardCreditManagerDTO.getAmount())));
                userCardCreditGrantRecordDetail.setApplyReason(cardCreditManagerDTO.getApplyReason());
                userCardCreditGrantRecordDetail.setApplyTitle(cardCreditManagerDTO.getApplyTitle());
                userCardCreditGrantRecordDetail.setApplyTime(DateFormatUtil.tradeDateFormat((cardCreditManagerDTO.getCreateTime())));
                userCardCreditGrantRecordDetail.setApplyTransNo(cardCreditManagerDTO.getApplyTransNo());
                userCardCreditGrantRecordDetail.setUseBalance(cardCreditManagerDTO.getUncheckedAmount());
                userCardCreditGrantRecordDetail.setApplyId(cardCreditManagerDTO.getBizNo());//IOS
                userCardCreditGrantRecordDetail.setUseBalanceShow(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(cardCreditManagerDTO.getCardPlatform()).getCurrencyEnum(), BigDecimalUtils.fen2yuan(cardCreditManagerDTO.getUncheckedAmount())));
                list.add(userCardCreditGrantRecordDetail);
            }

            page.setRecords(list);
            page.setTotal(list.size());
        }
        return page;
    }

    /**
     * 额度申请
     *
     * @param cardCreditApplyRpcReqDTO 额度申请请求
     * @return 额度申请结果
     */
//    @Transactional(rollbackFor = Exception.class)
    public CardCreditManagerApplyRespDTO apply(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO) {

        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardCreditApplyRpcReqDTO.getFxCardId());
        //检测参数是否符合条件
        checkApplyParam(cardCreditApplyRpcReqDTO,cardDTO);
        List<CardCreditManagerDTO>  existApply = cardCreditManagerService.queryExistedApply(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        if (!CollectionUtils.isEmpty(existApply) && existApply.size() > 0){
            CardCreditManagerApplyRespDTO cardCreditApplyRpcRespDTO = new CardCreditManagerApplyRespDTO();
            cardCreditApplyRpcRespDTO.setApplyTransNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
            cardCreditApplyRpcRespDTO.setCardBalance(cardDTO.getBalance());
            cardCreditApplyRpcRespDTO.setFxCardId(cardDTO.getFxCardId());
            cardCreditApplyRpcRespDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardCreditApplyRpcRespDTO.setCompanyId(cardDTO.getCompanyId());
            cardCreditApplyRpcRespDTO.setApplyStatus(Boolean.TRUE);
            return cardCreditApplyRpcRespDTO;
        }
        //确定渠道
        FxAcctChannelEnum fxAcctChannelEnum = buildFxAcctChannelEnum(cardCreditApplyRpcReqDTO.getBankName());

        //额度下发
        BigDecimal afterBalance = BigDecimal.ZERO;
        if (FxAcctChannelEnum.isLianLian(cardCreditApplyRpcReqDTO.getBankName())) {
            List<AcctOverseaRespDTO> companyAcctResList = acctOverseaCardService.queryCompanyOverseaAcctInfor(cardDTO.getCompanyId(),cardDTO.getCardPlatform(),cardDTO.getCardPlatform());
            boolean isBalanceEnough = checkLianLianBalance(companyAcctResList.get(0),cardCreditApplyRpcReqDTO.getApplyCreditAmount());
            String creditId = BizIdUtils.getFxCreditApplyId();
            //6. 额度发放成功: 无需扣预算(费控在审批通过后扣)
            EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
            //当前生效模式
            Integer cardModel = employeeModelConfigDTO.getActiveModel();
            if (!isBalanceEnough){
                //2. 额度申请单留存
                saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctResList.get(0).getAccountId(), creditId,0);
                cardCreditNoticeManager.sendNoticeMsgForFailed(cardCreditApplyRpcReqDTO.getFxCardId(),cardCreditApplyRpcReqDTO.getApplyCreditAmount(),cardCreditApplyRpcReqDTO.getSaasApplyNo());
            }else {
                //调用账户扣减余额
                callAccount(cardDTO,cardCreditApplyRpcReqDTO);
                //2. 额度申请单留存
                saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctResList.get(0).getAccountId(), creditId,1);
                afterBalance =  addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctResList.get(0).getAccountId());
                callLianLianCardUpdateBalance(companyAcctResList.get(0).getBankAcctId(),cardDTO.getBankCardId(),creditId,cardCreditApplyRpcReqDTO.getApplyCreditAmount());
                if(cardCreditApplyRpcReqDTO.getApplyOrderType().equals(ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal())){
                    cardCreditNoticeManager.sendNoticeMsgForSuccess(cardCreditApplyRpcReqDTO.getFxCardId(),cardCreditApplyRpcReqDTO.getApplyCreditAmount(),cardCreditApplyRpcReqDTO.getSaasApplyNo());
                }
            }
        } else {
            CompanyAcctRes companyAcctRes = fxPayAcctManager.queryAcct4Petty(cardDTO.getCompanyId(), fxAcctChannelEnum);

            ResponseVo<CompanyBusinessConfigDTO> companyBusinessConfigDTOResponseVo = null;
            try {
                // 先远程调用风控引擎，获取是否可以下发额度
                CommonConfigReq commonConfigReq = new CommonConfigReq();
                commonConfigReq.setCommonConfig(CommonConfigEnum.ACCT_GRANT_TYPE);
                commonConfigReq.setBizCode(companyAcctRes.getAccountId());

                companyBusinessConfigDTOResponseVo = iCompanyAcctService.queryCommonConfig(commonConfigReq);
            } catch (Exception e) {
                log.warn("调用风控系统异常, 获取是否可下发额度失败, 但不阻断正常下发额度.", e);
            }

            if (Func.isNotNull(companyBusinessConfigDTOResponseVo)) {
                CompanyBusinessConfigDTO companyBusinessConfigDTO = companyBusinessConfigDTOResponseVo.getData();
                if (Func.isNotNull(companyBusinessConfigDTO)) {
                    String bizValue = companyBusinessConfigDTO.getBizValue();
                    if (bizValue.equals(CommonConfigGrantEnum.ACCT_GRANT_OFF.getBizValue())) {
                        throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_COMPANY_ACCOUNT_GRANT_OFF);
                    }
                }
            }

            BigDecimal acctBalanceOfAfterTrade = companyAcctRes.getAvailableBalance().subtract(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
            //4. 调用企业余额扣减接口
            String creditId = BizIdUtils.getFxCreditApplyId();
            //6. 额度发放成功: 无需扣预算(费控在审批通过后扣)
            EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
            //当前生效模式
            Integer cardModel = employeeModelConfigDTO.getActiveModel();
            if (acctBalanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0) {
                saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId(), creditId,0);
                //余额不足发消息
                cardCreditNoticeManager.sendNoticeMsgForFailed(cardCreditApplyRpcReqDTO.getFxCardId(),cardCreditApplyRpcReqDTO.getApplyCreditAmount(),cardCreditApplyRpcReqDTO.getSaasApplyNo());
            }else {
                //2. 额度申请单留存
                boolean isSuccess = fxPayAcctManager.grantCredit(buildFundChangingReq(cardDTO, creditId, null, cardCreditApplyRpcReqDTO.getCurrency(), cardCreditApplyRpcReqDTO.getApplyCreditAmount()));
                if (isSuccess) {
                    saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId(), creditId, 1);
                    afterBalance = addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId());
                    if(cardCreditApplyRpcReqDTO.getApplyOrderType().equals(ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal())){
                        cardCreditNoticeManager.sendNoticeMsgForSuccess(cardCreditApplyRpcReqDTO.getFxCardId(),cardCreditApplyRpcReqDTO.getApplyCreditAmount(),cardCreditApplyRpcReqDTO.getSaasApplyNo());
                    }
                }

            }
        }
        CardCreditManagerApplyRespDTO cardCreditApplyRpcRespDTO = new CardCreditManagerApplyRespDTO();
        cardCreditApplyRpcRespDTO.setApplyTransNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        cardCreditApplyRpcRespDTO.setCardBalance(afterBalance);
        cardCreditApplyRpcRespDTO.setFxCardId(cardDTO.getFxCardId());
        cardCreditApplyRpcRespDTO.setEmployeeId(cardDTO.getEmployeeId());
        cardCreditApplyRpcRespDTO.setCompanyId(cardDTO.getCompanyId());
        cardCreditApplyRpcRespDTO.setApplyStatus(Boolean.TRUE);
        //发送额度下发完成消息
        cardCreditNoticeManager.sendNoticeMsgForVoucher(cardCreditApplyRpcReqDTO, cardDTO);
        return cardCreditApplyRpcRespDTO;
    }
    public void lianlianRetry(CardCreditManagerDTO cardCreditManagerDTO){
        CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO = new CardCreditManagerApplyReqDTO();
        cardCreditApplyRpcReqDTO.setFxCardId(cardCreditManagerDTO.getFxCardId());
        cardCreditApplyRpcReqDTO.setApplyCreditAmount(cardCreditManagerDTO.getAmount());
        cardCreditApplyRpcReqDTO.setSaasApplyNo(cardCreditManagerDTO.getApplyTransNo());
        //追加圈存的判断
        if (!cardCreditManagerDTO.getBizNo().equals(cardCreditManagerDTO.getOriApplyTransNo())){
            cardCreditApplyRpcReqDTO.setAppendFlag(Boolean.TRUE);
            cardCreditApplyRpcReqDTO.setApplyAppendBatchId(cardCreditManagerDTO.getApplyTransBatchNo());
        }
        cardCreditApplyRpcReqDTO.setCurrency(cardCreditManagerDTO.getCurrency());
        cardCreditApplyRpcReqDTO.setApplyReason(cardCreditManagerDTO.getApplyReason());

        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardCreditApplyRpcReqDTO.getFxCardId());
        List<AcctOverseaRespDTO> companyAcctResList = acctOverseaCardService.queryCompanyOverseaAcctInfor(cardDTO.getCompanyId(),cardDTO.getCardPlatform(),cardDTO.getCardPlatform());
        boolean isBalanceEnough = checkLianLianBalance(companyAcctResList.get(0),cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        if (!isBalanceEnough){
            return;
        }
        //6. 额度发放成功: 无需扣预算(费控在审批通过后扣)
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        //调用账户扣减余额
        callAccount(cardDTO,cardCreditApplyRpcReqDTO);
        BigDecimal afterBalance =  addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctResList.get(0).getAccountId());
//        String flowNo = cardCreditManagerDTO.getApplyTransNo();
        String creditId = BizIdUtils.getFxCreditApplyId();
        callLianLianCardUpdateBalance(companyAcctResList.get(0).getBankAcctId(),cardDTO.getBankCardId(),creditId,cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
        cardCreditManagerModifyReqDTO.setId(Long.valueOf(cardCreditManagerDTO.getId()));
        cardCreditManagerModifyReqDTO.setApplyStatus(1);
        cardCreditManagerService.modify(cardCreditManagerModifyReqDTO);
    }
    public void airwallexRetry(CardCreditManagerDTO cardCreditManagerDTO){
        CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO = new CardCreditManagerApplyReqDTO();
        cardCreditApplyRpcReqDTO.setFxCardId(cardCreditManagerDTO.getFxCardId());
        cardCreditApplyRpcReqDTO.setApplyCreditAmount(cardCreditManagerDTO.getAmount());
        cardCreditApplyRpcReqDTO.setSaasApplyNo(cardCreditManagerDTO.getApplyTransNo());
        //追加圈存的判断
        if (!cardCreditManagerDTO.getBizNo().equals(cardCreditManagerDTO.getOriApplyTransNo())){
            cardCreditApplyRpcReqDTO.setAppendFlag(Boolean.TRUE);
            cardCreditApplyRpcReqDTO.setApplyAppendBatchId(cardCreditManagerDTO.getApplyTransBatchNo());
        }
        cardCreditApplyRpcReqDTO.setCurrency(cardCreditManagerDTO.getCurrency());
        cardCreditApplyRpcReqDTO.setApplyReason(cardCreditManagerDTO.getApplyReason());

        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardCreditApplyRpcReqDTO.getFxCardId());
        CompanyAcctRes companyAcctRes = fxPayAcctManager.queryAcct4Petty(cardDTO.getCompanyId(), FxAcctChannelEnum.AIRWALLEX);

        ResponseVo<CompanyBusinessConfigDTO> companyBusinessConfigDTOResponseVo = null;
        try {
            // 先远程调用风控引擎，获取是否可以下发额度
            CommonConfigReq commonConfigReq = new CommonConfigReq();
            commonConfigReq.setCommonConfig(CommonConfigEnum.ACCT_GRANT_TYPE);
            commonConfigReq.setBizCode(companyAcctRes.getAccountId());

            companyBusinessConfigDTOResponseVo = iCompanyAcctService.queryCommonConfig(commonConfigReq);
        } catch (Exception e) {
            log.warn("调用风控系统异常, 获取是否可下发额度失败, 但不阻断正常下发额度.", e);
        }

        if (Func.isNotNull(companyBusinessConfigDTOResponseVo)) {
            CompanyBusinessConfigDTO companyBusinessConfigDTO = companyBusinessConfigDTOResponseVo.getData();
            if (Func.isNotNull(companyBusinessConfigDTO)) {
                String bizValue = companyBusinessConfigDTO.getBizValue();
                if (bizValue.equals(CommonConfigGrantEnum.ACCT_GRANT_OFF.getBizValue())) {
                    throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_COMPANY_ACCOUNT_GRANT_OFF);
                }
            }
        }

        BigDecimal acctBalanceOfAfterTrade = companyAcctRes.getAvailableBalance().subtract(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        if (acctBalanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0) {
            return;
        }
        //4. 调用企业余额扣减接口
        String creditId = cardCreditManagerDTO.getApplyTransNo();
        boolean isSuccess = fxPayAcctManager.grantCredit(buildFundChangingReq(cardDTO, creditId, null, cardCreditApplyRpcReqDTO.getCurrency(), cardCreditApplyRpcReqDTO.getApplyCreditAmount()));
        if (isSuccess) {
            //6. 额度发放成功: 无需扣预算(费控在审批通过后扣)
            //2. 额度申请单更新
            BigDecimal afterBalance = addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardCreditManagerDTO.getCardModel(), companyAcctRes.getAccountId());
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(cardCreditManagerDTO.getId()));
            cardCreditManagerModifyReqDTO.setApplyStatus(1);
            cardCreditManagerService.modify(cardCreditManagerModifyReqDTO);
        }

    }
    public void applyRetry(KafkaCompanyCardAcctChangeMsg kafkaCompanyCardAcctChangeMsg){
        //一周数据
        Date startDate = DateUtils.addDay(new Date(), -7);
        List<CardCreditManagerDTO> cardCreditManagerDTOS = cardCreditManagerService.queryFailedApply(kafkaCompanyCardAcctChangeMsg.getCompanyId(),kafkaCompanyCardAcctChangeMsg.getBankName(),startDate);
        if (CollectionUtils.isEmpty(cardCreditManagerDTOS)){
            return;
        }
        for (CardCreditManagerDTO cardCreditManagerDTO: cardCreditManagerDTOS) {
            if (CardPlatformEnum.isLianLian(cardCreditManagerDTO.getCardPlatform())){
                lianlianRetry(cardCreditManagerDTO);
            }
            if (CardPlatformEnum.isAirwallex(cardCreditManagerDTO.getCardPlatform())){
                airwallexRetry(cardCreditManagerDTO);
            }

        }

    }
    public void callLianLianCardUpdateBalance(String mchId,String bankCardId,String creditId,BigDecimal applyCreditAmount){
        //更新卡余额
        LianLianCardBalanceAdjustReqDTO lianLianCardBalanceAdjustReqDTO = new LianLianCardBalanceAdjustReqDTO();
        lianLianCardBalanceAdjustReqDTO.setMchId(mchId);
        lianLianCardBalanceAdjustReqDTO.setAccountNo(bankCardId);
        lianLianCardBalanceAdjustReqDTO.setOutOrderNo(creditId);
        lianLianCardBalanceAdjustReqDTO.setAdjustType("INCREASE");
        String applyAmount = BigDecimalUtils.fen2yuan(applyCreditAmount).toString();
        lianLianCardBalanceAdjustReqDTO.setAdjustAmount(applyAmount);
        lianLianCardBalanceAdjustReqDTO.setAdjustMemo("额度申请");
        LianLianCardBalanceAdjustRespDTO cardBalanceAdjustRespDTO = iLianLianCardService.balanceAdjust(lianLianCardBalanceAdjustReqDTO);
        if (!applyAmount.equals(cardBalanceAdjustRespDTO.getAmountBalanceAvailable())){
            FinhubLogger.error("连连卡余额调整不一致,请关注:"  + JSON.toJSONString(lianLianCardBalanceAdjustReqDTO) + ","+ JSON.toJSONString(cardBalanceAdjustRespDTO));
        }
    }
    public void callAccount(CardDTO cardDTO,CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO){
        OverseaCardApplyAmountReqDTO overseaCardApplyAmountReqDTO = new OverseaCardApplyAmountReqDTO();
        overseaCardApplyAmountReqDTO.setCompanyId(cardDTO.getCompanyId());
        overseaCardApplyAmountReqDTO.setBankName(cardDTO.getCardPlatform());
        overseaCardApplyAmountReqDTO.setOperationAmount(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        overseaCardApplyAmountReqDTO.setReceiveAccountName(cardDTO.getNameOnCard());
        overseaCardApplyAmountReqDTO.setReceiveAccountNo(cardDTO.getBankCardNo());
        overseaCardApplyAmountReqDTO.setBizNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        overseaCardApplyAmountReqDTO.setAccountSubType(FundAccountSubType.OVERSEA_ACCOUNT.getKey());
        overseaCardApplyAmountReqDTO.setTargetAccount(cardDTO.getBankCardNo());
        overseaCardApplyAmountReqDTO.setDirectAcctType(1);
        overseaCardApplyAmountReqDTO.setEmployeeId(cardDTO.getEmployeeId());
        overseaCardApplyAmountReqDTO.setOperationDescription("额度申请");
        overseaCardApplyAmountReqDTO.setCurrency(cardCreditApplyRpcReqDTO.getCurrency());
        overseaCardApplyAmountReqDTO.setApplyReason(cardCreditApplyRpcReqDTO.getApplyReason());
        overseaCardApplyAmountReqDTO.setAccountId(cardDTO.getCompanyAccountId());
        overseaCardApplyAmountReqDTO.setTargetAccountName(cardDTO.getNameOnCard());
        EmployeeSimpleDTO companyEmployee = irEmployeeService.getEmployeeSimpleDtoById(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        overseaCardApplyAmountReqDTO.setOperationUserId(cardDTO.getEmployeeId());
        overseaCardApplyAmountReqDTO.setOperationUserName(companyEmployee.getName());
        try {
            log.info("issueCredit req:{}",JsonUtils.toJson(overseaCardApplyAmountReqDTO));
            AccountSubOperationRespRPCDTO accountSubOperationRespRPCDTO = acctOverseaCardService.issueCredit(overseaCardApplyAmountReqDTO);
            log.info("issueCredit req:{},resp:{}",JsonUtils.toJson(overseaCardApplyAmountReqDTO),JsonUtils.toJson(accountSubOperationRespRPCDTO));
        }catch (Exception e){
            FinhubLogger.error("issueCredit error",e);
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_ERROR);
        }
    }
    public boolean checkLianLianBalance(AcctOverseaRespDTO acctOverseaRespDTO,BigDecimal applyCreditAmount){
        //4. 调用企业余额扣减接口
        BigDecimal acctBalanceOfAfterTrade = acctOverseaRespDTO.getBalance().subtract(applyCreditAmount);
        if (acctBalanceOfAfterTrade.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }
        return true;
    }
    public FxAcctChannelEnum buildFxAcctChannelEnum(String bankName){
        FxAcctChannelEnum fxAcctChannelEnum = FxAcctChannelEnum.AIRWALLEX;
        boolean isLianLian = FxAcctChannelEnum.isLianLian(bankName);
        if (isLianLian) {
            fxAcctChannelEnum = FxAcctChannelEnum.LIANLIAN;
        }
        return fxAcctChannelEnum;
    }
    /**
     * 2. 创建额度申请单
     */
    public void buildCreditApply(){

    }
    /**
     * 1.检查参数
     */
    public void checkApplyParam(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO,CardDTO cardDTO){
        //0. 预算占用(提交申请单时占用)
        //1. 申请验证
        //卡状态
        if (!Objects.equals(cardDTO.getCardStatus(), CardStatusEnum.ACTIVE.getStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_CARD_DISABLED);
        }
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        //备用金模式下不能有未核销金额
        if (Objects.equals(ActiveModelEnum.PETTY.getCode(), cardModel)) {
            List<CardCreditManagerDTO> cardCreditManagerDTOS = CardCreditManagerManager.me().queryUncheckApplyByEmployeeId(cardCreditApplyRpcReqDTO.getEmployeeId());
            if (!CollectionUtils.isEmpty(cardCreditManagerDTOS)) {
                throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_HAS_UNCHECKED);
            }
        }
        //备用金模式:卡上有钱非追加情况下不允许申请
        if (BigDecimalUtils.hasPrice(cardDTO.getBalance()) && Objects.equals(ActiveModelEnum.PETTY.getCode(), cardModel) && !cardCreditApplyRpcReqDTO.getAppendFlag()) {
            throw new FxCardException(GlobalCoreResponseCode.CREDIT_APPLY_REJECT_OF_HAS_BALANCE);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BigDecimal apply0LianLian(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO, CardDTO cardDTO, AcctOverseaRespDTO companyAcctRes, String creditId) {
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        //2. 额度申请单留存
        saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId(), creditId,1);
        return addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId());
    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public BigDecimal apply0(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO, CardDTO cardDTO, CompanyAcctRes companyAcctRes, String creditId) {
//        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
//        //当前生效模式
//        Integer cardModel = employeeModelConfigDTO.getActiveModel();
//        //2. 额度申请单留存
//        saveApply(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId(), creditId,1);
//        return addBalance(cardCreditApplyRpcReqDTO, cardDTO, cardModel, companyAcctRes.getAccountId());
//    }

    public CardCreditManagerReturnRespDTO refund(CardCreditManagerReturnReqDTO cardCreditReturnRpcReqDTO) {

        BigDecimal totalSubRefund = BigDecimal.ZERO;
        Map<String, BigDecimal> returnAmountMap = cardCreditReturnRpcReqDTO.getReturnAmount();
        for (String key : returnAmountMap.keySet()) {
            totalSubRefund = totalSubRefund.add(returnAmountMap.get(key));
        }
        FinhubLogger.info("额度退还:{}", JSONObject.toJSON(cardCreditReturnRpcReqDTO));
        //1. 额度退还
        CardDTO cardDTO = cardService.cardDetailByFxCardId(cardCreditReturnRpcReqDTO.getFxCardId());
        //补齐员工信息(登陆的操作人可以是企业)
        cardCreditReturnRpcReqDTO.setCompanyId(cardDTO.getCompanyId());
        cardCreditReturnRpcReqDTO.setEmployeeId(cardDTO.getEmployeeId());
        FxAcctChannelEnum fxAcctChannelEnum = FxAcctChannelEnum.AIRWALLEX;
        String cardPlatform = cardDTO.getCardPlatform();
        EmployeeModelConfigDTO employeeModelConfigDTO = cardModelConfigService.getEmployeeModelConfigByEmployeeId(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        //当前生效模式
        Integer cardModel = employeeModelConfigDTO.getActiveModel();
        String accountId = null;
        List<CardCreditManagerDTO> creditManagerDTOS = null;
        BigDecimal returnAmount = BigDecimal.ZERO;
        Map<String, String> applyIdMap = Maps.newHashMap();
        if (FxAcctChannelEnum.isLianLian(cardPlatform)) {
            LianLianCardDetailReqDTO lianLianCardDetailReqDTO = new LianLianCardDetailReqDTO();
            lianLianCardDetailReqDTO.setAccountNo(cardDTO.getBankCardId());
            lianLianCardDetailReqDTO.setMchId(cardDTO.getBankMchId());
            LianLianCardDetailRespDTO lianLianCardDetailRespDTO = iLianLianCardService.detail(lianLianCardDetailReqDTO);
            BigDecimal amountBalance = BigDecimalUtils.yuan2fen(new BigDecimal(lianLianCardDetailRespDTO.getAmtBalaval()));
            BigDecimal returnAmountTotal = cardCreditReturnRpcReqDTO.getReturnAmount().values().stream().map(BigDecimal::abs).reduce(BigDecimal.ZERO,BigDecimal::add);
            if (returnAmountTotal.compareTo(amountBalance) > 0){
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_BALANCE_NOT_ENOUGH);
            }
            fxAcctChannelEnum = FxAcctChannelEnum.LIANLIAN;
            List<AcctOverseaRespDTO> companyAcctResList = acctOverseaCardService.queryCompanyOverseaAcctInfor(cardDTO.getCompanyId(),cardDTO.getCardPlatform(),cardDTO.getCardPlatform());
            accountId = companyAcctResList.get(0).getAccountId();
            //4. 调用企业余额增加接口
            //6. 额度退回成功:预算恢复
            creditManagerDTOS = cardCreditManagerService.queryUncheckApply(cardDTO.getFxCardId());
            //调用企业退还并记录额度退回记录
            returnAmount = saveRefundApply(cardCreditReturnRpcReqDTO, cardDTO, cardModel, accountId, creditManagerDTOS,applyIdMap);
            try {
                String creditRefundId = BizIdUtils.getFxCreditRefundOrderId();
                LianLianCardBalanceAdjustReqDTO lianLianCardBalanceAdjustReqDTO = new LianLianCardBalanceAdjustReqDTO();
                lianLianCardBalanceAdjustReqDTO.setMchId(companyAcctResList.get(0).getBankAcctId());
                lianLianCardBalanceAdjustReqDTO.setAccountNo(cardDTO.getBankCardId());
                lianLianCardBalanceAdjustReqDTO.setOutOrderNo(creditRefundId);
                lianLianCardBalanceAdjustReqDTO.setAdjustType("REDUCE");
                String applyAmount = BigDecimalUtils.fen2yuan(cardCreditReturnRpcReqDTO.getRefundCreditAmount()).toString();
                lianLianCardBalanceAdjustReqDTO.setAdjustAmount(applyAmount);
                lianLianCardBalanceAdjustReqDTO.setAdjustMemo("额度退回");
                LianLianCardBalanceAdjustRespDTO cardBalanceAdjustRespDTO = iLianLianCardService.balanceAdjust(lianLianCardBalanceAdjustReqDTO);
                if (!applyAmount.equals(cardBalanceAdjustRespDTO.getAmountBalanceAvailable())){
                    FinhubLogger.error("连连卡余额调整不一致,请关注:"  + JSON.toJSONString(lianLianCardBalanceAdjustReqDTO) + ","+ JSON.toJSONString(cardBalanceAdjustRespDTO));
                }
            }catch (Exception e){
                FinhubLogger.error("包装异常",e);
            }

        }else {
            CompanyAcctRes companyAcctRes = fxPayAcctManager.queryAcct4Petty(cardDTO.getCompanyId(), fxAcctChannelEnum);
            accountId = companyAcctRes.getAccountId();
            //4. 调用企业余额增加接口
            //6. 额度退回成功:预算恢复
            creditManagerDTOS = cardCreditManagerService.queryUncheckApply(cardDTO.getFxCardId());
            //调用企业退还并记录额度退回记录
            returnAmount = saveRefundApply(cardCreditReturnRpcReqDTO, cardDTO, cardModel, accountId, creditManagerDTOS, applyIdMap);

        }
        //额度退回发送凭证消息入账
        cardCreditNoticeManager.sendNoticeMsgForVoucherRefund(cardCreditReturnRpcReqDTO, creditManagerDTOS,cardDTO, applyIdMap);
        //2. 关联记录
        relationApply(cardCreditReturnRpcReqDTO.getReturnAmount(), creditManagerDTOS);
        List<ApplyOrderReturnAmount> list = new ArrayList<>();
        for (String key : returnAmountMap.keySet()) {
            CardCreditManagerDTO cardCreditManagerDTO = creditManagerDTOS.stream().filter(
                i -> i.getBizNo().equals(key) && i.getApplyType() == 1
            ).findFirst().get();

            BigDecimal usdAmount = returnAmountMap.get(key);
            //没有汇率信息，需要报警
            if (BigDecimalUtils.hasPrice(cardCreditManagerDTO.getUsdCnyExchangeRate())) {
                BigDecimal cnyAmount = usdAmount.multiply(cardCreditManagerDTO.getUsdCnyExchangeRate());
                BigDecimal cnyAMountYuan = cnyAmount.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                ApplyOrderReturnAmount applyOrderReturnAmount = new ApplyOrderReturnAmount();
                applyOrderReturnAmount.setAmount(cnyAMountYuan);
                applyOrderReturnAmount.setApplyOrderId(key);
                list.add(applyOrderReturnAmount);
                toRefundSaasBudget(cardCreditReturnRpcReqDTO.getCompanyId(), cnyAmount, list, buildSaasBugetDto(cardCreditReturnRpcReqDTO, cardCreditManagerDTO));
            } else {
                log.error("申请单上没有汇率信息，请处理，applyTransNo={}", key);
            }
            CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
            cardCreditManagerModifyReqDTO.setId(Long.valueOf(cardCreditManagerDTO.getId()));
            cardCreditManagerModifyReqDTO.setReturnedAmount(cardCreditManagerDTO.getReturnedAmount().add(returnAmountMap.get(key)));
            cardCreditManagerModifyReqDTO.setAvalibleAmount(cardCreditManagerDTO.getAvalibleAmount().subtract(returnAmountMap.get(key)));
            cardCreditManagerModifyReqDTO.setUncheckedAmount(cardCreditManagerDTO.getUncheckedAmount().subtract(returnAmountMap.get(key)));
            cardCreditManagerService.modify(cardCreditManagerModifyReqDTO);
        }
        CardCreditManagerReturnRespDTO cardCreditManagerReturnRespDTO = new CardCreditManagerReturnRespDTO();
        cardCreditManagerReturnRespDTO.setCardBalance(cardDTO.getBalance().subtract(returnAmount));
        cardCreditManagerReturnRespDTO.setCardStatus(cardDTO.getCardStatus());
        cardCreditManagerReturnRespDTO.setEmployeeId(cardDTO.getEmployeeId());
        cardCreditManagerReturnRespDTO.setRefundAmount(returnAmount);
//        cardCreditManagerReturnRespDTO.setApplyTransNo(cardCreditReturnRpcReqDTO.ge);
        cardCreditManagerReturnRespDTO.setCompanyId(cardCreditReturnRpcReqDTO.getCompanyId());
        cardCreditManagerReturnRespDTO.setFxCardId(cardCreditReturnRpcReqDTO.getEmployeeId());
        return cardCreditManagerReturnRespDTO;
    }

    public void relationApply(Map<String, BigDecimal> returnAmountMap, List<CardCreditManagerDTO> creditManagerDTOS) {
        for (String key : returnAmountMap.keySet()) {
            CardCreditManagerDTO creditManagerDTO = creditManagerDTOS.stream()
                .filter(i -> i.getBizNo().equals(key) && Objects.equals(i.getApplyType(), CreditApplyTypeEnum.APPLY.getCode()))
                .findFirst().orElse(null);
            if (creditManagerDTO != null) {
                CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO = new CardCreditManagerRelationAddReqDTO();
                cardCreditManagerRelationAddReqDTO.setFxCardId(creditManagerDTO.getFxCardId());
                cardCreditManagerRelationAddReqDTO.setCompanyId(creditManagerDTO.getCompanyId());
                cardCreditManagerRelationAddReqDTO.setEmployeeId(creditManagerDTO.getEmployeeId());
                cardCreditManagerRelationAddReqDTO.setRecordId(creditManagerDTO.getBizNo());
                cardCreditManagerRelationAddReqDTO.setApplyTransNo(creditManagerDTO.getApplyTransNo());
                cardCreditManagerRelationAddReqDTO.setApplyTitle(creditManagerDTO.getApplyTitle());
                cardCreditManagerRelationAddReqDTO.setApplyAmount(returnAmountMap.get(key));
                cardCreditManagerRelationAddReqDTO.setUncheckedAmount(creditManagerDTO.getUncheckedAmount());
                cardCreditManagerRelationAddReqDTO.setApplyTime(new Date());
                cardCreditManagerRelationAddReqDTO.setRelationAmount(returnAmountMap.get(key));
                cardCreditManagerRelationAddReqDTO.setCreateTime(new Date());
                cardCreditManagerRelationAddReqDTO.setUpdateTime(new Date());
                CardCreditManagerRelationService.me().add(cardCreditManagerRelationAddReqDTO);
            }
        }
    }

    public static SaasBugetDto buildSaasBugetDto(CardCreditManagerReturnReqDTO cardCreditReturnRpcReqDTO, CardCreditManagerDTO cardCreditManagerDTO) {

        List<BudgetCostAttributionDTO> budgetCostAttributionDTO = JSON.parseArray(cardCreditManagerDTO.getCostAttribution(), BudgetCostAttributionDTO.class);

        return SaasBugetDto.builder()
            .companyId(cardCreditReturnRpcReqDTO.getCompanyId())
            .employeeId(cardCreditReturnRpcReqDTO.getEmployeeId())
            .orderAmount(cardCreditReturnRpcReqDTO.getReturnAmount().get(cardCreditManagerDTO.getBizNo()))
            .bizNo(cardCreditManagerDTO.getBizNo())
            .orderType(CategoryTypeEnum.BANK_INDIVIDUAL.getCode())
            .costAttributionCategory(budgetCostAttributionDTO.get(0).getCost_attribution_category())
            .costAttributionId(budgetCostAttributionDTO.get(0).getCost_attribution_id())
            .costAttributionTime(com.luastar.swift.base.utils.DateUtils.format(new Date()))
            .costAttributionScope(budgetCostAttributionDTO.get(0).getCost_attribution_category())
            .budgetCostAttrType(budgetCostAttributionDTO.get(0).getCost_attribution_category())
            .costAttributionList(budgetCostAttributionDTO)
//            .pettyId(bankRefundTradeReqDTO.getPettyId())
            .transNo(cardCreditManagerDTO.getApplyTransNo())
            .bankApplyCreditType(2)
            .build();
    }

    public BigDecimal saveRefundApply(CardCreditManagerReturnReqDTO cardCreditReturnRpcReqDTO, CardDTO cardDTO, Integer cardModel, String companyAccountId, List<CardCreditManagerDTO> creditManagerDTOS, Map<String, String> applyIdMap) {
        boolean isLianLian = FxAcctChannelEnum.isLianLian(cardDTO.getCardPlatform());
        Map<String, BigDecimal> returnAmount = cardCreditReturnRpcReqDTO.getReturnAmount();
        BigDecimal totalAmount = new BigDecimal("0");
        for (String key : returnAmount.keySet()) {
            CardCreditManagerDTO cardCreditManagerDTO = creditManagerDTOS.stream().filter(
                i -> i.getBizNo().equals(key) && Objects.equals(i.getApplyType(), CreditApplyTypeEnum.APPLY.getCode())
            ).findFirst().get();
            String creditRefundId = BizIdUtils.getFxCreditRefundOrderId();
            applyIdMap.put(key, creditRefundId);
            if (isLianLian) {
                OverseaAcctRefundAmountReqDTO overseaAcctRefundAmountReqDTO = new OverseaAcctRefundAmountReqDTO();
                overseaAcctRefundAmountReqDTO.setCompanyId(cardDTO.getCompanyId());
                overseaAcctRefundAmountReqDTO.setBankName(cardDTO.getCardPlatform());
                overseaAcctRefundAmountReqDTO.setOperationAmount(returnAmount.get(key));
                overseaAcctRefundAmountReqDTO.setReceiveAccountName(cardDTO.getNameOnCard());
                overseaAcctRefundAmountReqDTO.setReceiveAccountNo(cardDTO.getBankCardNo());
                overseaAcctRefundAmountReqDTO.setBizNo(creditRefundId);
                overseaAcctRefundAmountReqDTO.setFundPlatform(FundPlatformEnum.LIANLIAN.getKey());
                overseaAcctRefundAmountReqDTO.setAccountSubType(FundAccountSubType.OVERSEA_ACCOUNT.getKey());
                overseaAcctRefundAmountReqDTO.setTargetAccount(cardDTO.getBankCardNo());
                overseaAcctRefundAmountReqDTO.setDirectAcctType(1);
                overseaAcctRefundAmountReqDTO.setOperationDescription("额度退回");
                overseaAcctRefundAmountReqDTO.setApplyReason(cardCreditReturnRpcReqDTO.getApplyReason());
//            overseaCardApplyAmountReqDTO.setAccountModel();
                overseaAcctRefundAmountReqDTO.setAccountId(cardDTO.getCompanyAccountId());
                overseaAcctRefundAmountReqDTO.setTargetAccountName(cardDTO.getNameOnCard());
                FinhubLogger.info("recoverCredit:{}",JsonUtils.toJson(overseaAcctRefundAmountReqDTO));
                EmployeeSimpleDTO companyEmployee = irEmployeeService.getEmployeeSimpleDtoById(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
                overseaAcctRefundAmountReqDTO.setOperationUserId(cardDTO.getEmployeeId());
                overseaAcctRefundAmountReqDTO.setOperationUserName(companyEmployee.getName());
                acctOverseaCardService.recoverCredit(overseaAcctRefundAmountReqDTO);
            } else {
                fxPayAcctManager.recoverCredit(buildFundChangingReq(cardDTO, creditRefundId, cardCreditManagerDTO.getApplyTransNo(), CurrencyEnum.USD.getCurrencyCode(), returnAmount.get(key)));
            }
            CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO = new CardCreditManagerAddReqDTO();
            cardCreditManagerAddReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardCreditManagerAddReqDTO.setBankCardId(cardDTO.getBankCardId());
            cardCreditManagerAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
            cardCreditManagerAddReqDTO.setCardPlatform(cardDTO.getCardPlatform());
            cardCreditManagerAddReqDTO.setApplyStatus(CreditApplyStatusEnum.SUCCESS.getCode());
            cardCreditManagerAddReqDTO.setAmount(returnAmount.get(key));
            cardCreditManagerAddReqDTO.setApplyReason(cardCreditReturnRpcReqDTO.getApplyReason());
            cardCreditManagerAddReqDTO.setApplyTitle(cardCreditReturnRpcReqDTO.getApplyReasonDesc());
            cardCreditManagerAddReqDTO.setApplyType(CreditApplyTypeEnum.RETURN.getCode());
            cardCreditManagerAddReqDTO.setBizNo(key);
            cardCreditManagerAddReqDTO.setApplyTransNo(creditRefundId);
            cardCreditManagerAddReqDTO.setOriApplyTransNo(cardCreditManagerDTO.getApplyTransNo());
            cardCreditManagerAddReqDTO.setReturnedAmount(returnAmount.get(key));
            cardCreditManagerAddReqDTO.setUnwriteOffAmount(BigDecimal.ZERO);
            cardCreditManagerAddReqDTO.setUncheckedAmount(BigDecimal.ZERO);
            cardCreditManagerAddReqDTO.setCardModel(cardModel);
            cardCreditManagerAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
            cardCreditManagerAddReqDTO.setCompanyId(cardDTO.getCompanyId());
            cardCreditManagerAddReqDTO.setCompanyAccountId(companyAccountId);
            cardCreditManagerAddReqDTO.setOperationUserId(cardDTO.getEmployeeId());
            cardCreditManagerAddReqDTO.setOperationUserDept("--");
            cardCreditManagerAddReqDTO.setCurrency(cardDTO.getCurrency());
            cardCreditManagerService.add(cardCreditManagerAddReqDTO);
            totalAmount = totalAmount.add(returnAmount.get(key));
            //5. 卡余额增加记录流水
            cardDTO = cardService.cardDetailByFxCardId(cardCreditReturnRpcReqDTO.getFxCardId());
            reduceBalance(creditRefundId, returnAmount.get(key), cardDTO, cardModel);
        }
        return totalAmount;
    }

    public void toRefundSaasBudget(String companyId, BigDecimal amount, List<ApplyOrderReturnAmount> list, SaasBugetDto saasBugetDto) {
        VirtualCardControlConf config = iVirtualCardBudgetService.queryControlConf(companyId);
        //获取当前新老预算模式
        boolean newBudgetFlag = config.getBudgetNewSwitch().equals(NumberUtils.INTEGER_ONE);
        FinhubLogger.info("当前公司编号：{},预算模式,新预算：{}", companyId, newBudgetFlag);
        if (newBudgetFlag) {
            toRefundSaasBudgetNew(companyId, amount, list);
        } else {
            toRefundSaasBudgetOld(saasBugetDto);
        }
    }

    public void toRefundSaasBudgetOld(SaasBugetDto saasBugetDto) {
        try {
            saasBugetDto.setOrderAmount(BigDecimalUtils.fen2yuan(saasBugetDto.getOrderAmount()).negate());
            saasBudgetManager.saasBudget(saasBugetDto);
        } catch (Exception e) {
            FinhubLogger.error("老预算额退还预算异常：参数{}=={}", JsonUtils.toJson(saasBugetDto), e);
        }
    }

    public void toRefundSaasBudgetNew(String companyId, BigDecimal amount, List<ApplyOrderReturnAmount> list) {
        FinhubLogger.info("新预算模式returnAmount()--companyId:{},amount:{},applyOrderReturnAmountList:{}",
            companyId, amount, JSON.toJSONString(list));
        try {
            iVirtualCardBudgetService.returnAmount(companyId, amount, list);
        } catch (Exception e) {
            FinhubLogger.error("新预算模式异常", e);
        }

    }

    public FundChangingReq buildFundChangingReq(CardDTO cardDTO, String orderId, String oriOrderId, String currency, BigDecimal applyAmount) {
        FundChangingReq fundChangingReq = new FundChangingReq();
        fundChangingReq.setChannel(FxAcctChannelEnum.AIRWALLEX.getChannel());
        fundChangingReq.setAmount(applyAmount.abs());
        fundChangingReq.setCurrency(currency);
        fundChangingReq.setCompanyId(cardDTO.getCompanyId());
        fundChangingReq.setBizNo(orderId);
        fundChangingReq.setOriBizNo(oriOrderId);
        UserComInfoVO userComInfoVO = UserAuthHolder.getCurrentUser();
        if (userComInfoVO != null) {
            fundChangingReq.setOperatorId(userComInfoVO.getUser_id());
            fundChangingReq.setOperatorName(userComInfoVO.getUser_name());
            if (userComInfoVO.getUser_id().equals(cardDTO.getEmployeeId())) {
                fundChangingReq.setOperatorRole(OperatorRoleEnum.COMPANY_EMPLOEE);
            } else {
                fundChangingReq.setOperatorRole(OperatorRoleEnum.PLATFORM_SYSTEM);
            }
        } else {
            fundChangingReq.setOperatorId(cardDTO.getEmployeeId());
            fundChangingReq.setOperatorName(cardDTO.getNameOnCard());
            fundChangingReq.setOperatorRole(OperatorRoleEnum.COMPANY_EMPLOEE);
        }
        fundChangingReq.setTargetAcctBank(cardDTO.getCardPlatform());
        fundChangingReq.setTargetAcctName(cardDTO.getNameOnCard());
        fundChangingReq.setTargetAcctNo(cardDTO.getBankCardNo());
        return fundChangingReq;
    }

    public void reduceBalance(String bizNo, BigDecimal returnAmount, CardDTO cardDTO, Integer cardModel) {
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(cardDTO.getId());
        BigDecimal afterBalance = cardDTO.getBalance().subtract(returnAmount);
        cardModifyReqDTO.setBalance(afterBalance);
        cardService.modify(cardModifyReqDTO);
        addFlow(bizNo, CardOperationTypeEnum.REFUND.getCode(), returnAmount, afterBalance, cardDTO, cardModel);
    }

    public void reduceUncheckedAmount(String fxCardId, BigDecimal wrongPaidAmount) {
        List<CardCreditManagerDTO> creditManagerDTOS = queryUncheckApply4WrongPaid(fxCardId, wrongPaidAmount);
        if (CollectionUtils.isEmpty(creditManagerDTOS)) {
            return;
        }
        BigDecimal uncheckedAmount = creditManagerDTOS.get(0).getUncheckedAmount().subtract(wrongPaidAmount);
        BigDecimal unwriteOffAmount = creditManagerDTOS.get(0).getUnwriteOffAmount().add(wrongPaidAmount);
        CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = new CardCreditManagerModifyReqDTO();
        cardCreditManagerModifyReqDTO.setId(Long.valueOf(creditManagerDTOS.get(0).getId()));
//        cardCreditManagerModifyReqDTO.setAvalibleAmount(unwriteOffAmount);
        cardCreditManagerModifyReqDTO.setUncheckedAmount(uncheckedAmount);
        cardCreditManagerModifyReqDTO.setUnwriteOffAmount(unwriteOffAmount);
        cardCreditManagerService.modify(cardCreditManagerModifyReqDTO);
    }

    public BankCardFlowAddReqDTO addFlow(String bizNo, Integer operationType, BigDecimal operationAmount, BigDecimal afterBalance, CardDTO cardShowResDTO, Integer cardModel) {
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = new BankCardFlowAddReqDTO();
        bankCardFlowAddReqDTO.setId(IdUtils.getId());
        bankCardFlowAddReqDTO.setFxCardId(cardShowResDTO.getFxCardId());
        bankCardFlowAddReqDTO.setEmployeeId(cardShowResDTO.getEmployeeId());
        bankCardFlowAddReqDTO.setCompanyId(cardShowResDTO.getCompanyId());
        bankCardFlowAddReqDTO.setBizNo(bizNo);
        bankCardFlowAddReqDTO.setOperationType(operationType);
        bankCardFlowAddReqDTO.setCardModel(cardModel);
        bankCardFlowAddReqDTO.setCurrentAmount(cardShowResDTO.getBalance());
        bankCardFlowAddReqDTO.setOperationAmount(operationAmount);
        bankCardFlowAddReqDTO.setBalance(afterBalance);
        bankCardFlowAddReqDTO.setCreateTime(new Date());
        bankCardFlowAddReqDTO.setUpdateTime(new Date());
        bankCardFlowService.add(bankCardFlowAddReqDTO);
        return bankCardFlowAddReqDTO;
    }

    public void saveApply(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO, CardDTO cardDTO, Integer cardModel, String companyAccountId, String creditId,Integer applyStatus) {
        CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO = new CardCreditManagerAddReqDTO();
        cardCreditManagerAddReqDTO.setFxCardId(cardCreditApplyRpcReqDTO.getFxCardId());
        cardCreditManagerAddReqDTO.setBankCardId(cardDTO.getBankCardId());
        cardCreditManagerAddReqDTO.setBankCardNo(cardDTO.getBankCardNo());
        cardCreditManagerAddReqDTO.setCardPlatform(cardDTO.getCardPlatform());
        cardCreditManagerAddReqDTO.setApplyStatus(applyStatus);
        cardCreditManagerAddReqDTO.setAmount(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        cardCreditManagerAddReqDTO.setApplyTitle(cardCreditApplyRpcReqDTO.getPettyName());
        cardCreditManagerAddReqDTO.setApplyReason(cardCreditApplyRpcReqDTO.getApplyReason());
        cardCreditManagerAddReqDTO.setApplyReasonDesc(cardCreditApplyRpcReqDTO.getApplyReasonDesc());
        cardCreditManagerAddReqDTO.setApplyTransBatchNo(cardCreditApplyRpcReqDTO.getApplyAppendBatchId());
        cardCreditManagerAddReqDTO.setApplyType(1);
        if (cardCreditApplyRpcReqDTO.getAppendFlag()) {
            //如果是追加申请,新单
            cardCreditManagerAddReqDTO.setBizNo(cardCreditApplyRpcReqDTO.getApplyAppendBatchId());
        } else {
            cardCreditManagerAddReqDTO.setBizNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        }
        //原始申请单
        cardCreditManagerAddReqDTO.setOriApplyTransNo(cardCreditApplyRpcReqDTO.getSaasApplyNo());
        cardCreditManagerAddReqDTO.setApplyMeaningNo(cardCreditApplyRpcReqDTO.getSaasApplyMeaningNo());
        cardCreditManagerAddReqDTO.setApplyTransNo(creditId);//业务主键
        cardCreditManagerAddReqDTO.setReturnedAmount(BigDecimal.ZERO);
        cardCreditManagerAddReqDTO.setUnwriteOffAmount(BigDecimal.ZERO);
        cardCreditManagerAddReqDTO.setUncheckedAmount(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        cardCreditManagerAddReqDTO.setAvalibleAmount(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        cardCreditManagerAddReqDTO.setCostType(String.valueOf(cardCreditApplyRpcReqDTO.getCostAttributionType()));
        cardCreditManagerAddReqDTO.setCostTypeName(cardCreditApplyRpcReqDTO.getCostAttributionName());
        cardCreditManagerAddReqDTO.setCostAttribution(JsonUtils.toJson(cardCreditApplyRpcReqDTO.getAttributions()));
        cardCreditManagerAddReqDTO.setCardModel(cardModel);
        cardCreditManagerAddReqDTO.setEmployeeId(cardDTO.getEmployeeId());
        cardCreditManagerAddReqDTO.setCompanyId(cardDTO.getCompanyId());
        cardCreditManagerAddReqDTO.setCompanyAccountId(companyAccountId);
        cardCreditManagerAddReqDTO.setOperationUserId(cardDTO.getEmployeeId());
        cardCreditManagerAddReqDTO.setApplyOrderType(cardCreditApplyRpcReqDTO.getApplyOrderType());

        EmployeeSimpleDTO companyEmployee = irEmployeeService.getEmployeeSimpleDtoById(cardDTO.getCompanyId(), cardDTO.getEmployeeId());
        cardCreditManagerAddReqDTO.setOperationUserDept(companyEmployee.getOrgUnitName());
        cardCreditManagerAddReqDTO.setOperationUserName(companyEmployee.getName());
        CardPlatformCaseEnum enumByFxAcctChannel = CardPlatformCaseEnum.getEnumByFxAcctChannel(cardDTO.getCardPlatform());
        cardCreditManagerAddReqDTO.setCurrency(StringUtils.isNotBlank(cardCreditApplyRpcReqDTO.getCurrency())?cardCreditApplyRpcReqDTO.getCurrency():enumByFxAcctChannel.getCurrencyEnum().getCurrencyCode());
        cardCreditManagerAddReqDTO.setUsdCnyExchangeRate(cardCreditApplyRpcReqDTO.getUsdCnyExchangeRate());
        cardCreditManagerService.add(cardCreditManagerAddReqDTO);
    }

    public BigDecimal addBalance(CardCreditManagerApplyReqDTO cardCreditApplyRpcReqDTO, CardDTO cardDTO, Integer cardModel, String companyAccountId) {
        CardModifyReqDTO cardModifyReqDTO = new CardModifyReqDTO();
        cardModifyReqDTO.setId(cardDTO.getId());
        BigDecimal afterBalance = cardDTO.getBalance().add(cardCreditApplyRpcReqDTO.getApplyCreditAmount());
        cardModifyReqDTO.setBalance(afterBalance);
        cardService.modify(cardModifyReqDTO);
        String bizNo = cardCreditApplyRpcReqDTO.getSaasApplyNo();
        if (cardCreditApplyRpcReqDTO.getAppendFlag()) {
            //如果是追加申请,新单
            bizNo = cardCreditApplyRpcReqDTO.getApplyAppendBatchId();
        }
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = addFlow(bizNo, CardOperationTypeEnum.APPLY.getCode(), cardCreditApplyRpcReqDTO.getApplyCreditAmount(), afterBalance, cardDTO, cardModel);
        KafkaProducer.me().sendCardApplyMsg(bankCardFlowAddReqDTO.getCompanyId(), bankCardFlowAddReqDTO.getEmployeeId(), bankCardFlowAddReqDTO.getFxCardId(), bankCardFlowAddReqDTO.getOperationAmount(), companyAccountId);
        return afterBalance;
    }

    public List<CardCreditManagerDTO> queryUncheckApply(String fxCardId) {
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, 0);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS,1);
        queryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        return findList(queryWrapper);
    }

    public List<CardCreditManagerDTO> queryUncheckApply4WrongPaid(String fxCardId, BigDecimal wrongPaidAmount) {
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, wrongPaidAmount);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS,1);
        queryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        queryWrapper.last("limit 1");
        return findList(queryWrapper);
    }


    public List<CardCreditManagerDTO> queryUncheckApplyByEmployeeId(String employeeId) {
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, 0);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS,1);
        queryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        return findList(queryWrapper);
    }
    public List<CardCreditManagerDTO> queryUncheckApplyByEmployeeIdAndPlat(String employeeId,String cardPlatformCode) {
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_CARD_PLATFORM, cardPlatformCode);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.gt(CardCreditManagerPO.DB_COL_UNCHECKED_AMOUNT, 0);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS,1);
        queryWrapper.orderByDesc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        return findList(queryWrapper);
    }

    public List<CardCreditManagerDTO> queryFailedApply(String companyId, String bankName,Date startDate) {
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_CARD_PLATFORM, bankName);
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_TYPE, CreditApplyTypeEnum.APPLY.getCode());
        queryWrapper.eq(CardCreditManagerPO.DB_COL_APPLY_STATUS, 0);
        queryWrapper.gt(CardCreditManagerPO.DB_COL_CREATE_TIME,startDate);
        queryWrapper.orderByAsc(CardCreditManagerPO.DB_COL_CREATE_TIME);
        return findList(queryWrapper);
    }

    public List<CardCreditManagerDTO> queryExistedApply(String bizNo){
        QueryWrapper<CardCreditManagerPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerPO.DB_COL_BIZ_NO,bizNo);
        return findList(queryWrapper);
    }


}
