package com.fenbei.fx.card.service.cardcreditmanagerrelation.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardcreditmanagerrelation.po.CardCreditManagerRelationPO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.CardCreditManagerRelationService;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationShowResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.manager.CardCreditManagerRelationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 关联申请单记录 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Slf4j
@Service
public class CardCreditManagerRelationServiceImpl extends BaseServiceImpl<CardCreditManagerRelationManager, CardCreditManagerRelationPO, CardCreditManagerRelationDTO> implements CardCreditManagerRelationService {

    @Override
    public List<CardCreditManagerRelationListResDTO> list(final CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO) {
        return manager.list(cardCreditManagerRelationListReqDTO);
    }

    @Override
    public CardCreditManagerRelationListResDTO listOne(final CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO) {
        return manager.listOne(cardCreditManagerRelationListReqDTO);
    }

    @Override
    public Page<CardCreditManagerRelationPageResDTO> pagination(final CardCreditManagerRelationPageReqDTO cardCreditManagerRelationPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardCreditManagerRelationPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        return manager.add(cardCreditManagerRelationAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        return manager.addAllColumn(cardCreditManagerRelationAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList) {
        return manager.addBatchAllColumn(cardCreditManagerRelationAddReqDTOList);
    }

    @Override
    public CardCreditManagerRelationShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardCreditManagerRelationShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        return manager.modify(cardCreditManagerRelationModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        return manager.modifyAllColumn(cardCreditManagerRelationModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO) {
        return manager.removeByParams(cardCreditManagerRelationRemoveReqDTO);
    }
}
