package com.fenbei.fx.card.service.bankcardflow.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.bankcardflow.po.BankCardFlowPO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface BankCardFlowConverter extends BaseConverter<BankCardFlowDTO, BankCardFlowPO> {

    static BankCardFlowConverter me() {
        return SpringUtil.getBean(BankCardFlowConverter.class);
    }

    BankCardFlowDTO convertToBankCardFlowDTO(BankCardFlowAddReqDTO bankCardFlowAddReqDTO);

    BankCardFlowDTO convertToBankCardFlowDTO(BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO);

    BankCardFlowDTO convertToBankCardFlowDTO(BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO);

    BankCardFlowDTO convertToBankCardFlowDTO(BankCardFlowListReqDTO bankCardFlowListReqDTO);

    BankCardFlowDTO convertToBankCardFlowDTO(BankCardFlowPageReqDTO bankCardFlowPageReqDTO);

    BankCardFlowShowResDTO convertToBankCardFlowShowResDTO(BankCardFlowDTO bankCardFlowDTO);

    List<BankCardFlowShowResDTO> convertToBankCardFlowShowResDTOList(List<BankCardFlowDTO> bankCardFlowDTOList);

    BankCardFlowListResDTO convertToBankCardFlowListResDTO(BankCardFlowDTO bankCardFlowDTO);

    List<BankCardFlowListResDTO> convertToBankCardFlowListResDTOList(List<BankCardFlowDTO> bankCardFlowDTOList);

    List<BankCardFlowDTO> convertToBankCardFlowDTOList(List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList);

    BankCardFlowPageResDTO convertToBankCardFlowPageResDTO(BankCardFlowDTO bankCardFlowDTO);

    List<BankCardFlowPageResDTO> convertToBankCardFlowPageResDTOList(List<BankCardFlowDTO> bankCardFlowDTOList);

    Page<BankCardFlowPageResDTO> convertToBankCardFlowPageResDTOPage(Page<BankCardFlowDTO> bankCardFlowDTOPage);
}
