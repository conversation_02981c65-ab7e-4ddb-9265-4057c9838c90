package com.fenbei.fx.card.service.cardmail.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailAddReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailListResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailModifyReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailRemoveReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailShowResDTO;
import com.fenbei.fx.card.service.cardmail.converter.CardMailConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 实体卡邮寄管理表 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Slf4j
@Component
public class CardMailDO extends BaseDO<CardMailDTO, CardMailPO, CardMailConverter> {

    public static CardMailDO me() {
        return SpringUtil.getBean(CardMailDO.class);
    }

    public void checkCardMailAddReqDTO(final CardMailAddReqDTO cardMailAddReqDTO) {
        if (Func.isEmpty(cardMailAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardMailAddReqDTOList(final List<CardMailAddReqDTO> cardMailAddReqDTOList) {
        if (Func.isEmpty(cardMailAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardMailModifyReqDTO(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        if (Func.isEmpty(cardMailModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardMailRemoveReqDTO(final CardMailRemoveReqDTO cardMailRemoveReqDTO) {
        if (Func.isEmpty(cardMailRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardMailDTO buildListParamsDTO(final CardMailListReqDTO cardMailListReqDTO) {
        return converter.convertToCardMailDTO(cardMailListReqDTO);
    }

    public CardMailDTO buildPageParamsDTO(final CardMailPageReqDTO cardMailPageReqDTO) {
        return converter.convertToCardMailDTO(cardMailPageReqDTO);
    }

    public CardMailDTO buildAddCardMailDTO(final CardMailAddReqDTO cardMailAddReqDTO) {
        return converter.convertToCardMailDTO(cardMailAddReqDTO);
    }

    public List<CardMailDTO> buildAddBatchCardMailDTOList(final List<CardMailAddReqDTO> cardMailAddReqDTOList) {
        return converter.convertToCardMailDTOList(cardMailAddReqDTOList);
    }

    public CardMailDTO buildModifyCardMailDTO(final CardMailModifyReqDTO cardMailModifyReqDTO) {
        return converter.convertToCardMailDTO(cardMailModifyReqDTO);
    }

    public CardMailDTO buildRemoveCardMailDTO(final CardMailRemoveReqDTO cardMailRemoveReqDTO) {
        return converter.convertToCardMailDTO(cardMailRemoveReqDTO);
    }

    public List<CardMailListResDTO> transferCardMailListResDTOList(final List<CardMailDTO> cardMailDTOList) {
        if (Func.isEmpty(cardMailDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardMailListResDTOList(cardMailDTOList);
    }

    public CardMailListResDTO transferCardMailListResDTO(final CardMailDTO cardMailDTO) {
        if (Func.isEmpty(cardMailDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardMailListResDTO(cardMailDTO);
    }

    public Page<CardMailPageResDTO> transferCardMailPageResDTOPage(final Page<CardMailDTO> cardMailDTOPage) {
//        if (Func.isEmpty(cardMailDTOPage) || Func.isEmpty(cardMailDTOPage.getRecords())) {
//            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
//        }

        return converter.convertToCardMailPageResDTOPage(cardMailDTOPage);
    }

    public CardMailShowResDTO transferCardMailShowResDTO(final CardMailDTO cardMailDTO) {
        if (Func.isEmpty(cardMailDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardMailShowResDTO(cardMailDTO);
    }

    public List<CardMailShowResDTO> transferCardMailShowResDTOList(final List<CardMailDTO> cardMailDTOList) {
        if (Func.isEmpty(cardMailDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "未查找到记录");
        }

        return converter.convertToCardMailShowResDTOList(cardMailDTOList);
    }
}
