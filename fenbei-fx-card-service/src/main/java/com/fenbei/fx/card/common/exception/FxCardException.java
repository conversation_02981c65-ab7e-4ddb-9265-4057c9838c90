package com.fenbei.fx.card.common.exception;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;

/**
 * <AUTHOR>
 * @date 2020/3/12
 */
public class FxCardException extends FinhubException {

    private GlobalCoreResponseCode responseCode;

    private Object data;

    public FxCardException() {
        super();
    }

    public FxCardException(int code, String msg, int type) {
        super(code, type, msg);
    }

    public FxCardException(int code, String msg) {
        super(code, msg);
    }

    public FxCardException(GlobalCoreResponseCode responseCode) {
        super(responseCode.getCode(), responseCode.getMessage(), responseCode.getTitle());
        this.responseCode = responseCode;
    }

    public FxCardException(GlobalCoreResponseCode responseCode, String message) {
        super(responseCode.getCode(), message, responseCode.getTitle());
        this.responseCode = responseCode;
    }

    public FxCardException(GlobalCoreResponseCode responseCode, Integer type) {
        super(responseCode.getCode(), type, responseCode.getMessage());
        this.responseCode = responseCode;
    }

    public FxCardException(int code, String msg, Integer type, String title) {
        super(code, type, msg, title);
    }

    public FxCardException(int code, String msg, Integer type, Object data) {
        super(code, type, msg);
        this.data = data;
    }

    public GlobalCoreResponseCode getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(GlobalCoreResponseCode responseCode) {
        this.responseCode = responseCode;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
