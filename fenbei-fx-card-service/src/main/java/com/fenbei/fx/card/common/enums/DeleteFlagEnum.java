package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DeleteFlagEnum {


    NORMAL(0, "正常"),
    DEL(1, "删除"),

    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("正常", "delete.flag.normal");
        I18N_KEY_MAP.put("删除", "delete.flag.del");
    }

    private final Integer code;

    private final String msg;

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }
}
