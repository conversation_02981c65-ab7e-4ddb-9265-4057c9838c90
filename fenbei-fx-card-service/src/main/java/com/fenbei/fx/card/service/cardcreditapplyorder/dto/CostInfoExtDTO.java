package com.fenbei.fx.card.service.cardcreditapplyorder.dto;

import com.fenbeitong.saasplus.api.model.dto.virtualcard.CostInfoExtCustomFieldDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/3/2 3:39 下午
 */
@Data
public class CostInfoExtDTO {

    /**
     * 费用归属依赖关系
     */
    private List costAttributionRelationList;
    /**
     * 费用归属配置ID
     */
    private String costAttributionSettingId;
    /**
     * 是否分摊 0不分摊
     */
    private int costInfoType;
    /**
     * 自定义参数
     */
    private CostInfoExtCustomFieldDTO customField;

}
