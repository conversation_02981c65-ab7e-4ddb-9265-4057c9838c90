package com.fenbei.fx.card.util;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Created by FBT on 2023/7/14.
 */
public class EmailCheckUtils {

    final static Pattern partern = Pattern.compile("[a-zA-Z0-9]+[\\.]{0,1}[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]+");

    /**
     * 允许的个人邮箱域名列表后续再加
     */
    private static final Set<String> ALLOWED_PERSONAL_EMAIL_DOMAINS = new HashSet<>(Arrays.asList(
        // 中国常用个人邮箱
        "qq.com", "163.com", "126.com", "sina.com", "sina.cn", "sohu.com", "139.com", "189.cn",
        "wo.cn", "21cn.com", "aliyun.com", "foxmail.com", "yeah.net", "tom.com", "vip.sina.com",
        "vip.163.com", "vip.126.com", "vip.qq.com", "188.com", "263.net", "x263.net",

        // 国际常用个人邮箱
        "gmail.com", "outlook.com", "hotmail.com", "yahoo.com", "icloud.com", "live.com",
        "msn.com", "aol.com", "mail.com", "protonmail.com", "yandex.com", "zoho.com",
        "tutanota.com", "fastmail.com", "gmx.com", "mail.ru", "inbox.com", "hushmail.com"
    ));

    /**
     * 验证输入的邮箱格式是否符合
     * @param email
     * @return 是否合法
     */
    public static boolean emailFormat(String email){
        try {
            boolean isMatch = partern.matcher(email).matches();
            return isMatch;
        }catch (Exception e){
            return false ;
        }
    }

    /**
     * 验证邮箱域名是否为允许的个人邮箱域名
     * @param email 邮箱地址
     * @return true-允许的个人邮箱域名，false-不允许的域名（可能是企业邮箱）
     */
    public static boolean isAllowedPersonalEmailDomain(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        try {
            // 提取域名部分
            int atIndex = email.lastIndexOf("@");
            if (atIndex == -1 || atIndex == email.length() - 1 || atIndex == 0) {
                return false;
            }

            String domain = email.substring(atIndex + 1).toLowerCase().trim();
            return ALLOWED_PERSONAL_EMAIL_DOMAINS.contains(domain);
        } catch (Exception e) {
            return false;
        }
    }

}
