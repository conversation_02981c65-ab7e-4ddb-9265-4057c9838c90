package com.fenbei.fx.card.service.bankcardflow.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.math.BigDecimal;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 删除 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankCardFlowRemoveReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private Long id;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 
     */
    private String bizNo;

    /**
     * 4申请额度,5退还额度,11消费,12退款,16冲正,41还款,51企业回收额度,52系统回收额度,53还款退回
     */
    private Integer operationType;

    /**
     * 当前额度
     */
    private BigDecimal currentAmount;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal operationAmount;

    /**
     * 卡的余额
     */
    private BigDecimal balance;

    /**
     * 1普通模式 2备用金模式
     */
    private Integer cardModel;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
