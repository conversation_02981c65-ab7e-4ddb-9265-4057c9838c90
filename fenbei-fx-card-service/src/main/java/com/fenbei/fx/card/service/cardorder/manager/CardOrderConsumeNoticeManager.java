package com.fenbei.fx.card.service.cardorder.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.util.EmailCheckUtils;
import com.fenbei.fx.card.util.EmailContract;
import com.fenbei.fx.card.util.SmsContract;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaPushMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.fenbeitong.finhub.kafka.producer.impl.KafkaProducerPublisher;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupReceiverVO;
import com.fenbeitong.saas.api.model.dto.message.MessageSetupVO;
import com.fenbeitong.saas.api.service.message.setting.IMessageSettingService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeCompanyDto;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CardOrderConsumeNoticeManager {

    @Autowired
    CardService cardService;

    @Autowired
    CardOrderService cardOrderService;

    @DubboReference
    private IBaseEmployeeExtService baseEmployeeExtService;
    @Autowired
    public MessageService messageService;
    @Autowired
    private KafkaProducerPublisher kafkaProducerPublisher;
    @DubboReference
    private IMessageSettingService iMessageSettingService;


    /**
     * 员工海外卡使用通知，1. 消费通知
     */
    String ITEM_OVERSEA_CONSUME_INFO_REMIND = "oversea_consume_info_remind";



    public static String CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID = "67569ddf59aac212705316a4";


    /**
     this.setIs_check(setup.getIsChecked());
     this.setApp_notice(setup.getIntVal1());
     this.setMail_notice(setup.getIntVal2());
     this.setPhone_notice(setup.getIntVal3());
     */

    public void sendNoticeMsgForConsume(String fxCardId,String bizNo, String tradeId,String subTradeId,Integer tradeType){
        CardDTO cardDTO = cardService.cardDetailByFxCardId(fxCardId);
        try {
            String title = "海外卡消费通知";
            //查询配置
            FinhubLogger.info("海外卡消费通知 查询消息配置信息 companyId = {}" ,cardDTO.getCompanyId());
            List<MessageSetupVO> messageSetupVOS = iMessageSettingService.queryCompanyMessageSetupWithDefault(cardDTO.getCompanyId(), Collections.singletonList(ITEM_OVERSEA_CONSUME_INFO_REMIND));
            FinhubLogger.info("海外卡消费通知 查询消息配置信息 companyId = {}，res = {}" ,cardDTO.getCompanyId() ,messageSetupVOS);
            if (messageSetupVOS != null && messageSetupVOS.size() > 0) {
                MessageSetupVO messageSetupVO = messageSetupVOS.get(0);
                if (messageSetupVO.getIsChecked() > 0) {
                   //额外配置人员
                   sendCompanyNoticeForConsume(messageSetupVO, cardDTO, bizNo, tradeId, subTradeId, tradeType, title);
                }
            }
        }catch (Exception e){
            FinhubLogger.warn("海外卡消费通知通知消息推送异常，employeeId={},bizNo={}", cardDTO.getEmployeeId(), bizNo , e );
        }
    }


    private void sendCompanyNoticeForConsume(MessageSetupVO messageSetupVO, CardDTO cardDTO,String bizNo, String tradeId,String subTradeId, Integer tradeType, String title){
        boolean appWebFlag = (1 == messageSetupVO.getIntVal1());
        boolean emailFlag = (1 == messageSetupVO.getIntVal2());
        boolean smsFlag = (1 == messageSetupVO.getIntVal3());
        if(!appWebFlag&&!emailFlag&&!smsFlag){
            // 无可发送渠道
            FinhubLogger.info("海外卡消费通知 无可发送渠道不发提醒 companyId = {}，res = {}" ,cardDTO.getCompanyId() ,messageSetupVO);
        }
        List<MessageSetupReceiverVO> messageSetupReceiverVOS = iMessageSettingService.queryMessageReceiverList(cardDTO.getCompanyId(),ITEM_OVERSEA_CONSUME_INFO_REMIND);

        if (CollectionUtils.isEmpty(messageSetupReceiverVOS)){
            return ;
        }
        List<String> userIdList = messageSetupReceiverVOS.stream().map(MessageSetupReceiverVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)){
            return;
        }

        List<EmployeeCompanyDto> employeeCompanyDtos = baseEmployeeExtService.queryEmployeeCompanyInfoListById(Lists.newArrayList(cardDTO.getEmployeeId()), cardDTO.getCompanyId());
        EmployeeCompanyDto companyEmployeeHolder = employeeCompanyDtos.get(0);
        CardOrderDTO cardOrderDTO;
        if(StringUtils.isBlank(subTradeId)){
            cardOrderDTO = cardOrderService.findByTradeId(tradeId,tradeType);
        }else{
            cardOrderDTO = cardOrderService.findByTradeIdAndSub(tradeId,subTradeId,tradeType);
        }
        if(cardOrderDTO==null) {return ;}
        String message = convertMsgForConsume4Other(cardDTO,companyEmployeeHolder,cardOrderDTO);
        if (appWebFlag){//APP通知
            userIdList.forEach(userId ->{
                try {
                    FinhubLogger.info("海外卡消费通知发送站内信给配置人员  入参 req = {},msg = {}", userId , message);
                    sendWebPush4ForConsume(cardDTO.getCompanyId(),userId,cardDTO.getCardPlatform(),cardDTO.getBankCardNo(),message,title);
                    sendMsgCenter4ForConsume(cardDTO.getCompanyId(),userId,bizNo,message,title);
                    pushAlert4ForConsume(cardDTO.getCompanyId(),userId,bizNo,message,title,null);
                }catch (Exception e){
                    FinhubLogger.info("海外卡消费通知发送站内通知给配置人员失败 userId = {} , message = {}", userId , message , e);
                }
            });
        }
        List<EmployeeCompanyDto> employeeOrgUnitDTOSOther = baseEmployeeExtService.queryEmployeeCompanyInfoListById(userIdList, cardDTO.getCompanyId());
        if (emailFlag){//邮件通知
            Set<String> mailSet = employeeOrgUnitDTOSOther.stream().map(EmployeeCompanyDto::getUserEmail).collect(Collectors.toSet());
            String companyName = employeeOrgUnitDTOSOther.get(0).getCompanyName();
            FinhubLogger.info("海外卡消费通知发送邮件给配置人员  入参 req = {},msg = {}", mailSet , message);
            sendMail4ForConsume(mailSet,message,companyName);
        }
        if (smsFlag){//短信通知
            FinhubLogger.info("海外卡消费通知发送短信给配置人员  入参 req = {},msg = {}", userIdList , message);
            employeeOrgUnitDTOSOther.forEach(employee ->{
                try {
                    sendMsg4ForConsume(employee.getUserPhone(),CREDIT_REFUND_MSG_TEMP_NO_AMOUNT_ID,message);
                } catch (Exception e){
                    FinhubLogger.info("海外卡消费通知发送短信给配置人员失败 employeeId = {} , message = {}", employee.getId(), message , e);
                }
            });
        }
    }



    private String convertMsgForConsume4Other(CardDTO cardDTO, EmployeeCompanyDto companyEmployeeHolder, CardOrderDTO cardOrderDTO){
        String bankCardNo = cardDTO.getBankCardNo();
        String bankCardNoSub = StringUtils.substring(bankCardNo, bankCardNo.length() - 4);

        String tradeTime = DateUtils.format(cardOrderDTO.getTradeTime(), DateUtils.FORMAT_DATETIME_HHMM);

        BigDecimal billTradeAmount = cardOrderDTO.getBillTradeAmount();

        String currency = cardOrderDTO.getBillTradeCurrency();
        CurrencyEnum currencyByCode = CurrencyEnum.getCurrencyByCodeIgnoreCase(currency);
        String message = MessageFormat.format("您好，{0}持有的尾号为{1}的海外卡，在{2}发生一笔金额为{3}{4}的交易", companyEmployeeHolder.getUserName(),bankCardNoSub,tradeTime,BigDecimalUtils.fen2yuan(billTradeAmount),currencyByCode.getCurrencyCode());
        return message;
    }


    private void sendWebPush4ForConsume(String companyId , String employeeId , String bankName , String bankAccountNo, String msg, String title) {
        try {
            //发送web
            KafkaWebMessageMsg kafkaWebMessageMsg = new KafkaWebMessageMsg();
            kafkaWebMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaWebMessageMsg.setMsgSubType(BizType.FxCardTrade.getCode());
            kafkaWebMessageMsg.setBizType(BizType.FxCardTrade.getCode());
            kafkaWebMessageMsg.setComment(msg);
            kafkaWebMessageMsg.setCompanyId(companyId);
            kafkaWebMessageMsg.setReceiver(employeeId);
            kafkaWebMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaWebMessageMsg.setSender(employeeId);
            kafkaWebMessageMsg.setTitle(title);
            JSONObject json = new JSONObject();
            json.put("bankName", bankName);
            json.put("bankAccountNo",bankAccountNo);
            kafkaWebMessageMsg.setInfo(json.toJSONString());
            FinhubLogger.info("海外卡消费通知推送WebPush 参数={}", JsonUtils.toJson(kafkaWebMessageMsg));
            kafkaProducerPublisher.publish(kafkaWebMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡消费通知消息发送失败");
            e.printStackTrace();
        }
    }

    private void sendMsg4ForConsume(String phone, String tempId , String message) {
        try {
            SmsContract msgBody = new SmsContract();
            Set<String> phones = new HashSet<>();
            phones.add(phone);
            Map<String, Object> param = new HashMap<>(3);
            param.put("var1", message);
            msgBody.setPhone_nums(phones);
            msgBody.setTemp_id(tempId);
            msgBody.setParam(param);
            messageService.pushSMS(msgBody);
            FinhubLogger.info("海外卡消费通知,短信发送成功 msg = {}", JSON.toJSONString(msgBody));
        } catch (IOException e2) {
            FinhubLogger.error("海外卡消费通知，短信发送失败！！！");
            e2.printStackTrace();
        }
    }


    private void sendMsgCenter4ForConsume(String companyId , String employeeId , String bizNo , String msg , String title) {
        try {
            KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
            kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
            kafkaSaasMessageMsg.setBizType(BizType.FxCardTrade.getCode());
            kafkaSaasMessageMsg.setBizOrder(bizNo);
            kafkaSaasMessageMsg.setComment(msg);
            kafkaSaasMessageMsg.setCompanyId(companyId);
            kafkaSaasMessageMsg.setReceiver(employeeId);
            kafkaSaasMessageMsg.setSenderType(SenderType.Person.getCode());
            kafkaSaasMessageMsg.setSender(employeeId);
            kafkaSaasMessageMsg.setTitle(title);
            FinhubLogger.info("海外卡消费通知推送 MsgCenter 参数={}",JsonUtils.toJson(kafkaSaasMessageMsg));
            kafkaProducerPublisher.publish(kafkaSaasMessageMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡消费通知消息中心发送失败！！！");
            e.printStackTrace();
        }
    }

    public void sendMail4ForConsume(Set<String> emailSet , String msg, String companyName) {
        try {
            EmailContract emailContract = new EmailContract();
            Set<String> checkList = new HashSet<>();
            emailSet.forEach(email ->{
                if (EmailCheckUtils.emailFormat(email)){
                    checkList.add(email);
                }
            });
            if (CollectionUtils.isEmpty(checkList)){
                return ;
            }
            FinhubLogger.info("sendMail4ForConsume req = {},msg = {}", emailSet , msg);
            // 收件人
            emailContract.setToList(checkList);
            // 邮件标题
            String subject = "【分贝通】{0}海外卡消费通知";
            String subjectFormat = MessageFormat.format(subject, companyName);
            emailContract.setSubject(subjectFormat);
            String text = "您好!" + "\n" + "感谢您使用分贝通的服务"+"\n"+ msg;
            emailContract.setText(text);
            // 发送邮件
            messageService.sendEmail(emailContract);
        }catch (Exception e) {
            FinhubLogger.error("发送邮件失败 email = {},msg = {}",emailSet,msg , e);
        }
    }

    private void pushAlert4ForConsume(String companyId , String employeeId , String bizNo, String msg, String title, String desc) {
        try {
            Map<String, Object> msgInfo = Maps.newHashMap();
            msgInfo.put("myself", "true");
            msgInfo.put("view_type", "1");
            msgInfo.put("id", bizNo);
            msgInfo.put("setting_type", "12");
            msgInfo.put("apply_type", ApplyType.BankIndividual.getValue());
            msgInfo.put("order_type", String.valueOf(BizType.FxCardTrade.getCode()));
            String linkInfo = JSONObject.toJSONString(msgInfo);
            KafkaPushMsg kafkaPushMsg = new KafkaPushMsg();
            kafkaPushMsg.setAlert(true);
            kafkaPushMsg.setContent(msg);
            kafkaPushMsg.setDesc(desc);
            kafkaPushMsg.setMsg(linkInfo);
            kafkaPushMsg.setMsgType(0+"");
            kafkaPushMsg.setTitle(title);
            kafkaPushMsg.setUserId(employeeId);
            kafkaPushMsg.setCompanyId(companyId);
            FinhubLogger.info("海外卡消费通知push消息参数kafkaPushMsg:{}",kafkaPushMsg);
            kafkaProducerPublisher.publish(kafkaPushMsg);
        } catch (Exception e) {
            FinhubLogger.error("海外卡消费通知push消息参数msg失败:{}", msg,e);
        }
    }


}
