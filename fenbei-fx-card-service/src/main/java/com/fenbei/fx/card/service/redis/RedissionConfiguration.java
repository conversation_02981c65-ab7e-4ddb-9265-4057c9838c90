package com.fenbei.fx.card.service.redis;

import com.finhub.framework.cache.property.RedisProperties;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName RedissionConfiguration
 * @Description: redission配置类
 * <AUTHOR>
 * @Date 2021/9/18
 **/
@Slf4j
@Configuration
@EnableConfigurationProperties(RedisProperties.class)
public class RedissionConfiguration {

    final String REDIS_PREFIX = "redis://";

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
            .setAddress(REDIS_PREFIX + redisProperties.getIp() + ":" + redisProperties.getPort())
            .setPassword(redisProperties.getPassword())
            .setDatabase(redisProperties.getDatabase())
            .setConnectionMinimumIdleSize(redisProperties.getMinIdle())
            .setConnectionPoolSize(redisProperties.getMaxIdle())
            .setSubscriptionConnectionMinimumIdleSize(redisProperties.getMinIdle())
            .setSubscriptionConnectionPoolSize(redisProperties.getMaxIdle());

        try {
            return Redisson.create(config);
        } catch (Exception e) {
            log.error("RedissonClient init redis url:[{}], Exception:", redisProperties.getIp(), e);
            return null;
        }
    }

}
