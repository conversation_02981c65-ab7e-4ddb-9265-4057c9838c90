package com.fenbei.fx.card.service.cardholderoperflow.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholderoperflow.po.CardholderOperFlowPO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 持卡人被操作流水 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardholderOperFlowConverter extends BaseConverter<CardholderOperFlowDTO, CardholderOperFlowPO> {

    static CardholderOperFlowConverter me() {
        return SpringUtil.getBean(CardholderOperFlowConverter.class);
    }

    CardholderOperFlowDTO convertToCardholderOperFlowDTO(CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO);

    CardholderOperFlowDTO convertToCardholderOperFlowDTO(CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO);

    CardholderOperFlowDTO convertToCardholderOperFlowDTO(CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO);

    CardholderOperFlowDTO convertToCardholderOperFlowDTO(CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO);

    CardholderOperFlowDTO convertToCardholderOperFlowDTO(CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO);

    CardholderOperFlowShowResDTO convertToCardholderOperFlowShowResDTO(CardholderOperFlowDTO cardholderOperFlowDTO);

    List<CardholderOperFlowShowResDTO> convertToCardholderOperFlowShowResDTOList(List<CardholderOperFlowDTO> cardholderOperFlowDTOList);

    CardholderOperFlowListResDTO convertToCardholderOperFlowListResDTO(CardholderOperFlowDTO cardholderOperFlowDTO);

    List<CardholderOperFlowListResDTO> convertToCardholderOperFlowListResDTOList(List<CardholderOperFlowDTO> cardholderOperFlowDTOList);

    List<CardholderOperFlowDTO> convertToCardholderOperFlowDTOList(List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList);

    CardholderOperFlowPageResDTO convertToCardholderOperFlowPageResDTO(CardholderOperFlowDTO cardholderOperFlowDTO);

    List<CardholderOperFlowPageResDTO> convertToCardholderOperFlowPageResDTOList(List<CardholderOperFlowDTO> cardholderOperFlowDTOList);

    Page<CardholderOperFlowPageResDTO> convertToCardholderOperFlowPageResDTOPage(Page<CardholderOperFlowDTO> cardholderOperFlowDTOPage);
}
