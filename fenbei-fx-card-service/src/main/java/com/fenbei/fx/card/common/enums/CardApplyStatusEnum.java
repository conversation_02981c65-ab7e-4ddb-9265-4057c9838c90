package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by FBT on 2023/4/18.
 */
@AllArgsConstructor
public enum CardApplyStatusEnum {

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，4.银行处理中 5.银行失败 6.银行成功'
     *
     * operateType : 1:待审核 2：更新中 3：更新失败 4：生效中 5：已失效 6:已拒绝
     */
    WAITCHECK(1,"待审核",1,7, CardShowStatusEnum.WAITCHECK.getStatus()),
    PENDING(4,"审核中",2,8, CardShowStatusEnum.PENDING.getStatus()),
    PASS(2,"审核通过",0,0, CardShowStatusEnum.PENDING.getStatus()),
    REFUSE(3,"审核拒绝",6,10, CardShowStatusEnum.REFUSE.getStatus()),
    FAILED(5,"更新失败",3,9, CardShowStatusEnum.OPER_FAILED.getStatus()),
    BANKPASS(6,"银行成功",0,0, CardShowStatusEnum.ACTIVE.getStatus()),
    STEREO_PENDING(7,"待邮寄",7,11, CardShowStatusEnum.WAITMAIL.getStatus()),
    EXPIRED(8,"已过期",0,12, CardShowStatusEnum.EXPIRED.getStatus()),
//    DISABLE(9,"已失效",5,5, CardShowStatusEnum.DISABLE.getStatus()),
//    ACTIVE(10,"已失效",4,1, CardShowStatusEnum.ACTIVE.getStatus()),
    WAIT_ACTIVE(11,"待激活",7,11, CardShowStatusEnum.WAIT_ACTIVE.getStatus()),
    ;

    public static CardApplyStatusEnum getApplyStatus(Integer status){
        for (CardApplyStatusEnum item : values()) {
            if (Objects.equals(item.getOperateType(), status)) {
                return item;
            }
        }
        return null;
    }

    public static CardApplyStatusEnum getRelationEnum(Integer status ){
        for (CardApplyStatusEnum item : values()) {
            if (item.getStatus() == status) {
                return item;
            }
        }
        return null;
    }

    public static Integer mergeCardStatus(Integer applyStatus){
        CardApplyStatusEnum cardApplyStatusEnum =  CardApplyStatusEnum.getRelationEnum(applyStatus);
        if (cardApplyStatusEnum != null){
            return cardApplyStatusEnum.getCardStatus();
        }
        return null;
    }

    /**
     * 获取在进行中的申请
     * @return
     */
    public static List<Integer> getProgressApplyStatus(){
        List<Integer> statusList = new ArrayList<>();

        statusList.add(WAITCHECK.getStatus());
        statusList.add(PENDING.getStatus());
        statusList.add(PASS.getStatus());

        return statusList;
    }

    public static List<Integer> getCanReSubmitStatus(){
        List<Integer> statusList = new ArrayList<>();

        statusList.add(WAITCHECK.getStatus());
        statusList.add(REFUSE.getStatus());
        statusList.add(FAILED.getStatus());
        statusList.add(PASS.getStatus());

        return statusList;
    }

    public static Integer getShowStatus(Integer status){
        for (CardApplyStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item.getShowStatus();
            }
        }

        return null;
    }


    private  Integer status;

    private  String name;

    private Integer operateType;

    private Integer cardStatus;

    private Integer showStatus;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return I18nUtils.getMessage("CardApplyStatusEnum." + this.name(), this.name);
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Integer getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }
}
