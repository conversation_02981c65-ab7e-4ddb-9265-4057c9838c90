package com.fenbei.fx.card.util;

import com.finhub.framework.i18n.manager.MessageSourceManager;

import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/06/17 16:00
 */
@UtilityClass
public class I18nUtils {

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("未知", "i18n.weizhi");

        I18N_KEY_MAP.put("无需核销", "i18n.wuxu.hexiao");
        I18N_KEY_MAP.put("已退款", "i18n.yi.tuikuan");
        I18N_KEY_MAP.put("部分待核销", "i18n.bufen.daihexiao");
        I18N_KEY_MAP.put("待核销", "i18n.dai.hexiao");
        I18N_KEY_MAP.put("核销中", "i18n.hexiao.zhong");
        I18N_KEY_MAP.put("已核销", "i18n.yi.hexiao");

        I18N_KEY_MAP.put("预授权", "i18n.yuding");
        I18N_KEY_MAP.put("消费", "i18n.xiaofei");
        I18N_KEY_MAP.put("退款", "i18n.tuikuan");
        I18N_KEY_MAP.put("预授权撤销", "i18n.yuding.shifang");
        I18N_KEY_MAP.put("交易失败", "i18n.jiaoyi.shibai");
        I18N_KEY_MAP.put("还款", "i18n.huankuan");

        I18N_KEY_MAP.put("企业发放", "i18n.qiye.fafang");

        I18N_KEY_MAP.put("退还额度", "i18n.tuihuan.edu");

        I18N_KEY_MAP.put("存在审批中的申请单，不允许提交新的申请单", "i18n.cunzaishenpizhong.fail");
        I18N_KEY_MAP.put("当前为模式为备用金模式，在额度未清零/未核销完成时，无法申请新的额度", "i18n.cunzaiweihexiao.fail");
        I18N_KEY_MAP.put("申请成功", "i18n.apply.success");

        I18N_KEY_MAP.put("预授权", "i18n.trade.yushouquan");
        I18N_KEY_MAP.put("预授权撤销", "i18n.trade.yushouquanchexiao");
        I18N_KEY_MAP.put("交易失败", "i18n.trade.jiaoyishibai");
        I18N_KEY_MAP.put("消费", "i18n.trade.xiaofei");
        I18N_KEY_MAP.put("退款", "i18n.trade.tuikuan");
        I18N_KEY_MAP.put("错花还款", "i18n.trade.cuohuahuankuan");

    }

    public static String getMessage(String i18nKey, String defaultMsg) {
        try {
            String i18nValue = MessageSourceManager.me().getMessage(i18nKey);
            if (isBlank(i18nValue) || i18nValue.equals(i18nKey)) {
                return defaultMsg;
            }
            return i18nValue;
        } catch (Exception e) {
            return defaultMsg;
        }
    }

    public static String getMessage(String i18nKey) {
        try {
            return MessageSourceManager.me().getMessage(i18nKey);
        } catch (Exception e) {
            return i18nKey;
        }
    }

    public static String transferI18nMessage(String msg) {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : getMessage(i18nKey, msg);
    }
}
