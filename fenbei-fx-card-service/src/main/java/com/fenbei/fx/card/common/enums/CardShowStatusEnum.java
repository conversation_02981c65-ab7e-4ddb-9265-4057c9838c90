package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by FBT on 2023/4/18.
 * 页面展示状态
 */
@AllArgsConstructor
public enum CardShowStatusEnum {
     /* operateType : 1:待审核 2：更新中 3：更新失败 4：生效中 5：已失效 6:已拒绝*/

    /**
     * 卡状态：1.待审核 2.审核中 3.审核失败 4.生效中 5.已失效 6.已拒绝 99.其他
     */
    WAITCHECK(1, "待审核"),
    PENDING(2, "审核中"),
    OPER_FAILED(3, "创建失败"),
    ACTIVE(4, "生效中"),
    DISABLE(5, "已失效"),
    REFUSE(6, "已拒绝"),
    WAITMAIL(7, "待邮寄"),
    EXPIRED(8, "已过期"),

    WAIT_ACTIVE(11,"待激活"),
    OTHER(99, "其它");

    private Integer status;

    private String name;

    public static List<Map<Integer,String>> getKeyValueEnum() {
        List<Map<Integer,String>> list = new ArrayList<>();
        for (CardShowStatusEnum cardQueryEnum : values()) {
            if (cardQueryEnum.getStatus() != OTHER.getStatus()){
                Map map = new HashMap();
                map.put("key",cardQueryEnum.getStatus());
                map.put("value",cardQueryEnum.getName());
                list.add(map);
            }
        }
        return list;
    }

    public static CardShowStatusEnum get(Integer status) {
        for (CardShowStatusEnum item : values()) {
            if (item.getStatus().equals(status)) {
                return item;
            }
        }

        return null;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return I18nUtils.getMessage("CardShowStatusEnum." + this.name(), this.name);
    }

    public void setName(String name) {
        this.name = name;
    }


}
