package com.fenbei.fx.card.service.usercard.dto;

import com.fenbei.fx.card.common.constant.CoreConstant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
public class UserCardInfosDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 公司支持开国际卡的渠道信息
     */
    private List<CardInfoDTO> companyFxCardInfoList;

    /**
     * 用户已经开的国际卡
     */
    private List<CardInfoDTO> userFxCardInfoList;

    /**
     * 公司是否有海外卡权限
     * 0 无 1有
     */
    private Integer companyFxCardAuthStatus;


    /**
     * 用户可申请的国际卡
     */
    private List<CardInfoDTO> userCanApplyFxCardInfoList;

    /**
     * 员工是否有海外卡权限
     * 0 无 1有
     */
    private Integer employeeFxCardAuthStatus;


    /**
     * 海外卡介绍url
     */
    private String fxCardIntroUrl;

    public UserCardInfosDTO(){
        companyFxCardInfoList = new ArrayList<>();
        userFxCardInfoList = new ArrayList<>();
        userCanApplyFxCardInfoList = new ArrayList<>();
        companyFxCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
        employeeFxCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
    }

    public boolean hasCompanyAuth(Integer companyFxCardAuthStatus){
        return Objects.equals(CoreConstant.CARD_OPER_AUTH_EXIST, companyFxCardAuthStatus);
    }

    public boolean hasEmployeeAuth(Integer employeeFxCardAuthStatus){
        return Objects.equals(CoreConstant.CARD_OPER_AUTH_EXIST, employeeFxCardAuthStatus);
    }


}
