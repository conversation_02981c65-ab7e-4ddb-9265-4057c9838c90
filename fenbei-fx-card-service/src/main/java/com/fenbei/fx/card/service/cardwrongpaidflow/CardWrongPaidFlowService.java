package com.fenbei.fx.card.service.cardwrongpaidflow;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowAddReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowShowResDTO;

import java.util.List;

/**
 * 错花还款流水表 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
public interface CardWrongPaidFlowService extends BaseService<CardWrongPaidFlowDTO> {

    static CardWrongPaidFlowService me() {
        return SpringUtil.getBean(CardWrongPaidFlowService.class);
    }

    /**
     * 列表
     *
     * @param cardWrongPaidFlowListReqDTO 入参DTO
     * @return
     */
    List<CardWrongPaidFlowListResDTO> list(CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO);

    /**
     * First查询
     *
     * @param cardWrongPaidFlowListReqDTO 入参DTO
     * @return
     */
    CardWrongPaidFlowListResDTO listOne(CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO);

    /**
     * 分页
     *
     * @param cardWrongPaidFlowPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardWrongPaidFlowPageResDTO> pagination(CardWrongPaidFlowPageReqDTO cardWrongPaidFlowPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardWrongPaidFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardWrongPaidFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardWrongPaidFlowAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardWrongPaidFlowShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardWrongPaidFlowShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardWrongPaidFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardWrongPaidFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardWrongPaidFlowRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO);
}
