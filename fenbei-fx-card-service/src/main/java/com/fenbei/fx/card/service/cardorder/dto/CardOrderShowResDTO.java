package com.fenbei.fx.card.service.cardorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡订单 详情 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOrderShowResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private Long id;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 交易单号
     */
    private String bizNo;

    /**
     * 退款时代表原业务单号
     */
    private String oriBizNo;

    /**
     * 11消费,12退款
     */
    private Integer type;

    /**
     * 币种 美元-USD
     */
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    private String tradeName;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 交易地
     */
    private String tradeAddress;

    /**
     * 交易备注
     */
    private String tradeRemark;

    /**
     * 核销状态
     */
    private Integer checkStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 基准币种 美元-USD
     *
     */
    private String billTradeCurrency;

    /**
     * 基准操作金额 单位：分
     */
    private BigDecimal billTradeAmount;

    /**
     * 渠道信息
     */
    private String cardPlatform;

}
