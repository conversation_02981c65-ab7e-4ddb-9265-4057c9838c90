package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 生效模式
 */
@AllArgsConstructor
public enum ActiveModelEnum {

    /**
     * 1.普通模式,2.备用金模式
     */
    NORMAL(1, "普通模式"),
    PETTY(2, "备用金模式"),

    ;

    public static ActiveModelEnum getEnum(Integer code) {
        for (ActiveModelEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
    public static boolean isActiveModel(Integer code) {
        for (ActiveModelEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return true;
            }
        }
        return false;
    }



    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return I18nUtils.getMessage("ActiveModelEnum." + this.name(), this.name);
    }

    public void setName(String name) {
        this.name = name;
    }

}
