package com.fenbei.fx.card.service.cardorder.dto;

import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录 Web 分页 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardTradeInfoStereoPageResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;


    /**
     * 公司id
     */
    private String companyId;

    /**
     * 交易记录 id
     * 示例：*********
     */
    private String transactionId;

    /**
     * 持卡人
     * 示例：张三
     */
    private String cardHolderName;

    /**
     * "bankAccountNo": "**** 7817", //卡号：masked_card_number,
     * 示例：**** 7817
     */
    private String maskedCardNumber;

    /**
     * 卡片类型
     * 示例：VISA
     */
    private String bankCardType;

    /**
     * "shopName": "中国银联无卡快捷支付业务二级商户信息测试",
     * 商户名称：对应库表merchant_name
     * 示例：中国银联无卡快捷支付业务二级商户信息测试
     */
    private String merchantName;

    /**
     * 消费类型 1-预定 4-预定释放 5-交易失败 11-消费 12-退款
     * 示例：消费
     */
    private String transactionType;

    /**
     * 消费类型编码 1-预定 4-预定释放 5-交易失败 11-消费 12-退款
     * 示例：1
     */
    private Integer transactionTypeCode;

    /**
     * 交易时间
     * 示例：2021-05-20 12:00:00
     */
    private Date transactionDate;

    /**
     * 交易时间
     * 示例：2021-05-20 12:00:00
     */
    private Date createTime;

    /**
     * 交易币种
     * 示例：USD
     */
    private String transactionCurrency;

    /**
     * 交易金额
     * 示例：100.00
     */
    private BigDecimal transactionAmount;

    /**
     * 交易金额描述
     */
    private TotalPrice transactionAmountDesc;

    /**
     * 交易地 "tradeAddress":"USA-NEW",
     * 示例：USA-NEW
     */
    private String transactionAddress;

    /**
     * 折算币种
     */
    private String obversionCurrType;

    /**
     * 折算金额
     */
    private BigDecimal obversionTotalPrice;

    /**
     * 折算金额
     */
    private TotalPrice obversionTotalPriceDesc;

    /**
     * 核销状态 0-无核销状态（如退款单，已全额退款的正向单） 2-未核销 3-核销中 4-已核销
     * 示例：未核销
     */
    private String checkStatus;

    /**
     * 核销状态编码
     * 示例：0-无核销状态（如退款单，已全额退款的正向单） 2-未核销 3-核销中 4-已核销
     */
    private Integer checkStatusCode;

    /**
     * "uncheckConsume": 0, // 未核销金额
     */
    private BigDecimal uncheckConsume;

    /**
     * 未核销金额描述
     */
    private TotalPrice uncheckConsumeDesc;

    /**
     * "unNeedCheckConsume": 0, // 无需核销金额
     */
    private BigDecimal unNeedCheckConsume;

    /**
     * 无需核销金额描述
     */
    private TotalPrice unNeedCheckConsumeDesc;

    /**
     * 交易备注
     */
    private String remark;


    /**
     * 已核销金额
     */
    private BigDecimal checkedConsume;

    private TotalPrice checkedConsumeDesc;

    /**
     * 已退款金额
     */
    private BigDecimal refundConsume;
    /**
     * 已退款金额
     */
    private TotalPrice refundConsumeDesc;
    /**
     * 已错花金额(无需核销)
     */
    private BigDecimal wrongPaidConsume;
    /**
     * 已错花金额(无需核销)
     */
    private TotalPrice wrongPaidConsumeDesc;

    /**
     * 核销中金额
     */
    private BigDecimal checkingConsume;

    /**
     * 核销中金额,元
     */
    private TotalPrice checkingConsumeDesc;


    private String cardPlatform;

}
