package com.fenbei.fx.card.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

/**
 * Created by zhuminghua on 2017/6/17.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsContract {

    /**
     * 时间
     */
    private Long deliver_time;
    /**
     * 手机号
     */
    private Set<String> phone_nums;
    /**
     * 模板id
     */
    private String temp_id;
    /**
     * 参数
     */
    private Map<String, Object> param;
    /**
     * 短信内容
     */
    private String content;
    /**
     * 发送类型 1验证码 2 通知类 3 营销类 4 语音短信，不传时，如果传了模板id，取模板对应类型，否则默认为2通知类
     */
    private Integer type;

    public SmsContract(Set<String> phone_nums, String temp_id) {
        this.phone_nums = phone_nums;
        this.temp_id = temp_id;
    }

    public SmsContract(Long deliver_time, Set<String> phone_nums, String content) {
        this.deliver_time = deliver_time;
        this.phone_nums = phone_nums;
        this.content = content;
    }
}
