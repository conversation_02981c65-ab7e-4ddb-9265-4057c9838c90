package com.fenbei.fx.card.service.cardholder.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import org.mapstruct.Mapper;

/**
 * 持卡人 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardholderConverter extends BaseConverter<CardholderDTO, CardholderPO> {

    static CardholderConverter me() {
        return SpringUtil.getBean(CardholderConverter.class);
    }

}
