package com.fenbei.fx.card.service.cardverificationflow.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 核销记录表 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardVerificationFlowConverter extends BaseConverter<CardVerificationFlowDTO, CardVerificationFlowPO> {

    static CardVerificationFlowConverter me() {
        return SpringUtil.getBean(CardVerificationFlowConverter.class);
    }

    CardVerificationFlowDTO convertToCardVerificationFlowDTO(CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO);

    CardVerificationFlowDTO convertToCardVerificationFlowDTO(CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO);

    CardVerificationFlowDTO convertToCardVerificationFlowDTO(CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO);

    CardVerificationFlowDTO convertToCardVerificationFlowDTO(CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO);

    CardVerificationFlowDTO convertToCardVerificationFlowDTO(CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO);

    CardVerificationFlowShowResDTO convertToCardVerificationFlowShowResDTO(CardVerificationFlowDTO cardVerificationFlowDTO);

    List<CardVerificationFlowShowResDTO> convertToCardVerificationFlowShowResDTOList(List<CardVerificationFlowDTO> cardVerificationFlowDTOList);

    CardVerificationFlowListResDTO convertToCardVerificationFlowListResDTO(CardVerificationFlowDTO cardVerificationFlowDTO);

    List<CardVerificationFlowListResDTO> convertToCardVerificationFlowListResDTOList(List<CardVerificationFlowDTO> cardVerificationFlowDTOList);

    List<CardVerificationFlowDTO> convertToCardVerificationFlowDTOList(List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList);

    CardVerificationFlowPageResDTO convertToCardVerificationFlowPageResDTO(CardVerificationFlowDTO cardVerificationFlowDTO);

    List<CardVerificationFlowPageResDTO> convertToCardVerificationFlowPageResDTOList(List<CardVerificationFlowDTO> cardVerificationFlowDTOList);

    Page<CardVerificationFlowPageResDTO> convertToCardVerificationFlowPageResDTOPage(Page<CardVerificationFlowDTO> cardVerificationFlowDTOPage);
}
