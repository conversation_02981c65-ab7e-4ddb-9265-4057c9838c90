package com.fenbei.fx.card.service.cardmail.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 实体卡邮寄管理表 详情 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardMailShowResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private String id;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 卡邮寄编号（line2）
     */
    private String cardMailNo;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 员工手机
     */
    private String employeePhone;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 卡归属类型：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardOwnerType;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardIssuanceTime;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 分贝通收件状态：1-未收件，2-已收件，3-异常
     */
    private Integer fbtReceiveStatus;

    /**
     * 转运状态：1-未寄出，2-已寄出
     */
    private Integer forwardStatus;

    /**
     * 签收状态：1-未签收，2-已签收，3-异常
     */
    private Integer signStatus;

    /**
     * 是否转寄：0-不，1-是
     */
    private Integer forwardFlag;

    /**
     * 转寄单号
     */
    private String forwardNo;

    /**
     * 转供应商
     */
    private String forwardSupplier;
    /**
     * 发卡方实际邮寄地址地址
     */
    private String axPostAddress;
    /**
     * 原收获地址
     */
    private String originalAddress;

    /**
     * 变更后收货地址
     */
    private String changedAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
