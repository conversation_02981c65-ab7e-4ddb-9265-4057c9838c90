package com.fenbei.fx.card.common.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 申请类型
 */
@AllArgsConstructor
public enum CreditApplyTypeEnum {

    /**
     * 额度变更类型 1.申请 2.退回
     */
    APPLY(1, "额度申请"),
    RETURN(2, "额度退回"),

    ;

    public static CreditApplyTypeEnum getEnum(Integer code) {
        for (CreditApplyTypeEnum item : values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
