package com.fenbei.fx.card.common.enums;

import com.fenbeitong.finhub.common.utils.StringUtils;
import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * 发卡渠道
 */
@AllArgsConstructor
public enum CardPlatformEnum {

    /*
     * 发卡渠道 AIRWALLEX、LIANLIAN
     */
    AIRWALLEX("AIRWALLEX", "AIRWALLEX空中云汇", "https://stc.fenbeitong.com/icon/bank/airwallex.png"),
    LIANLIAN("LIANLIAN","连连","https://stc.fenbeitong.com/icon/bank/********/<EMAIL>"),
    ;

    public static CardPlatformEnum getPlatform(String code) {
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (CardPlatformEnum item : values()) {
            if (item.getCode().equalsIgnoreCase(code)){
                return item;
            }
        }
        return null;
    }

    public static boolean isLianLian(String code){
        return Objects.equals(code, LIANLIAN.getCode());
    }
    public static boolean isAirwallex(String code){
        return Objects.equals(code, AIRWALLEX.getCode());
    }

    private String code;

    private String name;

    private String platformIcon;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPlatformIcon() {
        return platformIcon;
    }

    public void setPlatformIcon(String platformIcon) {
        this.platformIcon = platformIcon;
    }

}
