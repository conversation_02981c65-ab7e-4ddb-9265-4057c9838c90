package com.fenbei.fx.card.service.cardorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

/**
 * 交易记录详情页 → 保存备注 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardTradeInfoAppRemarkReqDTO implements Serializable {

    /**
     * 交易记录 id【废弃字段】
     */
    private String transactionId;

    /**
     * 交易单号
     */
    private String orderId;
    /**
     * 交易类型
     */
    private Integer transactionType;

    /**
     * 备注信息
     */
    @NotBlank(message = "备注信息不能为空")
    private String remark;
}
