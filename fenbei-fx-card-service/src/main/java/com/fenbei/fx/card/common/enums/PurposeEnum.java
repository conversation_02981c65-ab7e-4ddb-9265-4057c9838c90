package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum PurposeEnum {


    OTHER("OTHER", "其他",CardPlatformEnum.AIRWALLEX.getCode()),
    BUSINESS_EXPENSES("BUSINESS_EXPENSES", "业务费用",CardPlatformEnum.AIRWALLEX.getCode()),
    CLIENT_EXPENSES("CLIENT_EXPENSES", "客勤费用",CardPlatformEnum.AIRWALLEX.getCode()),
    MARKETING_EXPENSES("MARKETING_EXPENSES", "营销费用",CardPlatformEnum.AIRWALLEX.getCode()),
    OFFICE_SUPPLIES("OFFICE_SUPPLIES", "办公采购",CardPlatformEnum.AIRWALLEX.getCode()),
    ONLINE_PURCHASING("ONLINE_PURCHASING", "网上购物",CardPlatformEnum.AIRWALLEX.getCode()),
    SUBSCRIPTIONS("SUBSCRIPTIONS", "订阅服务",CardPlatformEnum.AIRWALLEX.getCode()),
    TEAM_EXPENSES("TEAM_EXPENSES", "团建费用",CardPlatformEnum.AIRWALLEX.getCode()),
    TRAVEL_EXPENSES("TRAVEL_EXPENSES", "差旅费用",CardPlatformEnum.AIRWALLEX.getCode())
    ;

    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();
    private static final Map<String, PurposeEnum> ENUM_MAP = Maps.newHashMap();

    static {
        I18N_KEY_MAP.put("其他", "purpose.other");
        I18N_KEY_MAP.put("业务费用", "purpose.business.expenses");
        I18N_KEY_MAP.put("客勤费用", "purpose.client.expenses");
        I18N_KEY_MAP.put("营销费用", "purpose.marketing.expenses");
        I18N_KEY_MAP.put("办公采购", "purpose.office.supplies");
        I18N_KEY_MAP.put("网上购物", "purpose.online.purchasing");
        I18N_KEY_MAP.put("订阅服务", "purpose.subscriptions");
        I18N_KEY_MAP.put("团建费用", "purpose.team.expenses");
        I18N_KEY_MAP.put("差旅费用", "purpose.travel.expenses");


        for (PurposeEnum item : values()) {
            ENUM_MAP.put(item.getCode(), item);
        }
    }

    public static PurposeEnum getEnum(String code) {
        return ENUM_MAP.get(code);
    }


    public static List<KeyValueVO> getKeyValueInfo(String platform){
        List<KeyValueVO> valueVOList = new ArrayList<>();
        for (PurposeEnum item : values()) {
            if (item.getPlatform().equals(platform)){
                KeyValueVO valueVO = new KeyValueVO(item.getCode(),item.getMsg());
                valueVOList.add(valueVO);
            }
        }
        return valueVOList;
    }


    private final String code;

    private final String msg;

    private final String platform;

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }
}
