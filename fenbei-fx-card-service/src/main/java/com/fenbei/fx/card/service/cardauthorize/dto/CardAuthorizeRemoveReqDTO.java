package com.fenbei.fx.card.service.cardauthorize.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡授权表 删除 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardAuthorizeRemoveReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 币种 美元-USD
     */
    private String tradeCurrency;

    /**
     * 交易金额 单位：分
     */
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    private String tradeName;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 交易地
     */
    private String tradeAddress;

    /**
     * 原始请求
     */
    private String sourceData;

    /**
     * 授权状态
     */
    private Integer authStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
