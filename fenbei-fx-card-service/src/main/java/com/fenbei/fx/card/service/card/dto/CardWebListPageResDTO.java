package com.fenbei.fx.card.service.card.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Title: BankCardBaseReqDTO
 * @ProjectName fenbei-pay
 * @Description: 卡的基本信息
 * @author: wh
 * @date 2019/9/20 10:39
 */
@Data
@ToString(callSuper = true)
public class CardWebListPageResDTO implements Serializable  {
    /**
     * 卡编码
     */
    private String fbCardNo;
    /**
     * 开户：员工名称
     */
    private String employeeName;
    /**
     * 一级部门
     */
    private String stairOrgUnitId;

    /**
     * 一级部门名称
     */
    private String stairOrgUnitName;

    /**
     * 部门
     */
    private String userUnitName;

    /**
     * 开户：分贝通虚拟卡号
     */

    private String employeePhone;


    /**
     * 卡的余额
     */
    private BigDecimal cardBalance;

    /**
     * 企业资金
     */
    private BigDecimal cardCompanyBalance;

    /**
     * 个人资金
     */
    private BigDecimal cardPersonalBalance;

    /**
     * 申请额度最近时间
     */
    private Date applyCreditTime;


    List<CardsInfo> cardsInfo;

    /**
     * 银行卡号
     */
    private String bankAccountNo;

    /**
     * 银行名称简写
     */
    private String bankName;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 部门id
     */
    private String orgUnitId;
    /**
     * 银行名称简写编码,正常使用bankName承载银行编码，但是bankName被占用，使用bankNameCode承载，如：CGB
     */
    private String bankNameCode;



    @Data
    public static class CardsInfo implements Serializable {

        private String bankNameCode;

        private String bankName;

        private String bankAccountNo;

        private String fbCardNo;

        private String info;


    }


}
