package com.fenbei.fx.card.service.cardholder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 持卡人 修改 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderModifyReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private String id;

    /**
     * 申请单id，如果是编辑，传值
     */
    private String applyId;
    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 渠道方持卡人id
     */
    private String bankCardholderId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 员工id
     */
    private String employeeName;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 申请人名
     */
    private String firstName;

    /**
     * 申请人姓
     */
    private String lastName;

    /**
     * 姓名
     */
    private String name;

    /**
     * email地址
     */
    private String email;

    /**
     * 出生日期，格式为YYY-MM-DD
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birth;

    /**
     * 国家区号
     */
    private String nationCode;

    /**
     * 持卡人手机号
     */
    private String phone;

    /**
     * 展示状态，根据此状态判断是申请单还是持卡人
     * 1：待审核 2：更新中 4：更新失败3：生效中 5：已失效
     */
    private Integer showStatus;

    /**
     * 证件的国家:US
     */
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    private Integer identificationType;

    /**
     * 证件号
     */
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    private String identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围，2-长期有效
     */
    private Integer identificationExpiryType;

    /**
     * 地区
     */
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;


    public String getName() {
        return this.lastName + this.firstName;
    }
}
