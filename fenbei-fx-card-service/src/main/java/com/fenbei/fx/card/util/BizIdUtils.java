package com.fenbei.fx.card.util;


/**
 * 业务id生成
 */
public class BizIdUtils {

    private static final String AIR_REQUEST_ID = "ARI";

    private static final String CARDHOLDER_APPLY_ID_PRE = "CHA";

    private static final String CARDHOLDER_ID_PRE = "CH";

    private static final String CARD_APPLY_ID_PRE = "CA";

    private static final String CARD_ID_PRE = "FXC";

    private static final String FX_ORDER_PRE = "FXO";

    private static final String FX_ORDER_REFUND_PRE = "FXR";

    private static final String FX_CREDIT_APPLY_PRE = "FCA";

    private static final String FX_WRONG_PAID_PRE = "WPR";

    private static final String FX_CREDIT_REFUND_PRE = "FCR";

    private static final String LIANLIAN_REQUEST_ID = "LRI";

    private static final String FX_CREDIT_APPLY_ORDER = "FAO";
    private static final String FX_CREDIT_APPLY_BAT_ORDER = "FBO";
    private static final String FX_CREDIT_APPLY_ORDER_MEANING_NO = "FMO";

    public static String getLianLianRequestId() {
        return LIANLIAN_REQUEST_ID + IdUtils.getId();
    }

    public static String getAirRequestId() {
        return AIR_REQUEST_ID + IdUtils.getId();
    }

    public static String getCardholderApplyId() {
        return CARDHOLDER_APPLY_ID_PRE + IdUtils.getId();
    }

    public static String getCardholderId() {
        return CARDHOLDER_ID_PRE + IdUtils.getId();
    }

    public static String getCardApplyIdPre() {
        return CARD_APPLY_ID_PRE + IdUtils.getId();
    }

    public static String getCardIdPre() {
        return CARD_ID_PRE + IdUtils.getId();
    }

    public static String getFxOrderId(){
        return FX_ORDER_PRE + IdUtils.getId();
    }

    public static String getFxRefundOrderId(){
        return FX_ORDER_REFUND_PRE + IdUtils.getId();
    }

    public static String getFxCreditApplyId(){
        return FX_CREDIT_APPLY_PRE + IdUtils.getId();
    }

    public static String getWrongPaidId(){
        return FX_WRONG_PAID_PRE + IdUtils.getId();
    }

    public static String getFxCreditRefundOrderId(){
        return FX_CREDIT_REFUND_PRE + IdUtils.getId();
    }

    public static String getFxCreditApplyOrderId(){
        return FX_CREDIT_APPLY_ORDER + IdUtils.getId();
    }
    public static String getFxCreditApplyBatchOrderId(){
        return FX_CREDIT_APPLY_BAT_ORDER + IdUtils.getId();
    }
    public static String getFxCreditApplyOrderMeaningNo(){
        return FX_CREDIT_APPLY_ORDER_MEANING_NO + IdUtils.getId();
    }
}
