package com.fenbei.fx.card.service.cardcreditmanager.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class CardCreditManagerReturnReqDTO implements Serializable {
    /**
     * 员工
     */
    private String employeeId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 卡编码
     */
    @NotBlank
    private String fxCardId;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 事由
     */
    private String applyReasonDesc;

    /**
     * 是否为离职员工回收额度
     * true：是，false：不是
     */
    private boolean dismissionEmployee;
    /**
     * 关联的额度申请单和退还金额
     * key 为申请单ID
     * value 为金额
     */
    private Map<String, BigDecimal> returnAmount;
    /**
     *  退还总额,单位分
     */
    private BigDecimal refundCreditAmount;

    private Integer operationType = 4;
}
