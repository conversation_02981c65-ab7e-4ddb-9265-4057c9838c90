package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
public enum CardholderOperTypeEnum {
    EDIT(1, "编辑"),
    DELETE(2, "删除"),
    DISABLE(3, "禁用"),
    OPEN(4, "启用"),

    APPROVE(5, "审核"),
    DETAIL(6, "详情"),

    ;

    private final int key;
    private final String msg;
    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("编辑", "cardholder.oper.type.edit");
        I18N_KEY_MAP.put("删除", "cardholder.oper.type.delete");
        I18N_KEY_MAP.put("禁用", "cardholder.oper.type.disable");
        I18N_KEY_MAP.put("启用", "cardholder.oper.type.open");
        I18N_KEY_MAP.put("审核", "cardholder.oper.type.approve");
        I18N_KEY_MAP.put("详情", "cardholder.oper.type.detail");
    }

    public int getKey() {
        return key;
    }

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }

    CardholderOperTypeEnum(int key, String msg) {
        this.key = key;
        this.msg = msg;
    }
}
