package com.fenbei.fx.card.service.cardholderoperflow.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholderoperflow.CardholderOperFlowDAO;
import com.fenbei.fx.card.dao.cardholderoperflow.po.CardholderOperFlowPO;
import com.fenbei.fx.card.service.cardholderoperflow.domain.CardholderOperFlowDO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.converter.CardholderOperFlowConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 持卡人被操作流水 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardholderOperFlowManager extends BaseManagerImpl<CardholderOperFlowDAO, CardholderOperFlowPO, CardholderOperFlowDTO, CardholderOperFlowConverter> {

    public static CardholderOperFlowManager me() {
        return SpringUtil.getBean(CardholderOperFlowManager.class);
    }

    public List<CardholderOperFlowListResDTO> list(final CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO) {
        CardholderOperFlowDTO paramsDTO = CardholderOperFlowDO.me().buildListParamsDTO(cardholderOperFlowListReqDTO);

        List<CardholderOperFlowDTO> cardholderOperFlowDTOList = super.findList(paramsDTO);

        return CardholderOperFlowDO.me().transferCardholderOperFlowListResDTOList(cardholderOperFlowDTOList);
    }

    public CardholderOperFlowListResDTO listOne(final CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO) {
        CardholderOperFlowDTO paramsDTO = CardholderOperFlowDO.me().buildListParamsDTO(cardholderOperFlowListReqDTO);

        CardholderOperFlowDTO cardholderOperFlowDTO = super.findOne(paramsDTO);

        return CardholderOperFlowDO.me().transferCardholderOperFlowListResDTO(cardholderOperFlowDTO);
    }

    public Page<CardholderOperFlowPageResDTO> pagination(final CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO, final Integer current, final Integer size) {
        CardholderOperFlowDTO paramsDTO = CardholderOperFlowDO.me().buildPageParamsDTO(cardholderOperFlowPageReqDTO);

        Page<CardholderOperFlowDTO> cardholderOperFlowDTOPage = super.findPage(paramsDTO, current, size);

        return CardholderOperFlowDO.me().transferCardholderOperFlowPageResDTOPage(cardholderOperFlowDTOPage);
    }

    public Boolean add(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        CardholderOperFlowDO.me().checkCardholderOperFlowAddReqDTO(cardholderOperFlowAddReqDTO);

        CardholderOperFlowDTO addCardholderOperFlowDTO = CardholderOperFlowDO.me().buildAddCardholderOperFlowDTO(cardholderOperFlowAddReqDTO);

        return super.saveDTO(addCardholderOperFlowDTO);
    }

    public Boolean addAllColumn(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        CardholderOperFlowDO.me().checkCardholderOperFlowAddReqDTO(cardholderOperFlowAddReqDTO);

        CardholderOperFlowDTO addCardholderOperFlowDTO = CardholderOperFlowDO.me().buildAddCardholderOperFlowDTO(cardholderOperFlowAddReqDTO);

        return super.saveAllColumn(addCardholderOperFlowDTO);
    }

    public Boolean addBatchAllColumn(final List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList) {
        CardholderOperFlowDO.me().checkCardholderOperFlowAddReqDTOList(cardholderOperFlowAddReqDTOList);

        List<CardholderOperFlowDTO> addBatchCardholderOperFlowDTOList = CardholderOperFlowDO.me().buildAddBatchCardholderOperFlowDTOList(cardholderOperFlowAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardholderOperFlowDTOList);
    }

    public CardholderOperFlowShowResDTO show(final String id) {
        CardholderOperFlowDTO cardholderOperFlowDTO = super.findById(id);

        return CardholderOperFlowDO.me().transferCardholderOperFlowShowResDTO(cardholderOperFlowDTO);
    }

    public List<CardholderOperFlowShowResDTO> showByIds(final List<String> ids) {
        CardholderOperFlowDO.me().checkIds(ids);

        List<CardholderOperFlowDTO> cardholderOperFlowDTOList = super.findBatchIds(ids);

        return CardholderOperFlowDO.me().transferCardholderOperFlowShowResDTOList(cardholderOperFlowDTOList);
    }

    public Boolean modify(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        CardholderOperFlowDO.me().checkCardholderOperFlowModifyReqDTO(cardholderOperFlowModifyReqDTO);

        CardholderOperFlowDTO modifyCardholderOperFlowDTO = CardholderOperFlowDO.me().buildModifyCardholderOperFlowDTO(cardholderOperFlowModifyReqDTO);

        return super.modifyById(modifyCardholderOperFlowDTO);
    }

    public Boolean modifyAllColumn(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        CardholderOperFlowDO.me().checkCardholderOperFlowModifyReqDTO(cardholderOperFlowModifyReqDTO);

        CardholderOperFlowDTO modifyCardholderOperFlowDTO = CardholderOperFlowDO.me().buildModifyCardholderOperFlowDTO(cardholderOperFlowModifyReqDTO);

        return super.modifyAllColumnById(modifyCardholderOperFlowDTO);
    }

    public Boolean removeByParams(final CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO) {
        CardholderOperFlowDO.me().checkCardholderOperFlowRemoveReqDTO(cardholderOperFlowRemoveReqDTO);

        CardholderOperFlowDTO removeCardholderOperFlowDTO = CardholderOperFlowDO.me().buildRemoveCardholderOperFlowDTO(cardholderOperFlowRemoveReqDTO);

        return super.remove(removeCardholderOperFlowDTO);
    }

    @Override
    protected CardholderOperFlowPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardholderOperFlowPO();
        }

        return BeanUtil.toBean(map, CardholderOperFlowPO.class);
    }

    @Override
    protected CardholderOperFlowDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardholderOperFlowDTO();
        }

        return BeanUtil.toBean(map, CardholderOperFlowDTO.class);
    }
}
