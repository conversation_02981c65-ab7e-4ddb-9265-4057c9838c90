package com.fenbei.fx.card.service.card.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.dao.card.CardDAO;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.service.card.converter.CardConverter;
import com.fenbei.fx.card.service.card.domain.CardDO;
import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageResDTO;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.fenbei.fx.card.service.cardholder.manager.CardholderManager;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbei.fx.card.util.*;
import com.fenbeitong.dech.api.model.dto.airwallex.AirCardDetailsRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.AirUpdateCardRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianCaptchaApplyRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianPhysicalCardActiveRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianPhysicalResetPinRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianCaptchaApplyRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianCardModifyRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.req.LianLianCardModifyRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianPhysicalCardActiveRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.lianlian.card.resp.LianLianPhysicalCardResetPinRpcRespDTO;
import com.fenbeitong.dech.api.service.airwallex.IAirWallexCardService;
import com.fenbeitong.dech.api.service.lianlian.ILianLianCardService;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.constant.FinhubMessageType;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;

/**
 * 国际卡 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Slf4j
@Component
public class CardManager extends BaseManagerImpl<CardDAO, CardPO, CardDTO, CardConverter> {

    @DubboReference
    private IAirWallexCardService airWallexCardService;

    @DubboReference
    private ILianLianCardService iLianLianCardService;

    @Autowired
    private CardholderManager cardholderManager;

    @Autowired
    private CardApplyManager cardApplyManager;

    @Autowired
    private CardDAO cardDAO;


    public static CardManager me() {
        return SpringUtil.getBean(CardManager.class);
    }

    public CardListResDTO listOne(final CardListReqDTO cardListReqDTO) {
        CardDTO paramsDTO = CardDO.me().buildListParamsDTO(cardListReqDTO);

        CardDTO cardDTO = super.findOne(paramsDTO);

        return CardDO.me().transferCardListResDTO(cardDTO);
    }

    public Page<CardPageResDTO> mergePagination(final CardPageReqDTO cardPageReqDTO, final Integer current, final Integer size){
        if (CardApplyStatusEnum.getApplyStatus(cardPageReqDTO.getCardStatus()) != null) {
            List<CardPageResDTO> resDTOS = new ArrayList<>();
            CardApplyPageReqDTO reqDTO = new CardApplyPageReqDTO();
            BeanUtils.copyProperties(cardPageReqDTO, reqDTO);
            reqDTO.setApplyStatus(cardPageReqDTO.getCardStatus());
            Page<CardApplyPageResDTO> applyListResDTOS = cardApplyManager.pagination(reqDTO, current, size);
            if (!CollectionUtils.isEmpty(applyListResDTOS.getRecords())) {
                for (CardApplyPageResDTO cardApplyListResDTO : applyListResDTOS.getRecords()) {
                    CardPageResDTO dto = new CardPageResDTO();
                    BeanUtils.copyProperties(cardApplyListResDTO, dto);

                    CardApplyStatusEnum  relationEnum= CardApplyStatusEnum.getRelationEnum(cardApplyListResDTO.getApplyStatus());
                    dto.setCardStatusStr(relationEnum.getName());
                    dto.setCardStatus(relationEnum.getStatus());

                    CardCanOperationDTO canOperationDTO = CardStatusEnum.getCarCanOperateStatus(dto.getCardStatus());
                    dto.setOperationDTO(canOperationDTO);
                    if (!org.springframework.util.StringUtils.isEmpty(dto.getCardLimits())){
                        dto.setShowLimits(JSONObject.parseArray(dto.getCardLimits(), BaseAirwallexRpcDTO.Limit.class));
                    }
                    dto.setPhone(cardApplyManager.getPhone(dto.getFxCardId()));
                    dto.setShowUSDBalance(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(dto.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(dto.getBalance())));

                    CardFormFactorEnum formFactor = CardFormFactorEnum.getFormFactor(dto.getCardFormFactor());
                    if(Objects.nonNull(formFactor)){
                        dto.setCardFormFactorDesc(formFactor.getName());
                    }

                    PurposeEnum purposeEnum = PurposeEnum.getEnum(dto.getCardPurpose());
                    if(Objects.nonNull(purposeEnum)){
                        dto.setCardPurposeDesc(purposeEnum.getMsg());
                    }
                    CardPlatformEnum platform = CardPlatformEnum.getPlatform(dto.getCardPlatform());
                    if(Objects.nonNull(platform)){
                        dto.setCardPlatformName(platform.getName());
                    }
                    resDTOS.add(dto);
                }
            }
            Page<CardPageResDTO> page = new Page<>();
            page.setCurrent(current);
            page.setSize(size);
            page.setTotal(applyListResDTOS.getTotal());
            page.setRecords(resDTOS);
            return page;
        } else {
            return pagination(cardPageReqDTO, current, size);
        }
    }

    public Page<CardPageResDTO> pagination(final CardPageReqDTO cardPageReqDTO, final Integer current, final Integer size) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(cardPageReqDTO.getCompanyId())){
            queryWrapper.eq(CardPO.DB_COL_COMPANY_ID, cardPageReqDTO.getCompanyId());
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getCardPlatform())){
            queryWrapper.eq(CardPO.DB_COL_CARD_PLATFORM, cardPageReqDTO.getCardPlatform());
        }
        if (cardPageReqDTO.getCardStatus() != null) {
            queryWrapper.in(CardPO.DB_COL_CARD_STATUS, CardStatusEnum.getApplyStatus(cardPageReqDTO.getCardStatus()));
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getCardPurpose())) {
            queryWrapper.eq(CardPO.DB_COL_CARD_PURPOSE, cardPageReqDTO.getCardPurpose());
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getBankCardNo())) {
            queryWrapper.like(CardPO.DB_COL_BANK_CARD_NO, cardPageReqDTO.getBankCardNo());
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getNameOnCard())) {
            queryWrapper.like(CardPO.DB_COL_NAME_ON_CARD, cardPageReqDTO.getNameOnCard());
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getPhone())){
            //查询申请信息
            List<String> fxCardList = CardApplyManager.me().getFxCardIdByPhoneInfo(cardPageReqDTO.getPhone());
            if (CollectionUtils.isEmpty(fxCardList)){
                queryWrapper.eq(CardPO.DB_COL_FX_CARD_ID,"null");
            }else {
                queryWrapper.in(CardPO.DB_COL_FX_CARD_ID,fxCardList);
            }
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getFxCardholderId())) {
            queryWrapper.eq(CardPO.DB_COL_FX_CARDHOLDER_ID, cardPageReqDTO.getFxCardholderId());
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getCarPublicTimeStart())) {
            queryWrapper.ge(CardPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardPageReqDTO.getCarPublicTimeStart()));
        }
        if (StringUtils.isNotBlank(cardPageReqDTO.getCarPublicTimeEnd())) {
            queryWrapper.le(CardPO.DB_COL_CREATE_TIME, com.fenbeitong.finhub.common.utils.DateUtils.parseTime(cardPageReqDTO.getCarPublicTimeEnd()));
        }
        queryWrapper.orderByDesc(CardPO.DB_COL_UPDATE_TIME);
        Page<CardDTO> cardDTOPage = super.findPage(queryWrapper, current, size);

        Page<CardPageResDTO> resDTOS = CardDO.me().transferCardPageResDTOPage(cardDTOPage);
        if (!CollectionUtils.isEmpty(resDTOS.getRecords())) {
            for (CardPageResDTO dto : resDTOS.getRecords()) {
                CardCanOperationDTO canOperationDTO = CardStatusEnum.getCarCanOperateStatus(dto.getCardStatus());
                dto.setOperationDTO(canOperationDTO);
                dto.setCardStatusStr(CardStatusEnum.getShowStatusStr(dto.getCardStatus()));
                dto.setCardStatus(CardStatusEnum.getShowStatus(dto.getCardStatus()));
                dto.setPhone(CardApplyManager.me().getPhone(dto.getFxCardId()));
                if (!org.springframework.util.StringUtils.isEmpty(dto.getCardLimits())) {
                    dto.setShowLimits(JSONObject.parseArray(dto.getCardLimits(), BaseAirwallexRpcDTO.Limit.class));
                }
                dto.setShowUSDBalance(CurrencyNumberFormatUtil.moneyFormart(CurrencyEnum.getCurrencyByCodeIgnoreCase(dto.getCurrency()),BigDecimalUtils.fen2yuan(dto.getBalance())));

                CardFormFactorEnum formFactor = CardFormFactorEnum.getFormFactor(dto.getCardFormFactor());
                if(Objects.nonNull(formFactor)){
                    dto.setCardFormFactorDesc(formFactor.getName());
                }

                PurposeEnum purposeEnum = PurposeEnum.getEnum(dto.getCardPurpose());
                if(Objects.nonNull(purposeEnum)){
                    dto.setCardPurposeDesc(purposeEnum.getMsg());
                }
                CardPlatformEnum platform = CardPlatformEnum.getPlatform(dto.getCardPlatform());
                if(Objects.nonNull(platform)){
                    dto.setCardPlatformName(platform.getName());
                }
            }
        }
        return resDTOS;
    }

    public void dayCheckTask() {
        Integer current = 0;
        while (true) {
            Page<CardDTO> cardDTOPage = queryAllCardByPage(current, 100);
            if (cardDTOPage != null && !CollectionUtils.isEmpty(cardDTOPage.getRecords())) {
                //请求airwallet  拉取更新处理
                for (CardDTO cardDTO : cardDTOPage.getRecords()) {
                    cardCheckDetail(cardDTO);
                }
            } else {
                break;
            }
            current = current + 1;
        }
    }


    public void cardCheckDetail(CardDTO cardDTO) {
        /**  更新覆盖card表  */
        try {
            AirCardDetailsRpcRespDTO detailsRpcRespDTO = airWallexCardService.getCardDetails(cardDTO.getBankCardId());

            CardPO cardPO = buildCardInfo(cardDTO, detailsRpcRespDTO);

            CardManager.me().saveOrUpdate(cardPO);

        } catch (Exception es) {
            log.error("每日更新任务异常 卡信息req = {}", JSON.toJSONString(cardDTO), es);
        }
    }


    private CardPO buildCardInfo(CardDTO cardDTO, AirCardDetailsRpcRespDTO dto) {
        CardPO cardPO = CopyUtils.convert(cardDTO, CardPO.class);
        CardStatusEnum cardStatusEnum = CardStatusEnum.getAirWallexEnum(dto.getCard_status());
        cardPO.setCardStatus(cardStatusEnum.getStatus());
        cardPO.setBankCardId(dto.getCard_id());
        cardPO.setBankCardNo(dto.getCard_number());
        cardPO.setCardBrand(dto.getBrand());
        cardPO.setCardPurpose(dto.getPurpose());
        BaseAirwallexRpcDTO.TransactionLimits limits = dto.getAuthorization_controls().getTransaction_limits();
        cardPO.setCurrency(limits.getCurrency());
        cardPO.setCardLimits(JSON.toJSONString(limits));

        return cardPO;
    }


    public Page<CardDTO> queryAllCardByPage(Integer current, Integer size) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        Page<CardDTO> cardDTOPage = super.findPage(queryWrapper, current, size);
        return cardDTOPage;
    }

    public Boolean add(final CardAddReqDTO cardAddReqDTO) {
        CardDO.me().checkCardAddReqDTO(cardAddReqDTO);

        CardDTO addCardDTO = CardDO.me().buildAddCardDTO(cardAddReqDTO);

        return super.saveDTO(addCardDTO);
    }

    public Boolean addAllColumn(final CardAddReqDTO cardAddReqDTO) {
        CardDO.me().checkCardAddReqDTO(cardAddReqDTO);

        CardDTO addCardDTO = CardDO.me().buildAddCardDTO(cardAddReqDTO);

        return super.saveAllColumn(addCardDTO);
    }

    public Boolean addBatchAllColumn(final List<CardAddReqDTO> cardAddReqDTOList) {
        CardDO.me().checkCardAddReqDTOList(cardAddReqDTOList);

        List<CardDTO> addBatchCardDTOList = CardDO.me().buildAddBatchCardDTOList(cardAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardDTOList);
    }

    public CardShowResDTO cardDetail(final String fxCardId) {
        CardDTO cardDTO = getByfxCardId(fxCardId);
        CardApplyDTO cardApplyDTO = CardApplyManager.me().queryCardApplyByFxCardId(fxCardId);
        CardShowResDTO cardShowResDTO = CardDO.me().transferCardShowResDTO(cardDTO);
        if (ObjectUtils.isEmpty(cardShowResDTO)){
            //兼容查询一下卡申请列表
            if (ObjectUtils.isNotEmpty(cardApplyDTO)){
                cardShowResDTO = CopyUtils.convert(cardApplyDTO,CardShowResDTO.class);
            }
        }else {
            //兼容修改重新提交
            cardShowResDTO.setApplyerFirstName(cardApplyDTO.getApplyerFirstName());
            cardShowResDTO.setApplyerLastName(cardApplyDTO.getApplyerLastName());
        }
        if (ObjectUtils.isEmpty(cardDTO)){
            cardShowResDTO.setCardStatusStr(CardApplyStatusEnum.getRelationEnum(cardApplyDTO.getApplyStatus()).getName());
            cardShowResDTO.setCardStatus(CardApplyStatusEnum.getRelationEnum(cardApplyDTO.getApplyStatus()).getStatus());
            CardCanOperationDTO canOperationDTO = CardStatusEnum.getCarCanOperateStatus(cardShowResDTO.getCardStatus());
            cardShowResDTO.setOperationDTO(canOperationDTO);
        }else {
            cardShowResDTO.setCardStatusStr(CardStatusEnum.getShowStatusStr(cardShowResDTO.getCardStatus()));
            cardShowResDTO.setCardStatus(CardStatusEnum.getShowStatus(cardShowResDTO.getCardStatus()));
            CardCanOperationDTO canOperationDTO = CardStatusEnum.getCarCanOperateStatus(cardShowResDTO.getCardStatus());
            cardShowResDTO.setOperationDTO(canOperationDTO);
        }
        if (StringUtils.isNotBlank(cardShowResDTO.getCardLimits())) {
            cardShowResDTO.setShowLimits(JSONObject.parseArray(cardShowResDTO.getCardLimits(), BaseAirwallexRpcDTO.Limit.class));
        }
        cardShowResDTO.setMaskBankCardNo(MaskUtils.leftData(cardShowResDTO.getBankCardNo(),4));

        /*添加持卡人地址信息*/
        if (CardFormFactorEnum.isPhysical(cardShowResDTO.getCardFormFactor())) {
            if (CardPlatformEnum.isLianLian(cardApplyDTO.getCardPlatform())){
                cardShowResDTO.setCardHolderName(cardApplyDTO.getApplyerFirstName() + cardApplyDTO.getApplyerLastName());
                cardShowResDTO.setNationCode(cardApplyDTO.getNationCode());
                cardShowResDTO.setPhone(cardApplyDTO.getApplyerPhone());
                cardShowResDTO.setPostalAddress(cardApplyDTO.getPostalAddress());
                cardShowResDTO.setPostalAddressDto(JsonUtils.toObj(cardApplyDTO.getPostalAddress(), AddressDto.class));
            }else {
                CardholderDTO cardholderDTO = cardholderManager.findByCardholderId(cardShowResDTO.getFxCardholderId());
                cardShowResDTO.setCardHolderName(cardholderDTO.getFirstName() + cardholderDTO.getLastName());
                cardShowResDTO.setNationCode(cardholderDTO.getNationCode());
                cardShowResDTO.setPhone(cardholderDTO.getPhone());
                cardShowResDTO.setPostalAddress(cardholderDTO.getPostalAddress());
                cardShowResDTO.setAddress(cardholderDTO.getAddress());
                cardShowResDTO.setAddressDto(JsonUtils.toObj(cardholderDTO.getAddress(), AddressDto.class));
                cardShowResDTO.setPostalAddressDto(JsonUtils.toObj(cardholderDTO.getPostalAddress(), AddressDto.class));
            }
        }else {
            cardShowResDTO.setPhone(cardApplyDTO.getApplyerPhone());
            cardShowResDTO.setCardHolderName(cardApplyDTO.getApplyerFirstName() + cardApplyDTO.getApplyerLastName());
        }
        //赋值applyId  兼容修改
        cardShowResDTO.setApplyId(cardApplyDTO.getApplyId());
        CurrencyEnum currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(cardApplyDTO.getCurrency());
        if (currencyEnum == null || Objects.equals(currencyEnum.getCurrencyCode(), CurrencyEnum.UNKNOW.getCurrencyCode())) {
            currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(cardDTO.getCurrency());
        }
        if (!Objects.isNull(currencyEnum)){
            cardShowResDTO.setCurrencySymbol(currencyEnum.getSymbol());
            cardShowResDTO.setCurrencyName(currencyEnum.getDisplayName());
            cardShowResDTO.setCurrency(currencyEnum.getCurrencyCode());
        }
        CardPlatformEnum platform = CardPlatformEnum.getPlatform(cardDTO.getCardPlatform());
        cardShowResDTO.setCardPlatformIcon(platform.getPlatformIcon());
        cardShowResDTO.setCardPlatformName(platform.getName());
        CardBrandEnum brandAndNoBrand = CardBrandEnum.getBrandAndNoBrand(cardDTO.getCardBrand());
        cardShowResDTO.setCardBrandIcon(brandAndNoBrand.getBrandIcon());
        return cardShowResDTO;
    }

    public CardDTO cardDetailByCardId(String cardId){
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_BANK_CARD_ID, cardId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    public CardDTO cardDetailByFxCardId(String fxCardId){
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_FX_CARD_ID, fxCardId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }
    public CardDTO cardDetailByBankAccountNo(String bankAccountNo){
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_BANK_CARD_NO, bankAccountNo);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    public List<CardDTO> queryByEmployeeId(final String employeeId){
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findList(queryWrapper);
    }

    public List<CardShowResDTO> showByIds(final List<String> ids) {
        CardDO.me().checkIds(ids);

        List<CardDTO> cardDTOList = super.findBatchIds(ids);

        return CardDO.me().transferCardShowResDTOList(cardDTOList);
    }

    public Boolean modify(final CardModifyReqDTO cardModifyReqDTO) {
        CardDO.me().checkCardModifyReqDTO(cardModifyReqDTO);

        CardDTO modifyCardDTO = CardDO.me().buildModifyCardDTO(cardModifyReqDTO);

        return super.modifyById(modifyCardDTO);
    }

    public Boolean modifyAllColumn(final CardModifyReqDTO cardModifyReqDTO) {
        CardDO.me().checkCardModifyReqDTO(cardModifyReqDTO);

        CardDTO modifyCardDTO = CardDO.me().buildModifyCardDTO(cardModifyReqDTO);

        return super.modifyAllColumnById(modifyCardDTO);
    }

    public Boolean removeByParams(final CardRemoveReqDTO cardRemoveReqDTO) {
        CardDO.me().checkCardRemoveReqDTO(cardRemoveReqDTO);

        CardDTO removeCardDTO = CardDO.me().buildRemoveCardDTO(cardRemoveReqDTO);

        return super.remove(removeCardDTO);
    }

    public CardDTO getByfxCardId(String cardId) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_FX_CARD_ID, cardId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }

    public CardDTO getByEmployeeId(String employeeId) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        return this.findOne(queryWrapper);
    }
    public CardDTO getByBankCardId(String cardId) {
        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_BANK_CARD_ID, cardId);
        queryWrapper.eq(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        CardDTO cardDTO = this.findOne(queryWrapper);
        return cardDTO != null ? cardDTO : new CardDTO();
    }

    @Override
    protected CardPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardPO();
        }

        return BeanUtil.toBean(map, CardPO.class);
    }

    @Override
    protected CardDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardDTO();
        }

        return BeanUtil.toBean(map, CardDTO.class);
    }


    public List<CardDTO> userActiveCards(String employeeId, String companyId) {
        if (StringUtils.isAnyBlank(employeeId, companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.eq(CardPO.DB_COL_CARD_STATUS, CardStatusEnum.ACTIVE.getStatus());
        queryWrapper.orderByDesc(CardPO.DB_COL_CREATE_TIME);
        return super.findList(queryWrapper);

    }

    public List<CardDTO> userAllCards(String employeeId, String companyId) {
        if (StringUtils.isAnyBlank(employeeId, companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.eq(CardPO.DB_COL_EMPLOYEE_ID, employeeId);
        queryWrapper.in(CardPO.DB_COL_DELETE_FLAG, DeleteFlagEnum.NORMAL.getCode());
        queryWrapper.orderByAsc(CardPO.DB_COL_CARD_STATUS);
        queryWrapper.orderByDesc(CardPO.DB_COL_CREATE_TIME);
        return super.findList(queryWrapper);

    }


    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Boolean updateCardStatus(UpdateCardStatusReqDTO reqDTO) {

        try {
            // 检查看状态
            CardDTO card = CardManager.me().getByfxCardId(reqDTO.getFxCardId());
            if (ObjectUtils.isEmpty(card)) {
                throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
            }

            // 更新airwallex
            AirUpdateCardRpcReqDTO updateReq = new AirUpdateCardRpcReqDTO();
            LianLianCardModifyRpcReqDTO updateReq4LianLian = new LianLianCardModifyRpcReqDTO();
            String toCardStatus = "";
            if (reqDTO.getCardStatus() == 1) {
                // 启用
                if (!card.getCardStatus().equals(CardStatusEnum.DISABLE.getStatus())
                    && !card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())) {
                    throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO, I18nUtils.getMessage("CM_DJ_JY_ZT_CAN_ENABLE"));
                }
                toCardStatus = CardStatusEnum.ACTIVE.getCode();
                updateReq.setCard_status(CardStatusEnum.ACTIVE.getCode());
                updateReq4LianLian.setAccountStatus(CardStatusEnum.ACTIVE.getLianLianCode());
                card.setCardStatus(CardStatusEnum.ACTIVE.getStatus());
            } else if (reqDTO.getCardStatus() == 2) {
                //禁用
                if (!card.getCardStatus().equals(CardStatusEnum.ACTIVE.getStatus())) {
                    throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO, I18nUtils.getMessage("CM_SXZ_CAN_DISABLE"));
                }
                toCardStatus = CardStatusEnum.DISABLE.getCode();
                updateReq.setCard_status(CardStatusEnum.DISABLE.getCode());
                //连连无禁用暂时保留注释
                //updateReq4LianLian.setAccountStatus(CardStatusEnum.ACTIVE.getLianLianCode());
                card.setCardStatus(CardStatusEnum.DISABLE.getStatus());
            } else if (reqDTO.getCardStatus() == 3) {
                //注销
                if (!card.getCardStatus().equals(CardStatusEnum.PENDING.getStatus())
                    && !card.getCardStatus().equals(CardStatusEnum.ACTIVE.getStatus())
                    && !card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())) {
                    throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO, I18nUtils.getMessage("CM_SHZ_SXZ_DJ_CAN_ZX"));

                }
                toCardStatus = CardStatusEnum.LOGOUT.getCode();
                updateReq.setCard_status(CardStatusEnum.LOGOUT.getCode());
                updateReq4LianLian.setAccountStatus(CardStatusEnum.LOGOUT.getLianLianCode());
                card.setCardStatus(CardStatusEnum.LOGOUT.getStatus());
            }

            if (reqDTO.getCardStatus() == 3) { //只有注销才去请求airwallex/lianlian
                if(CardPlatformEnum.isAirwallex(card.getCardPlatform())){
                    logOut4AirWallex(card, updateReq, toCardStatus);
                }else if (CardPlatformEnum.isLianLian(card.getCardPlatform())){
                    logOut4LianLian(card, updateReq4LianLian);
                }else{
                    log.info("updateCardStatus req,no support cardPlatform {}", JSON.toJSONString(updateReq));
                }

            }
            // 更新DB卡状态
            CardPO po = CopyUtils.convert(card, CardPO.class);
            CardManager.me().saveOrUpdate(po);
            return true;
        } catch (FxCardException e) {
            throw e;
        } catch (Exception e) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }
    }


    private void logOut4LianLian(CardDTO card,LianLianCardModifyRpcReqDTO updateReq) {
        updateReq.setMchId(card.getBankMchId());
        updateReq.setAccountNo(card.getBankCardId());
        try {
            log.info("lianLianCardService updateCardStatus req {}", JSON.toJSONString(updateReq));
            LianLianCardModifyRpcRespDTO modify = iLianLianCardService.modify(updateReq);
            log.info("lianLianCardService updateCardStatus resp {}", JSON.toJSONString(modify));
            if (ObjectUtils.isEmpty(modify)) {
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_NOT_MATCH);
            }else if(modify.success()&&modify.getModifyRequestStatus()){
                log.info("lianLianCardService updateCardStatus success {}", JSON.toJSONString(modify));
            }else{
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_NOT_MATCH);
            }
        } catch (FxCardException fxCardException){
            throw fxCardException;
        } catch (Exception e) {
            log.error("lianLianCardService updateCardStatus error{}", JSON.toJSONString(updateReq),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }
    }

    private void logOut4AirWallex(CardDTO card, AirUpdateCardRpcReqDTO updateReq, String toCardStatus) {
        try {
            log.info("airWallexCardService updateCardStatus req {}", JSON.toJSONString(updateReq));
            AirCardDetailsRpcRespDTO airCardDetailsRpcRespDTO = airWallexCardService.updateCard(card.getBankCardId(), updateReq);
            log.info("airWallexCardService updateCardStatus resp {}", JSON.toJSONString(airCardDetailsRpcRespDTO));
            if (!toCardStatus.equals(airCardDetailsRpcRespDTO.getCard_status())) {
                throw new FxCardException(GlobalCoreResponseCode.AIR_REQUEST_STATUS_NOT_MATCH);
            }
        } catch (FxCardException fxCardException){
            throw fxCardException;
        } catch (Exception e) {
            log.error("airWallexCardService updateCardStatus error{}", JSON.toJSONString(updateReq),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }
    }

    public Boolean updateLimits (UpdateCardStatusReqDTO updateCardStatusReqDTO){
        CardDTO card = CardManager.me().getByfxCardId(updateCardStatusReqDTO.getFxCardId());
        if (ObjectUtils.isEmpty(card)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
        }
        if (CardStatusEnum.ACTIVE.getStatus() != card.getActiveStatus() && CardStatusEnum.FREEZE.getStatus() != card.getActiveStatus()){
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        try {
            AirUpdateCardRpcReqDTO updateReq = new AirUpdateCardRpcReqDTO();
            BaseAirwallexRpcDTO.AuthorizationControls authorizationControls = new BaseAirwallexRpcDTO.AuthorizationControls();
            BaseAirwallexRpcDTO.TransactionLimits limits =new BaseAirwallexRpcDTO.TransactionLimits();
            limits.setCurrency(updateCardStatusReqDTO.getCurrency());
            limits.setLimits(updateCardStatusReqDTO.getCardLimits());
            authorizationControls.setAllowed_transaction_count("MULTIPLE");
            authorizationControls.setTransaction_limits(limits);
            updateReq.setAuthorization_controls(authorizationControls);
            log.info("airWallexCardService updateCardStatus req {}", JSON.toJSONString(updateReq));
            AirCardDetailsRpcRespDTO airCardDetailsRpcRespDTO = airWallexCardService.updateCard(card.getFxCardId(), updateReq);
            log.info("airWallexCardService updateCardStatus resp {}", JSON.toJSONString(airCardDetailsRpcRespDTO));
            if (airCardDetailsRpcRespDTO != null) {
                // 更新DB卡状态
                CardPO po = CopyUtils.convert(card, CardPO.class);
                po.setCardLimits(JSON.toJSONString(airCardDetailsRpcRespDTO.getAuthorization_controls().getTransaction_limits().getLimits()));

                CardManager.me().saveOrUpdate(po);
            }
        } catch (Exception e) {
            log.error("airWallexCardService updateLimits error{}", JSON.toJSONString(updateCardStatusReqDTO),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }

        return true;
    }

    public Boolean activateCard(ActivateCardReqDTO reqDTO) {
        try {
            // 检查看状态
            CardDTO card = CardManager.me().getByfxCardId(reqDTO.getFxCardId());
            if (ObjectUtils.isEmpty(card)) {
                throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
            }

            // 更新airwallex
            try {
                log.info("airWallexCardService activateCard bankCardId {}", JSON.toJSONString(card.getBankCardId()));
                /*airWallexCardService.activateCard(card.getBankCardId());
                card.setCardStatus(CardStatusEnum.ACTIVE.getStatus());*/
                card.setActiveStatus(CardActiveStatusEnum.SUCCESS.getStatus());
            } catch (Exception e) {
                throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
            }
            // 更新DB卡状态
            CardPO po = CopyUtils.convert(card, CardPO.class);
            CardManager.me().saveOrUpdate(po);
//            // 启用调用开卡计费
//            CardChargingNoticeDTO cardChargingNoticeDTO = buildCharingNotice(po);
//            cardChargingNoticeService.saveChargingNotice(cardChargingNoticeDTO);
            return true;
        } catch (Exception e) {
            log.error("airWallexCardService activateCard error{}", JSON.toJSONString(reqDTO),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }
    }

    /**
     * 查询公司特定渠道下的币种余额
     * @param companyId
     * @param platform
     * @return
     */
    public List<Map<String, Object>> cardAllBalance(String companyId, String platform) {
        if (StringUtils.isAnyBlank(platform, companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("currency,IFNULL(sum(balance),0) as 'allBalance'")
            .eq(CardPO.DB_COL_CARD_PLATFORM, platform)
            .eq(CardPO.DB_COL_COMPANY_ID,companyId)
            .groupBy("currency")
            .orderByAsc("currency");

        return this.listMaps(queryWrapper);

    }

    /**
     * 查询企业下所有员工帐户中有金额的（余额和冻结余额）
     * @param companyId
     * @return
     */
    public Integer getCardBalanceGreaterZeroByCompanyId(String companyId) {
        if (StringUtils.isAnyBlank(companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        QueryWrapper<CardPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardPO.DB_COL_COMPANY_ID, companyId);
        queryWrapper.gt(CardPO.DB_COL_BALANCE, 0);
        queryWrapper.gt(CardPO.DB_COL_FREEZEN_BALANCE, 0);
        return super.findCount(queryWrapper);

    }

    /**
     * 获取卡汇总余额
     * @param companyId
     * @return
     */
    @Deprecated
    public CardSumBalanceResDTO cardSumBalance(String companyId) {
        if (StringUtils.isAnyBlank(companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        CardSumBalanceResDTO cardSumBalanceResDTO = new CardSumBalanceResDTO();
        List<Map<String, Object>> cardSumBalanceList = cardDAO.getCardSumBalance(companyId);
        if (Objects.isNull(cardSumBalanceList)){
            return cardSumBalanceResDTO;
        }

        cardSumBalanceList.forEach(p->{
            String currency = p.get("currency").toString();
            if(Objects.equals(currency, CurrencyEnum.USD.getCurrencyCode())){
                BigDecimal sumBalance = (BigDecimal)p.get("sumBalance");
                String showUSDSumBalance = CurrencyNumberFormatUtil.moneyFormart(CurrencyEnum.getCurrencyByCodeIgnoreCase(currency),BigDecimalUtils.fen2yuan(sumBalance));
                cardSumBalanceResDTO.setCurrency(currency);
                cardSumBalanceResDTO.setSumBalance(sumBalance);
                cardSumBalanceResDTO.setShowUSDSumBalance(showUSDSumBalance);
                return;
            }
        });

        return cardSumBalanceResDTO;
    }


    public List<CardSumBalanceListResDTO> cardSumBalanceList(String companyId) {
        if (StringUtils.isAnyBlank(companyId)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        List<CardSumBalanceListResDTO> cardSumBalanceResDTOS=new ArrayList<>();

        List<Map<String, Object>> cardSumBalanceList = cardDAO.getCardSumBalance(companyId);
        if (Objects.isNull(cardSumBalanceList)){
            return cardSumBalanceResDTOS;
        }
        cardSumBalanceList.forEach(p->{
            CardSumBalanceListResDTO cardSumBalanceResDTO = new CardSumBalanceListResDTO();
            String currency = p.get("currency").toString();
            BigDecimal sumBalance = (BigDecimal)p.get("sumBalance");
            String showUSDSumBalance = CurrencyNumberFormatUtil.moneyFormart(CurrencyEnum.getCurrencyByCodeIgnoreCase(currency),BigDecimalUtils.fen2yuan(sumBalance));
            cardSumBalanceResDTO.setCurrency(currency);
            cardSumBalanceResDTO.setSumBalance(sumBalance);
            cardSumBalanceResDTO.setShowSumBalance(showUSDSumBalance);
            cardSumBalanceResDTOS.add(cardSumBalanceResDTO);
        });
        return cardSumBalanceResDTOS;


    }

    public CardCaptchaApplyResDTO captchaApply(CardCaptchaApplyReqDTO cardCaptchaApplyReqDTO) {
        //检查看状态
        CardDTO card = CardManager.me().getByfxCardId(cardCaptchaApplyReqDTO.getFxCardId());
        if (ObjectUtils.isEmpty(card)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
        }
        // 不可用
        if (card.getCardStatus().equals(CardStatusEnum.DISABLE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.EXPIRED.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.LOGOUT.getStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        String token = captchaApply4LianLian(card,cardCaptchaApplyReqDTO);
        CardCaptchaApplyResDTO captchaApplyResDTO= new CardCaptchaApplyResDTO();
        captchaApplyResDTO.setFxCardId(card.getFxCardId());
        captchaApplyResDTO.setToken(token);
        return captchaApplyResDTO;
    }

    private String captchaApply4LianLian(CardDTO card,CardCaptchaApplyReqDTO cardCaptchaApplyReqDTO) {
        LianLianCaptchaApplyRpcReqDTO lianLianCaptchaApplyRpcReqDTO = new LianLianCaptchaApplyRpcReqDTO();
        lianLianCaptchaApplyRpcReqDTO.setMchId(card.getBankMchId());
        lianLianCaptchaApplyRpcReqDTO.setUserId(card.getCreateUserId());
        lianLianCaptchaApplyRpcReqDTO.setBusiType(cardCaptchaApplyReqDTO.getBusiType());
        lianLianCaptchaApplyRpcReqDTO.setSendType(cardCaptchaApplyReqDTO.getSendType());
        try {
            log.info("lianLianCardService captchaApply4LianLian req {}", JSON.toJSONString(lianLianCaptchaApplyRpcReqDTO));
            LianLianCaptchaApplyRpcRespDTO lianCaptchaApplyRpcRespDTO = iLianLianCardService.captchaApply(lianLianCaptchaApplyRpcReqDTO);
            log.info("lianLianCardService captchaApply4LianLian resp {}", JSON.toJSONString(lianCaptchaApplyRpcRespDTO));
            if (ObjectUtils.isEmpty(lianCaptchaApplyRpcRespDTO)) {
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_NOT_MATCH);
            }else if(lianCaptchaApplyRpcRespDTO.success()&&StringUtils.isNotBlank(lianCaptchaApplyRpcRespDTO.getToken())){
                log.info("lianLianCardService captchaApply4LianLian success {}", JSON.toJSONString(lianCaptchaApplyRpcRespDTO));
                return lianCaptchaApplyRpcRespDTO.getToken();
            }else{
                log.info("lianLianCardService captchaApply4LianLian failed {}", JSON.toJSONString(lianCaptchaApplyRpcRespDTO));
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_NOT_MATCH);
            }
        } catch (FxCardException fxCardException){
            throw fxCardException;
        } catch (Exception e) {
            log.error("lianLianCardService captchaApply4LianLian error{}", JSON.toJSONString(lianLianCaptchaApplyRpcReqDTO),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }
    }

    public Boolean physcardActivate(CardPhyscardActiveReqDTO cardPhyscardActiveReqDTO) {
        CardPhyscardActiveResDTO activeResDTO = new CardPhyscardActiveResDTO();
        activeResDTO.setFxCardId(cardPhyscardActiveReqDTO.getFxCardId());
        //检查卡状态
        CardDTO card = CardManager.me().getByfxCardId(cardPhyscardActiveReqDTO.getFxCardId());
        if (ObjectUtils.isEmpty(card)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        // 不可用
        if (card.getCardStatus().equals(CardStatusEnum.DISABLE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.EXPIRED.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.LOGOUT.getStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        //检查pin
        String cardPin = cardPhyscardActiveReqDTO.getCardPin();
        int length = cardPin.length();
        if(length!=4){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        try{
            Integer cardPinInt = Integer.getInteger(cardPin);
        }catch (Exception e){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        //检查验证码
        String verifyCode = cardPhyscardActiveReqDTO.getVerifyCode();
        int lengthVer = verifyCode.length();
        if(lengthVer!=6){
            throw new FxCardException(GlobalCoreResponseCode.CARD_VERIFYCODE_NOT_DATA);
        }

        LianLianPhysicalCardActiveRpcReqDTO lianLianPhysicalCardActiveRpcReqDTO=new LianLianPhysicalCardActiveRpcReqDTO();
        lianLianPhysicalCardActiveRpcReqDTO.setMchId(card.getBankMchId());
        lianLianPhysicalCardActiveRpcReqDTO.setAccountNo(card.getBankCardId());
        lianLianPhysicalCardActiveRpcReqDTO.setCardCvv(cardPhyscardActiveReqDTO.getCardCvv());
        lianLianPhysicalCardActiveRpcReqDTO.setToken(cardPhyscardActiveReqDTO.getToken());
        lianLianPhysicalCardActiveRpcReqDTO.setCardPin(cardPhyscardActiveReqDTO.getCardPin());
        lianLianPhysicalCardActiveRpcReqDTO.setVerifyCode(cardPhyscardActiveReqDTO.getVerifyCode());
        Boolean active4LianLian = applyPhysicalCardActive4LianLian(card, lianLianPhysicalCardActiveRpcReqDTO);
        if(active4LianLian){
            card.setCardCvv(cardPhyscardActiveReqDTO.getCardCvv());
            card.setCardPin(cardPhyscardActiveReqDTO.getCardPin());
            card.setCardStatus(CardStatusEnum.ACTIVE.getStatus());
            card.setActiveStatus(CardActiveStatusEnum.SUCCESS.getStatus());
            card.setUpdateTime(new Date());
            // 更新DB卡状态
            CardPO po = CopyUtils.convert(card, CardPO.class);
            CardManager.me().saveOrUpdate(po);
        }
        return active4LianLian;
    }



    private Boolean applyPhysicalCardActive4LianLian(CardDTO card, LianLianPhysicalCardActiveRpcReqDTO lianLianPhysicalCardActiveRpcReqDTO) {

        boolean activeStatus = false;
        try {
            log.info("lianLianCardService applyPhysicalCardActive4LianLian req {}", JSON.toJSONString(lianLianPhysicalCardActiveRpcReqDTO));
            LianLianPhysicalCardActiveRpcRespDTO physicalCardActiveRpcRespDTO = iLianLianCardService.applyPhysicalCardActive(lianLianPhysicalCardActiveRpcReqDTO);
            log.info("lianLianCardService applyPhysicalCardActive4LianLian resp {}", JSON.toJSONString(physicalCardActiveRpcRespDTO));
            if (ObjectUtils.isEmpty(physicalCardActiveRpcRespDTO)) {
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_TIMEOUT);
            }else if(physicalCardActiveRpcRespDTO.success()){
                log.info("lianLianCardService applyPhysicalCardActive4LianLian success {}", JSON.toJSONString(physicalCardActiveRpcRespDTO));
                activeStatus = true;
            }else{
                log.info("lianLianCardService applyPhysicalCardActive4LianLian failed {}", JSON.toJSONString(physicalCardActiveRpcRespDTO));
                throw new FxCardException(12000021,physicalCardActiveRpcRespDTO.getRetMsg(),FinhubMessageType.TIP_WINDOW);
            }
        } catch (FxCardException fxCardException){
            throw fxCardException;
        } catch (Exception e) {
            log.error("lianLianCardService applyPhysicalCardActive4LianLian error{}", JSON.toJSONString(lianLianPhysicalCardActiveRpcReqDTO),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }

        return activeStatus;
    }


    public Boolean physcardResetPinCheck(CardPhyscardResetPinCheckReqDTO physcardResetPinCheckDTO) {
        //检查看状态
        CardDTO card = CardManager.me().getByfxCardId(physcardResetPinCheckDTO.getFxCardId());
        if (ObjectUtils.isEmpty(card)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
        }
        // 不可用
        if (card.getCardStatus().equals(CardStatusEnum.DISABLE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.EXPIRED.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.LOGOUT.getStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        String newPin = physcardResetPinCheckDTO.getNewPin();
        String oldPin = physcardResetPinCheckDTO.getOldPin();
        if(StringUtils.isBlank(card.getCardPin())||!card.getCardPin().equalsIgnoreCase(oldPin)){
            throw new FxCardException(GlobalCoreResponseCode.CARD_OLDPIN_NOT_MATCH);
        }
        if(newPin.equalsIgnoreCase(oldPin)){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_MATCH);
        }
        //检查pin
        int length = newPin.length();
        if(length!=4){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        try{
            Integer cardPinInt = Integer.getInteger(newPin);
        }catch (Exception e){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        if(StringUtils.isNotBlank(card.getBankCardNo())){
            String substring = card.getBankCardNo().substring(card.getBankCardNo().length() - 4);
            if(substring.equalsIgnoreCase(newPin)){
                throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_CARDNO_MATCH);
            }
        }

        return true;
    }

    public Boolean physcardResetPin(CardPhyscardResetPinReqDTO physcardResetPinReqDTO) {
        //检查看状态
        CardDTO card = CardManager.me().getByfxCardId(physcardResetPinReqDTO.getFxCardId());
        if (ObjectUtils.isEmpty(card)) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_NOT_EXIT);
        }
        // 不可用
        if (card.getCardStatus().equals(CardStatusEnum.DISABLE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.FREEZE.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.EXPIRED.getStatus())
            ||card.getCardStatus().equals(CardStatusEnum.LOGOUT.getStatus())) {
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_CAN_NOT_DO);
        }
        String newPin = physcardResetPinReqDTO.getNewPin();
        //检查pin
        int length = newPin.length();
        if(length!=4){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        try{
            Integer cardPinInt = Integer.getInteger(newPin);
        }catch (Exception e){
            throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_NOT_DATA);
        }
        if(StringUtils.isNotBlank(card.getBankCardNo())){
            String substring = card.getBankCardNo().substring(card.getBankCardNo().length() - 4);
            if(substring.equalsIgnoreCase(newPin)){
                throw new FxCardException(GlobalCoreResponseCode.CARD_PIN_CARDNO_MATCH);
            }
        }
        //检查验证码
        String verifyCode = physcardResetPinReqDTO.getVerifyCode();
        int lengthVer = verifyCode.length();
        if(lengthVer!=6){
            throw new FxCardException(GlobalCoreResponseCode.CARD_VERIFYCODE_NOT_DATA);
        }

        LianLianPhysicalResetPinRpcReqDTO lianLianPhysicalResetPinRpcReqDTO = new LianLianPhysicalResetPinRpcReqDTO();
        lianLianPhysicalResetPinRpcReqDTO.setMchId(card.getBankMchId());
        lianLianPhysicalResetPinRpcReqDTO.setAccountNo(card.getBankCardId());
        lianLianPhysicalResetPinRpcReqDTO.setNewPin(newPin);
        lianLianPhysicalResetPinRpcReqDTO.setToken(physcardResetPinReqDTO.getToken());
        lianLianPhysicalResetPinRpcReqDTO.setVerifyCode(physcardResetPinReqDTO.getVerifyCode());
        Boolean resetPin4LianLian = applyPhysicalResetPin4LianLian(card, lianLianPhysicalResetPinRpcReqDTO);
        if(resetPin4LianLian){
            card.setCardPin(newPin);
            card.setUpdateTime(new Date());
            // 更新DB卡状态
            CardPO po = CopyUtils.convert(card, CardPO.class);
            CardManager.me().saveOrUpdate(po);
        }
        return true;

    }

    private Boolean applyPhysicalResetPin4LianLian(CardDTO card, LianLianPhysicalResetPinRpcReqDTO lianLianPhysicalResetPinRpcReqDTO) {
        boolean resetStatus = false;
        try {
            log.info("lianLianCardService applyPhysicalResetPin4LianLian req {}", JSON.toJSONString(lianLianPhysicalResetPinRpcReqDTO));
            LianLianPhysicalCardResetPinRpcRespDTO lianLianPhysicalCardResetPinRpcRespDTO = iLianLianCardService.applyPhysicalResetPin(lianLianPhysicalResetPinRpcReqDTO);
            log.info("lianLianCardService applyPhysicalResetPin4LianLian resp {}", JSON.toJSONString(lianLianPhysicalCardResetPinRpcRespDTO));
            if (ObjectUtils.isEmpty(lianLianPhysicalCardResetPinRpcRespDTO)) {
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_TIMEOUT);
            }else if(lianLianPhysicalCardResetPinRpcRespDTO.success()){
                log.info("lianLianCardService applyPhysicalResetPin4LianLian success {}", JSON.toJSONString(lianLianPhysicalCardResetPinRpcRespDTO));
                resetStatus = true;
            }else{
                log.info("lianLianCardService applyPhysicalResetPin4LianLian failed {}", JSON.toJSONString(lianLianPhysicalCardResetPinRpcRespDTO));
                throw new FxCardException(GlobalCoreResponseCode.LIANLIAN_REQUEST_STATUS_NOT_MATCH);
            }
        } catch (FxCardException fxCardException){
            throw fxCardException;
        } catch (Exception e) {
            log.error("lianLianCardService applyPhysicalResetPin4LianLian error{}", JSON.toJSONString(lianLianPhysicalResetPinRpcReqDTO),e);
            throw new FxCardException(GlobalCoreResponseCode.CARD_STATUS_UPDATE_ERROR);
        }

        return resetStatus;
    }


}


