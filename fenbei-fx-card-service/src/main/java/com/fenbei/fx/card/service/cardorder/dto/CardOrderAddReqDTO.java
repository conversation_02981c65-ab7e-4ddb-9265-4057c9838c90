package com.fenbei.fx.card.service.cardorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡订单 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOrderAddReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private Long id;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 交易单号
     */
    private String bizNo;

    /**
     * 退款时代表原业务单号
     */
    private String oriBizNo;

    /**
     * 11消费,12退款
     */
    private Integer type;

    /**
     * 币种 美元-USD
     */
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    private String tradeName;
    /**
     * 原始交易时间
     */
    private String oriTradeTime;

    /**
     * 交易时间(北京)
     */
    private Date tradeTime;
    /**
     * 交易时区
     */
    private String tradeTimeZone;

    /**
     * 交易地
     */
    private String tradeAddress;

    /**
     * 交易备注
     */
    private String tradeRemark;

    /**
     * 核销状态
     *  0 无需核销
     *  1 已退款
     *  2 未核销(待核销)
     *  3 核销中
     *  4 已核销
     *  5 部分待核销(后续加)
     *  CHECKSTATUSENUM
     */
    private Integer checkStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 基准币种 美元-USD
     *
     */
    private String billTradeCurrency;

    /**
     * 基准操作金额 单位：分
     */
    private BigDecimal billTradeAmount;


    /**
     * 通道的交易ID
     */
    private String tradeId;

    /**
     * 通道网络交易ID
     */
    private String subTradeId;
    /**
     * 已核销金额 单位 分
     */
    private BigDecimal checkedAmount;
    /**
     * 未核销金额
     */
    private BigDecimal uncheckedAmount;
    /**
     * 核销中金额
     */
    private BigDecimal checkingAmount;
    /**
     * 无需核销金额
     */
    private BigDecimal needNotCheckAmount;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 交易币种兑人民币汇率
     */
    private BigDecimal tradeCnyExchangeRate;
    /**
     * 折算币种兑人民币汇率
     */
    private BigDecimal billTradeCnyExchangeRate;

    /**
     * 人民币金额
     */
    private BigDecimal cnyTradeAmount;

    /**
     * 渠道信息
     */
    private String cardPlatform;
    /**
     * 加密卡号
     */
    private String maskedCardNumber;
    /**
     * 订单展示状态
     */
    private Integer orderShow = 1;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

}
