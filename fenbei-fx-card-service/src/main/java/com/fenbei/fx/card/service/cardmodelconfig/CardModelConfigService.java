package com.fenbei.fx.card.service.cardmodelconfig;

import com.fenbei.fx.card.service.cardmodelconfig.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;

import java.util.List;

/**
 * 国际卡使用模式配置 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
public interface CardModelConfigService extends BaseService<CardModelConfigDTO> {

    static CardModelConfigService me() {
        return SpringUtil.getBean(CardModelConfigService.class);
    }

    /**
     * 列表
     *
     * @param cardModelConfigListReqDTO 入参DTO
     * @return
     */
    List<CardModelConfigListResDTO> list(CardModelConfigListReqDTO cardModelConfigListReqDTO);

    /**
     * First查询
     *
     * @param cardModelConfigListReqDTO 入参DTO
     * @return
     */
    CardModelConfigListResDTO listOne(CardModelConfigListReqDTO cardModelConfigListReqDTO);

    /**
     * 分页
     *
     * @param cardModelConfigPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardModelConfigPageResDTO> pagination(CardModelConfigPageReqDTO cardModelConfigPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardModelConfigAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardModelConfigAddReqDTO cardModelConfigAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardModelConfigAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardModelConfigAddReqDTO cardModelConfigAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardModelConfigAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardModelConfigShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardModelConfigShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardModelConfigModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardModelConfigModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardModelConfigRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO);

    /**
     * 保存或更新
     * @param cardModelConfigAddReqDTO
     * @return
     */
    Boolean saveOrUpdate(CardModelConfigAddReqDTO cardModelConfigAddReqDTO);

    /**
     * 根据员工id获取员工模式配置
     * @param companyId employeeId
     * @return
     */
    EmployeeModelConfigDTO getEmployeeModelConfigByEmployeeId(String companyId,String employeeId);

    /**
     * 员工默认模式变更
     * @param activeModel
     * @return
     */
    Boolean employeeModelDefaultChange(Integer activeModel);

    Integer employeeModelDefault();

    List<EmployeeModelConfigDTO> companyHaveModel();
}
