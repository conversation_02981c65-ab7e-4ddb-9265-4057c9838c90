package com.fenbei.fx.card.service.userinfo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 用户信息 修改 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoModifyReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 常驻地
     */
    private String employeeLocation;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业账户ID
     */
    private String companyAccountId;

    /**
     * 纳税人识别号
     */
    private String certificateNum;

    /**
     * 办公地址
     */
    private String officeAddress;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 发卡渠道
     */
    private String cardPlatform;

    /**
     * 发卡品牌
     */
    private String cardBrand;

    /**
     * 银行卡ID
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 发卡用途
     */
    private String cardPurpose;

    /**
     * 卡cvv码
     */
    private String cardCvv;

    /**
     * 卡片上姓名
     */
    private String nameOnCard;

    /**
     * 卡到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡到期年份
     */
    private String cardExpiryYear;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间戳
     */
    private Long createAt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间戳
     */
    private Long updateAt;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人ID
     */
    private String updateBy;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 是否删除
     */
    private Integer isDel;

}
