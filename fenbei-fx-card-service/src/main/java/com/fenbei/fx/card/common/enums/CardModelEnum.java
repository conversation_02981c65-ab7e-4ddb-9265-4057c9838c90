package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;
import org.apache.commons.lang3.StringUtils;

public enum CardModelEnum {
    UN_KNOW(-1, "未知操作"),
    NORMAL(1, "普通模式"),
    PETTY(2, "备用金模式");

    private int key;
    private String value;

    private CardModelEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public static boolean isPetty(Integer cardModel) {
        return PETTY.key == cardModel;
    }

    public static boolean isNormal(Integer cardModel) {
        return NORMAL.key == cardModel;
    }

    public int getKey() {
        return this.key;
    }

    public String getValue() {
        return I18nUtils.getMessage("CardModelEnum." + this.name(), this.value);
    }

    public static CardModelEnum getEnum(Integer key) {
        if (key == null) {
            return null;
        } else {
            CardModelEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                CardModelEnum item = var1[var3];
                if (item.getKey() == key) {
                    return item;
                }
            }

            return null;
        }
    }

    public static CardModelEnum getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        } else {
            CardModelEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                CardModelEnum item = var1[var3];
                if (item.getValue().equals(value)) {
                    return item;
                }
            }

            return null;
        }
    }
}
