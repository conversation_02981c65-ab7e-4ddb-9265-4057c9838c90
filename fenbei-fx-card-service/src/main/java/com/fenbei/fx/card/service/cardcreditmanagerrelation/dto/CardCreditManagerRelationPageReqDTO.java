package com.fenbei.fx.card.service.cardcreditmanagerrelation.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 关联申请单记录 分页 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditManagerRelationPageReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private String id;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 额度管理biz_no
     */
    private String recordId;

    /**
     * 申请单ID
     */
    private String applyTransNo;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 可核销金额
     */
    private BigDecimal uncheckedAmount;

    /**
     * 提交时间
     */
    private Date applyTime;

    /**
     * 关联金额
     */
    private BigDecimal relationAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
