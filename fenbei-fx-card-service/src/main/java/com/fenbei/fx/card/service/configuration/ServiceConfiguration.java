package com.fenbei.fx.card.service.configuration;

import com.finhub.framework.cache.config.CacheConfig;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Configuration
@ComponentScan(basePackages = {"com.fenbei.fx.card.service"})
public class ServiceConfiguration {

    @Bean
    CacheConfig cacheConfig() {
        return new CacheConfig();
    }
}
