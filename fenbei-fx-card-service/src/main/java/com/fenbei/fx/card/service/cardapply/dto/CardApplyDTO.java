package com.fenbei.fx.card.service.cardapply.dto;

import com.finhub.framework.common.dto.BaseDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 国际卡操作申请 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardApplyDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 操作申请id
     */
    private String applyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 企业商户ID
     */
    private String bankMchId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 申请类型 1.创建 2.更新
     */
    private Integer applyType;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，4.银行处理中 5.银行失败 6.银行成功'
     */
    private Integer applyStatus;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 国家区号
     */
    private String nationCode;

    /**
     * 申请人手机号+区号
     */
    private String applyerPhone;

    /**
     * 申请人邮箱
     */
    private String applyerEmail;

    /**
     * 申请人名
     */
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    private String applyerLastName;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 审批人
     */
    private String approveUserId;

    /**
     * 审批人姓名
     */
    private String approveUserName;

    /**
     * 审批通过时间
     */
    private Date approveTime;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;
    /**
     * 连连有值,云汇无值
     * 邮寄地址
     */
    private String postalAddress;

    private String outBatchNo;

    /**
     * 物流公司编码
     */
    private String logisticsCompany;
    /**
     * 物流公司订单
     */
    private String logisticsNo;
    /**
     * 订单状态
     */
    private String applyOrderStatus;

}
