package com.fenbei.fx.card.service.cardcreditmanagerrelation.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.object.MapUtils;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditmanagerrelation.CardCreditManagerRelationDAO;
import com.fenbei.fx.card.dao.cardcreditmanagerrelation.po.CardCreditManagerRelationPO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.domain.CardCreditManagerRelationDO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationShowResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.converter.CardCreditManagerRelationConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 关联申请单记录 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
@Slf4j
@Component
public class CardCreditManagerRelationManager extends BaseManagerImpl<CardCreditManagerRelationDAO, CardCreditManagerRelationPO, CardCreditManagerRelationDTO, CardCreditManagerRelationConverter> {

    public static CardCreditManagerRelationManager me() {
        return SpringUtil.getBean(CardCreditManagerRelationManager.class);
    }

    public List<CardCreditManagerRelationListResDTO> list(final CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO) {
        CardCreditManagerRelationDTO paramsDTO = CardCreditManagerRelationDO.me().buildListParamsDTO(cardCreditManagerRelationListReqDTO);

        List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList = super.findList(paramsDTO);

        return CardCreditManagerRelationDO.me().transferCardCreditManagerRelationListResDTOList(cardCreditManagerRelationDTOList);
    }

    public CardCreditManagerRelationListResDTO listOne(final CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO) {
        CardCreditManagerRelationDTO paramsDTO = CardCreditManagerRelationDO.me().buildListParamsDTO(cardCreditManagerRelationListReqDTO);

        CardCreditManagerRelationDTO cardCreditManagerRelationDTO = super.findOne(paramsDTO);

        return CardCreditManagerRelationDO.me().transferCardCreditManagerRelationListResDTO(cardCreditManagerRelationDTO);
    }

    public Page<CardCreditManagerRelationPageResDTO> pagination(final CardCreditManagerRelationPageReqDTO cardCreditManagerRelationPageReqDTO, final Integer current, final Integer size) {
        CardCreditManagerRelationDTO paramsDTO = CardCreditManagerRelationDO.me().buildPageParamsDTO(cardCreditManagerRelationPageReqDTO);

        Page<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOPage = super.findPage(paramsDTO, current, size);

        return CardCreditManagerRelationDO.me().transferCardCreditManagerRelationPageResDTOPage(cardCreditManagerRelationDTOPage);
    }

    public Boolean add(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationAddReqDTO(cardCreditManagerRelationAddReqDTO);

        CardCreditManagerRelationDTO addCardCreditManagerRelationDTO = CardCreditManagerRelationDO.me().buildAddCardCreditManagerRelationDTO(cardCreditManagerRelationAddReqDTO);

        return super.saveDTO(addCardCreditManagerRelationDTO);
    }

    public Boolean addAllColumn(final CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationAddReqDTO(cardCreditManagerRelationAddReqDTO);

        CardCreditManagerRelationDTO addCardCreditManagerRelationDTO = CardCreditManagerRelationDO.me().buildAddCardCreditManagerRelationDTO(cardCreditManagerRelationAddReqDTO);

        return super.saveAllColumn(addCardCreditManagerRelationDTO);
    }

    public Boolean addBatchAllColumn(final List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationAddReqDTOList(cardCreditManagerRelationAddReqDTOList);

        List<CardCreditManagerRelationDTO> addBatchCardCreditManagerRelationDTOList = CardCreditManagerRelationDO.me().buildAddBatchCardCreditManagerRelationDTOList(cardCreditManagerRelationAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardCreditManagerRelationDTOList);
    }

    public CardCreditManagerRelationShowResDTO show(final String id) {
        CardCreditManagerRelationDTO cardCreditManagerRelationDTO = super.findById(id);

        return CardCreditManagerRelationDO.me().transferCardCreditManagerRelationShowResDTO(cardCreditManagerRelationDTO);
    }

    public List<CardCreditManagerRelationShowResDTO> showByIds(final List<String> ids) {
        CardCreditManagerRelationDO.me().checkIds(ids);

        List<CardCreditManagerRelationDTO> cardCreditManagerRelationDTOList = super.findBatchIds(ids);

        return CardCreditManagerRelationDO.me().transferCardCreditManagerRelationShowResDTOList(cardCreditManagerRelationDTOList);
    }

    public Boolean modify(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationModifyReqDTO(cardCreditManagerRelationModifyReqDTO);

        CardCreditManagerRelationDTO modifyCardCreditManagerRelationDTO = CardCreditManagerRelationDO.me().buildModifyCardCreditManagerRelationDTO(cardCreditManagerRelationModifyReqDTO);

        return super.modifyById(modifyCardCreditManagerRelationDTO);
    }

    public Boolean modifyAllColumn(final CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationModifyReqDTO(cardCreditManagerRelationModifyReqDTO);

        CardCreditManagerRelationDTO modifyCardCreditManagerRelationDTO = CardCreditManagerRelationDO.me().buildModifyCardCreditManagerRelationDTO(cardCreditManagerRelationModifyReqDTO);

        return super.modifyAllColumnById(modifyCardCreditManagerRelationDTO);
    }

    public Boolean removeByParams(final CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO) {
        CardCreditManagerRelationDO.me().checkCardCreditManagerRelationRemoveReqDTO(cardCreditManagerRelationRemoveReqDTO);

        CardCreditManagerRelationDTO removeCardCreditManagerRelationDTO = CardCreditManagerRelationDO.me().buildRemoveCardCreditManagerRelationDTO(cardCreditManagerRelationRemoveReqDTO);

        return super.remove(removeCardCreditManagerRelationDTO);
    }

    public List<CardCreditManagerRelationDTO> findByApplyTransNo(String bizNo){
        QueryWrapper<CardCreditManagerRelationPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardCreditManagerRelationPO.DB_COL_RECORD_ID, bizNo);
        queryWrapper.eq(CardholderPO.DB_COL_DELETE_FLAG, 0);
        return this.findList(queryWrapper);
    }
    @Override
    protected CardCreditManagerRelationPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditManagerRelationPO();
        }

        return (CardCreditManagerRelationPO) MapUtils.toBean(map, CardCreditManagerRelationPO.class);
    }

    @Override
    protected CardCreditManagerRelationDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardCreditManagerRelationDTO();
        }

        return (CardCreditManagerRelationDTO) MapUtils.toBean(map, CardCreditManagerRelationDTO.class);
    }
}
