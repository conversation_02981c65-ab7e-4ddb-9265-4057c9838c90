package com.fenbei.fx.card.service.cardholderoperflow.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardholderoperflow.po.CardholderOperFlowPO;
import com.fenbei.fx.card.service.cardholderoperflow.CardholderOperFlowService;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.manager.CardholderOperFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 持卡人被操作流水 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Service
public class CardholderOperFlowServiceImpl extends BaseServiceImpl<CardholderOperFlowManager, CardholderOperFlowPO, CardholderOperFlowDTO> implements CardholderOperFlowService {

    @Override
    public List<CardholderOperFlowListResDTO> list(final CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO) {
        return manager.list(cardholderOperFlowListReqDTO);
    }

    @Override
    public CardholderOperFlowListResDTO listOne(final CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO) {
        return manager.listOne(cardholderOperFlowListReqDTO);
    }

    @Override
    public Page<CardholderOperFlowPageResDTO> pagination(final CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardholderOperFlowPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        return manager.add(cardholderOperFlowAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO) {
        return manager.addAllColumn(cardholderOperFlowAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList) {
        return manager.addBatchAllColumn(cardholderOperFlowAddReqDTOList);
    }

    @Override
    public CardholderOperFlowShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardholderOperFlowShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        return manager.modify(cardholderOperFlowModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO) {
        return manager.modifyAllColumn(cardholderOperFlowModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO) {
        return manager.removeByParams(cardholderOperFlowRemoveReqDTO);
    }
}
