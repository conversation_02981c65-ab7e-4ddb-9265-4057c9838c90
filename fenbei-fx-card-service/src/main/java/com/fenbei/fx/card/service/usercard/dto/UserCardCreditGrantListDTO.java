package com.fenbei.fx.card.service.usercard.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午2:38
 */
@Data
public class UserCardCreditGrantListDTO extends BaseDTO implements Serializable {

    /**
     * 记录id
     */
     private String recordId;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 审批单id
     */
    private String bizNo;

    /**
     * 申请单 meaningNo
     */
    private String applyMeaningNo;

    /**
     *  申请单记录id
     */
    private String applyTransNo;

    /**
     * 申请人
     */
    private String applyOperationUserName;

    /**
     * 申请人部门
     */
    private String applyOperationUserDept;

    /**
     * 申请金额 $1
     */
    private String applyAmount;

    /**
     *  费用类别
     */
    private String costType;
    /**
     * 费用类别描述
     */
    private String costTypeName;

    /**
     *  费用归属
     */
    private String costAttribution;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 申请状态
     */
    private String applyStatusDesc;

    /**
     * 已退还金额
     */
    private String backedAmount;

    /**
     * 已核销金额
     */
    private String writenOffAmount;

    /**
     * 核销中金额
     */
    private String writeOffIngAmount;

    /**
     * 无需核销金额
     */
    private String unWriteOffAmount;

    /**
     * 可用金额
     */
    private String availableAmount;
    /**
     * 未核销金额
     */
    private String waitWriteOffAmount;


}
