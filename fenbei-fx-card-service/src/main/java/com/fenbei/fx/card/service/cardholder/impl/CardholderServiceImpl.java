package com.fenbei.fx.card.service.cardholder.impl;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.common.enums.CardholderApplyShowStatusEnum;
import com.fenbei.fx.card.common.enums.CardholderShowStatusEnum;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.service.cardholder.CardholderService;
import com.fenbei.fx.card.service.cardholder.dto.*;
import com.fenbei.fx.card.service.cardholder.manager.CardholderManager;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.service.cardholderapply.manager.CardholderApplyManager;
import com.fenbeitong.dech.api.service.airwallex.IAirWallexCardHolderService;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.swift.utils.ObjUtils;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 持卡人 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Service
public class CardholderServiceImpl extends BaseServiceImpl<CardholderManager, CardholderPO, CardholderDTO> implements CardholderService {

    private CardholderApplyManager cardholderApplyManager = CardholderApplyManager.me();

    @DubboReference
    private IAirWallexCardHolderService iAirWallexCardHolderService;

    @Override
    public Page<CardholderByPageResDTO> findCardholderOrApplyByPage(CardholderByPageReqDTO reqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        String userId = user.getUser_id();
        reqDTO.setCompanyId(companyId);
        reqDTO.setCurrentUserId(userId);
        log.info("CardholderServiceImpl findCardholderOrApplyByPage params={}", JsonUtils.toJson(reqDTO));
        // 根据showStatus判断查申请单表还是持卡人表
        if (CardholderApplyShowStatusEnum.existKey(reqDTO.getShowStatus())) {
            CardholderApplyShowStatusEnum applyShowStatusEnum = CardholderApplyShowStatusEnum.getEnum(reqDTO.getShowStatus());
            // 查申请单表
           return cardholderApplyManager.findCardholderApplyPageByStatus(reqDTO, applyShowStatusEnum);
        }
        if (CardholderShowStatusEnum.existKey(reqDTO.getShowStatus())) {
            CardholderShowStatusEnum showStatusEnum = CardholderShowStatusEnum.getEnum(reqDTO.getShowStatus());
            // 查持卡人表
           return manager.findCardholderPageByStatus(reqDTO, showStatusEnum);
        } else {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
    }

    @Override
    public CardholderOrApplyListResDTO findUserCardholderOrApply(CardholderOrApplyListReqDTO reqDto) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        String userId = user.getUser_id();
        reqDto.setEmployeeId(userId);
        reqDto.setCompanyId(companyId);
        log.info("CardholderServiceImpl findUserCardholderOrApply userId={}，phone={}", userId, reqDto.getPhone());

        //持卡人
        CardholderOrApplyListResDTO resDTO = new CardholderOrApplyListResDTO();
        List<CardholderDTO> cardholders = manager.findCardholderByEmployeeId(reqDto);
        resDTO.setCardholderList(convert2CardholderDetailResDTOS(cardholders));
        List<CardholderApplyDTO> cardholderApplys = cardholderApplyManager.findCardholderApplyByEmployeeId(reqDto);
        resDTO.setCardholderApplyList(cardholderApplyManager.convert2CardholderDetailResDTOS(cardholderApplys));
        log.info("CardholderServiceImpl findUserCardholderOrApply userId={}，phone={}, resDTO={}", userId, reqDto.getPhone(), JsonUtils.toJson(resDTO));

        return resDTO;
    }

    @Override
    public CardholderDetailResDTO modify(CardholderModifyReqDTO reqDTO) {
        return doModify(reqDTO);
    }

    private CardholderDetailResDTO doModify(CardholderModifyReqDTO reqDTO) {
        // 根据showStatus判断查申请单表还是持卡人表
        if (CardholderApplyShowStatusEnum.existKey(reqDTO.getShowStatus())) {
            // 更新申请单表
            return cardholderApplyManager.modify(reqDTO);
        }
        if (CardholderShowStatusEnum.existKey(reqDTO.getShowStatus())) {
            // 查持卡人表，创建申请单（类型为更新）
            return cardholderApplyManager.createModify(reqDTO);
        } else {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
    }

    @Override
    public void allCompare() {
        manager.allCompare();
    }

    @Override
    public CardholderDetailResDTO detail(String applyId, String fxCardholderId, Integer showStatus) {
        if (CardholderApplyShowStatusEnum.existKey(showStatus)) {
            CardholderApplyDTO applyDTO = cardholderApplyManager.findByApplyId(applyId);
            return cardholderApplyManager.convert2CardholderDetailResDTO(applyDTO);
        }
        if (CardholderShowStatusEnum.existKey(showStatus)) {
            CardholderDTO cardholderDTO = manager.findByCardholderId(fxCardholderId);
            return convert2CardholderDetailResDTO(cardholderDTO);
        } else {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
    }

    private List<CardholderDetailResDTO> convert2CardholderDetailResDTOS(List<CardholderDTO> cardholderDTOs) {
        List<CardholderDetailResDTO> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(cardholderDTOs)){
            return list;
        }
        cardholderDTOs.forEach(p->{
            list.add(convert2CardholderDetailResDTO(p));
        });
        return list;
    }

    private CardholderDetailResDTO convert2CardholderDetailResDTO(CardholderDTO cardholderDTO) {
        CardholderDetailResDTO resDTO = new CardholderDetailResDTO();
        BeanUtils.copyProperties(cardholderDTO, resDTO);
        resDTO.setFxCardholderId(cardholderDTO.getFxCardholderId());
        resDTO.setName(cardholderDTO.getName());
        resDTO.setBirth(cardholderDTO.getBirth());
        resDTO.setNationCode(cardholderDTO.getNationCode());
        resDTO.setPhone(cardholderDTO.getPhone());
        resDTO.setAddressDto(JsonUtils.toObj(cardholderDTO.getAddress(), AddressDto.class));
        resDTO.setPostalAddressDto(JsonUtils.toObj(cardholderDTO.getPostalAddress(), AddressDto.class));
        resDTO.setIdentificationType(cardholderDTO.getIdentificationType());
        resDTO.setIdentificationNumber(cardholderDTO.getIdentificationNumber());
        resDTO.setIdentificationExpiryDate(cardholderDTO.getIdentificationExpiryDate());

        CardholderShowStatusEnum holderStatusEnum = CardholderShowStatusEnum.getEnumWhithHolderStatus(cardholderDTO.getHolderStatus());
        if(Objects.nonNull(holderStatusEnum)){
            resDTO.setShowStatusStr(holderStatusEnum.getDesc());
            resDTO.setShowStatus(holderStatusEnum.getKey());
        }
        CardPlatformEnum cardPlatformEnum  = CardPlatformEnum.getPlatform(cardholderDTO.getCardPlatform());
        resDTO.setCardPlatformName(cardPlatformEnum.getName());
        return resDTO;
    }

    @Override
    public void enableSwitch(CardholderEnableReqDTO reqDTO){
        manager.enableSwitch(reqDTO);
    }
}
