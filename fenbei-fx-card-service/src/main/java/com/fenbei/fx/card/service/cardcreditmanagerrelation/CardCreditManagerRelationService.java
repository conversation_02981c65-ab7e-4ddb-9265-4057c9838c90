package com.fenbei.fx.card.service.cardcreditmanagerrelation;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationListResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanagerrelation.dto.CardCreditManagerRelationShowResDTO;

import java.util.List;

/**
 * 关联申请单记录 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-17
 */
public interface CardCreditManagerRelationService extends BaseService<CardCreditManagerRelationDTO> {

    static CardCreditManagerRelationService me() {
        return SpringUtil.getBean(CardCreditManagerRelationService.class);
    }

    /**
     * 列表
     *
     * @param cardCreditManagerRelationListReqDTO 入参DTO
     * @return
     */
    List<CardCreditManagerRelationListResDTO> list(CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO);

    /**
     * First查询
     *
     * @param cardCreditManagerRelationListReqDTO 入参DTO
     * @return
     */
    CardCreditManagerRelationListResDTO listOne(CardCreditManagerRelationListReqDTO cardCreditManagerRelationListReqDTO);

    /**
     * 分页
     *
     * @param cardCreditManagerRelationPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardCreditManagerRelationPageResDTO> pagination(CardCreditManagerRelationPageReqDTO cardCreditManagerRelationPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardCreditManagerRelationAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardCreditManagerRelationAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardCreditManagerRelationAddReqDTO cardCreditManagerRelationAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardCreditManagerRelationAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardCreditManagerRelationAddReqDTO> cardCreditManagerRelationAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardCreditManagerRelationShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardCreditManagerRelationShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardCreditManagerRelationModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardCreditManagerRelationModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardCreditManagerRelationModifyReqDTO cardCreditManagerRelationModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardCreditManagerRelationRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardCreditManagerRelationRemoveReqDTO cardCreditManagerRelationRemoveReqDTO);
}
