package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-01 下午3:56
 */
@Data
public class BankCardCreditChangeDTO implements Serializable {

    /**
     * 持卡人
     */
    private String employeeName;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡片类型
     */
    private String cardType;

    private Integer operationType;

    /**
     * 交易类型
     */
    private String operationTypeDesc;

    /**
     * 交易时间
     */
    private String transTime;

    /**
     * 折算币种
     */
    private String conversCurrency;

    /**
     * 折算金额
     */
    private BigDecimal conversAmount;
}
