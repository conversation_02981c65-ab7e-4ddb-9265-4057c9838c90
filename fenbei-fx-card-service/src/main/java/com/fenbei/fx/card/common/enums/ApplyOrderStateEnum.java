package com.fenbei.fx.card.common.enums;

/**
 * <AUTHOR>
 * @desc (额度)发放状态
 * @date 2022/9/22 10:36 上午
 */
public enum ApplyOrderStateEnum {

    //excel批量导入临时表使用
    UPLOADING(-3, "上传中"),
    //-2 用于前端传值，代表全部
    ALL(-2, "全部"),
    CREATE_FAIL(-1, "创建失败"),
    CREATING(0, "创建中"),
    WAITING(1, "待发放"),
    GRANTING(2, "发放中"),
    GRANT_SUCCESS(3, "已发放"),
    GRANT_FAIL(4, "发放失败"),
    ;

    public static String getDescByCode(Integer code) {
        if (null == code) {
            return "-";
        }
        for (ApplyOrderStateEnum value : ApplyOrderStateEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "-";
    }

    private int code;
    private String desc;

    ApplyOrderStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean canRemove(Integer applyState) {
        return  applyState == CREATE_FAIL.getCode() || applyState == GRANT_FAIL.getCode();
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static boolean isAll(Integer code) {
        return code == ALL.getCode();
    }
    public static boolean isSuccess(Integer code) {
        return code == GRANT_SUCCESS.getCode();
    }

    public static boolean isCreating(Integer code) {
        return code == CREATING.getCode();
    }

    public static boolean isCreatFail(Integer code) {
        return code == CREATE_FAIL.getCode();
    }

    public static boolean isGrantFail(Integer code) {
        return code == GRANT_FAIL.getCode();
    }

    public static boolean isWaiting(Integer code) {
        return code == WAITING.getCode();
    }


    public static boolean canGrant(Integer code){
        return isWaiting(code)||isGrantFail(code);
    }

    public static boolean canTryAgainGrant(Integer code){
        return isWaiting(code)||isGrantFail(code);
    }
}
