package com.fenbei.fx.card.service.cardholder.domain;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.service.cardholder.converter.CardholderConverter;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.finhub.framework.core.domain.BaseDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 持卡人 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-06
 */
@Slf4j
@Component
public class CardholderDO extends BaseDO<CardholderDTO, CardholderPO, CardholderConverter> {

    public static CardholderDO me() {
        return SpringUtil.getBean(CardholderDO.class);
    }

}
