package com.fenbei.fx.card.common.enums;

import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.finhub.framework.swift.utils.ObjUtils;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.Objects;


/**
 * 各种渠道的限制条件，或者固有属性
 */
@AllArgsConstructor
public enum CardPlatformCaseEnum {


    /**
     * 发卡渠道 AIRWALLEX、LIANLIAN
     */
    AIRWALLEX(FxAcctChannelEnum.AIRWALLEX,CardPlatformEnum.AIRWALLEX,CurrencyEnum.USD,new BigDecimal(0)),
    LIANLIAN(FxAcctChannelEnum.LIANLIAN,CardPlatformEnum.LIANLIAN,CurrencyEnum.CNY,new BigDecimal(0)),

        ;


    public static CardPlatformCaseEnum getEnum(CardPlatformEnum cardPlatformEnum) {
        if(ObjUtils.isNull(cardPlatformEnum)){
            return null;
        }
        for (CardPlatformCaseEnum item : values()) {
            if (item.getCardPlatformEnum().getCode().equalsIgnoreCase(cardPlatformEnum.getCode())) {
                return item;
            }
        }
        return null;
    }

    public static CardPlatformCaseEnum getEnumByCardPlatformCode(String cardPlatformCode) {
        if(ObjUtils.isNull(cardPlatformCode)){
            return null;
        }
        for (CardPlatformCaseEnum item : values()) {
            if (item.getCardPlatformEnum().getCode().equalsIgnoreCase(cardPlatformCode)) {
                return item;
            }
        }
        return null;
    }


    public static CardPlatformCaseEnum getEnumByFxAcctChannel(String fxAcctChannel) {
        if(ObjUtils.isNull(fxAcctChannel)){
            return null;
        }
        for (CardPlatformCaseEnum item : values()) {
            if (Objects.equals(item.getFxAcctChannelEnum().getChannel(), fxAcctChannel)) {
                return item;
            }
        }
        return null;
    }


    private FxAcctChannelEnum fxAcctChannelEnum;

    private CardPlatformEnum cardPlatformEnum;

    private CurrencyEnum currencyEnum;

    /**
     * 申请开卡时，检查余额账户的额度
     */
    private BigDecimal applyLimitAmount;

    public FxAcctChannelEnum getFxAcctChannelEnum() {
        return fxAcctChannelEnum;
    }

    public void setFxAcctChannelEnum(FxAcctChannelEnum fxAcctChannelEnum) {
        this.fxAcctChannelEnum = fxAcctChannelEnum;
    }

    public CardPlatformEnum getCardPlatformEnum() {
        return cardPlatformEnum;
    }

    public void setCardPlatformEnum(CardPlatformEnum cardPlatformEnum) {
        this.cardPlatformEnum = cardPlatformEnum;
    }

    public CurrencyEnum getCurrencyEnum() {
        return currencyEnum;
    }

    public void setCurrencyEnum(CurrencyEnum currencyEnum) {
        this.currencyEnum = currencyEnum;
    }

    public BigDecimal getApplyLimitAmount() {
        return applyLimitAmount;
    }

    public void setApplyLimitAmount(BigDecimal applyLimitAmount) {
        this.applyLimitAmount = applyLimitAmount;
    }
}
