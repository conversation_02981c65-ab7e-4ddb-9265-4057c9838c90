package com.fenbei.fx.card.service.cardholderoperflow.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 持卡人被操作流水 分页 ResDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderOperFlowPageResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private Long id;

    /**
     * 操作记录id
     */
    private String operFlowId;

    /**
     * 操作申请id
     */
    private String applyId;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 渠道方持卡人id
     */
    private String bankCardholderId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作类型 1.创建 2.更新
     */
    private Integer operateType;

    /**
     * 操作描述
     */
    private String operateDesc;

    /**
     * 操作前快照
     */
    private String preSnapshot;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

}
