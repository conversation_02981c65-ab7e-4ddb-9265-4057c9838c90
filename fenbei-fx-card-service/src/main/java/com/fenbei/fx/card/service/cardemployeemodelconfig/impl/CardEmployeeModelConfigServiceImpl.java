package com.fenbei.fx.card.service.cardemployeemodelconfig.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardemployeemodelconfig.po.CardEmployeeModelConfigPO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.CardEmployeeModelConfigService;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.manager.CardEmployeeModelConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国际卡员工使用模式配置 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Service
public class CardEmployeeModelConfigServiceImpl extends BaseServiceImpl<CardEmployeeModelConfigManager, CardEmployeeModelConfigPO, CardEmployeeModelConfigDTO> implements CardEmployeeModelConfigService {

    @Override
    public List<CardEmployeeModelConfigListResDTO> list(final CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO) {
        return manager.list(cardEmployeeModelConfigListReqDTO);
    }

    @Override
    public CardEmployeeModelConfigListResDTO listOne(final CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO) {
        return manager.listOne(cardEmployeeModelConfigListReqDTO);
    }

    @Override
    public Page<CardEmployeeModelConfigPageResDTO> pagination(final CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardEmployeeModelConfigPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        return manager.add(cardEmployeeModelConfigAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO) {
        return manager.addAllColumn(cardEmployeeModelConfigAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList) {
        return manager.addBatchAllColumn(cardEmployeeModelConfigAddReqDTOList);
    }

    @Override
    public CardEmployeeModelConfigShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardEmployeeModelConfigShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        return manager.modify(cardEmployeeModelConfigModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO) {
        return manager.modifyAllColumn(cardEmployeeModelConfigModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO) {
        return manager.removeByParams(cardEmployeeModelConfigRemoveReqDTO);
    }
}
