package com.fenbei.fx.card.service.cardcreditmanager;

import com.fenbei.fx.card.service.cardcreditmanager.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;

import java.util.Date;
import java.util.List;

/**
 * 国际卡额度申请退回管理表 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
public interface CardCreditManagerService extends BaseService<CardCreditManagerDTO> {

    static CardCreditManagerService me() {
        return SpringUtil.getBean(CardCreditManagerService.class);
    }

    /**
     * 列表
     *
     * @param cardCreditManagerListReqDTO 入参DTO
     * @return
     */
    List<CardCreditManagerListResDTO> list(CardCreditManagerListReqDTO cardCreditManagerListReqDTO);

    /**
     * First查询
     *
     * @param cardCreditManagerListReqDTO 入参DTO
     * @return
     */
    CardCreditManagerListResDTO listOne(CardCreditManagerListReqDTO cardCreditManagerListReqDTO);

    /**
     * 分页
     *
     * @param cardCreditManagerPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardCreditManagerPageResDTO> pagination(CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardCreditManagerAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardCreditManagerAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardCreditManagerAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardCreditManagerShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardCreditManagerShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardCreditManagerModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardCreditManagerModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardCreditManagerRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO);

    /**
     * 额度退还
     * @param cardCreditReturnRpcReqDTO 额度退还DTO
     * @return CardCreditManagerReturnRespDTO
     */
    CardCreditManagerReturnRespDTO refund(CardCreditManagerReturnReqDTO cardCreditReturnRpcReqDTO);

    List<CardCreditManagerDTO> queryUncheckApply(String fxCardId);

    List<CardCreditManagerDTO> queryUncheckApplyByEmployeeId(String employeeId);

    List<CardCreditManagerDTO> queryFailedApply(String companyId,String bankName, Date startDate);

    List<CardCreditManagerDTO> queryExistedApply(String bizNo);


}
