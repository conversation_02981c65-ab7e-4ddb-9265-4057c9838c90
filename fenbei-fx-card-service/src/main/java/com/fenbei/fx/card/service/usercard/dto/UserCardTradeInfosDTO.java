package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class UserCardTradeInfosDTO  implements Serializable {
    /**
     * 交易总条数
     */
    private Integer totalCount;
    /**
     * 查询未核销消费金额
     */
    private BigDecimal uncheckConsume;
    /**
     * 未核销金额描述
     */
    private TotalPrice uncheckConsumeDesc;

    private Integer uncheckUse;

    private List<UserCardTradeInfoDTO> tradeInfos;

}
