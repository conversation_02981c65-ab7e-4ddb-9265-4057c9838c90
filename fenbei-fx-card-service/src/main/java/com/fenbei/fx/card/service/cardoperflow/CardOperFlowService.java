package com.fenbei.fx.card.service.cardoperflow;

import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;

import java.util.List;

/**
 * 卡操作流水 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardOperFlowService extends BaseService<CardOperFlowDTO> {

    static CardOperFlowService me() {
        return SpringUtil.getBean(CardOperFlowService.class);
    }

    /**
     * 列表
     *
     * @param cardOperFlowListReqDTO 入参DTO
     * @return
     */
    List<CardOperFlowListResDTO> list(CardOperFlowListReqDTO cardOperFlowListReqDTO);

    /**
     * First查询
     *
     * @param cardOperFlowListReqDTO 入参DTO
     * @return
     */
    CardOperFlowListResDTO listOne(CardOperFlowListReqDTO cardOperFlowListReqDTO);

    /**
     * 分页
     *
     * @param cardOperFlowPageReqDTO 入参DTO
     * @param current            当前页
     * @param size               每页大小
     * @return
     */
    Page<CardOperFlowPageResDTO> pagination(CardOperFlowPageReqDTO cardOperFlowPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardOperFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardOperFlowAddReqDTO cardOperFlowAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardOperFlowAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardOperFlowAddReqDTO cardOperFlowAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardOperFlowAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList);

    /**
     * 详情
     *
     * @param id 主键ID
     * @return
     */
    CardOperFlowShowResDTO show(String id);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardOperFlowShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardOperFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardOperFlowModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardOperFlowRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO);
}
