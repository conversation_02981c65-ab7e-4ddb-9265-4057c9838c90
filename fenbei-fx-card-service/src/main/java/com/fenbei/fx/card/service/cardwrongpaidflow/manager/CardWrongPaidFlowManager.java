package com.fenbei.fx.card.service.cardwrongpaidflow.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.dao.cardorder.po.CardOrderPO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.object.MapUtils;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardwrongpaidflow.CardWrongPaidFlowDAO;
import com.fenbei.fx.card.dao.cardwrongpaidflow.po.CardWrongPaidFlowPO;
import com.fenbei.fx.card.service.cardwrongpaidflow.domain.CardWrongPaidFlowDO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowAddReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowShowResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.converter.CardWrongPaidFlowConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 错花还款流水表 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Slf4j
@Component
public class CardWrongPaidFlowManager extends BaseManagerImpl<CardWrongPaidFlowDAO, CardWrongPaidFlowPO, CardWrongPaidFlowDTO, CardWrongPaidFlowConverter> {

    public static CardWrongPaidFlowManager me() {
        return SpringUtil.getBean(CardWrongPaidFlowManager.class);
    }

    public List<CardWrongPaidFlowListResDTO> list(final CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO) {
        CardWrongPaidFlowDTO paramsDTO = CardWrongPaidFlowDO.me().buildListParamsDTO(cardWrongPaidFlowListReqDTO);

        List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList = super.findList(paramsDTO);

        return CardWrongPaidFlowDO.me().transferCardWrongPaidFlowListResDTOList(cardWrongPaidFlowDTOList);
    }

    public CardWrongPaidFlowListResDTO listOne(final CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO) {
        CardWrongPaidFlowDTO paramsDTO = CardWrongPaidFlowDO.me().buildListParamsDTO(cardWrongPaidFlowListReqDTO);

        CardWrongPaidFlowDTO cardWrongPaidFlowDTO = super.findOne(paramsDTO);

        return CardWrongPaidFlowDO.me().transferCardWrongPaidFlowListResDTO(cardWrongPaidFlowDTO);
    }

    public Page<CardWrongPaidFlowPageResDTO> pagination(final CardWrongPaidFlowPageReqDTO cardWrongPaidFlowPageReqDTO, final Integer current, final Integer size) {
        CardWrongPaidFlowDTO paramsDTO = CardWrongPaidFlowDO.me().buildPageParamsDTO(cardWrongPaidFlowPageReqDTO);

        Page<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOPage = super.findPage(paramsDTO, current, size);

        return CardWrongPaidFlowDO.me().transferCardWrongPaidFlowPageResDTOPage(cardWrongPaidFlowDTOPage);
    }

    public Boolean add(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowAddReqDTO(cardWrongPaidFlowAddReqDTO);

        CardWrongPaidFlowDTO addCardWrongPaidFlowDTO = CardWrongPaidFlowDO.me().buildAddCardWrongPaidFlowDTO(cardWrongPaidFlowAddReqDTO);

        return super.saveDTO(addCardWrongPaidFlowDTO);
    }

    public Boolean addAllColumn(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowAddReqDTO(cardWrongPaidFlowAddReqDTO);

        CardWrongPaidFlowDTO addCardWrongPaidFlowDTO = CardWrongPaidFlowDO.me().buildAddCardWrongPaidFlowDTO(cardWrongPaidFlowAddReqDTO);

        return super.saveAllColumn(addCardWrongPaidFlowDTO);
    }

    public Boolean addBatchAllColumn(final List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowAddReqDTOList(cardWrongPaidFlowAddReqDTOList);

        List<CardWrongPaidFlowDTO> addBatchCardWrongPaidFlowDTOList = CardWrongPaidFlowDO.me().buildAddBatchCardWrongPaidFlowDTOList(cardWrongPaidFlowAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardWrongPaidFlowDTOList);
    }

    public CardWrongPaidFlowShowResDTO show(final String id) {
        CardWrongPaidFlowDTO cardWrongPaidFlowDTO = super.findById(id);

        return CardWrongPaidFlowDO.me().transferCardWrongPaidFlowShowResDTO(cardWrongPaidFlowDTO);
    }

    public List<CardWrongPaidFlowShowResDTO> showByIds(final List<String> ids) {
        CardWrongPaidFlowDO.me().checkIds(ids);

        List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList = super.findBatchIds(ids);

        return CardWrongPaidFlowDO.me().transferCardWrongPaidFlowShowResDTOList(cardWrongPaidFlowDTOList);
    }

    public Boolean modify(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowModifyReqDTO(cardWrongPaidFlowModifyReqDTO);

        CardWrongPaidFlowDTO modifyCardWrongPaidFlowDTO = CardWrongPaidFlowDO.me().buildModifyCardWrongPaidFlowDTO(cardWrongPaidFlowModifyReqDTO);

        return super.modifyById(modifyCardWrongPaidFlowDTO);
    }

    public Boolean modifyAllColumn(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowModifyReqDTO(cardWrongPaidFlowModifyReqDTO);

        CardWrongPaidFlowDTO modifyCardWrongPaidFlowDTO = CardWrongPaidFlowDO.me().buildModifyCardWrongPaidFlowDTO(cardWrongPaidFlowModifyReqDTO);

        return super.modifyAllColumnById(modifyCardWrongPaidFlowDTO);
    }

    public Boolean removeByParams(final CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO) {
        CardWrongPaidFlowDO.me().checkCardWrongPaidFlowRemoveReqDTO(cardWrongPaidFlowRemoveReqDTO);

        CardWrongPaidFlowDTO removeCardWrongPaidFlowDTO = CardWrongPaidFlowDO.me().buildRemoveCardWrongPaidFlowDTO(cardWrongPaidFlowRemoveReqDTO);

        return super.remove(removeCardWrongPaidFlowDTO);
    }

    @Override
    protected CardWrongPaidFlowPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardWrongPaidFlowPO();
        }

        return (CardWrongPaidFlowPO) MapUtils.toBean(map, CardWrongPaidFlowPO.class);
    }

    @Override
    protected CardWrongPaidFlowDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardWrongPaidFlowDTO();
        }

        return (CardWrongPaidFlowDTO) MapUtils.toBean(map, CardWrongPaidFlowDTO.class);
    }

    public CardWrongPaidFlowDTO findByBizNoAndCompanyId(String bizNo, String companyId){
        if (StringUtils.isAnyBlank(bizNo, companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardWrongPaidFlowPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_BIZ_NO, bizNo);
        queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, companyId);
        return super.findOne(queryWrapper);
    }
}
