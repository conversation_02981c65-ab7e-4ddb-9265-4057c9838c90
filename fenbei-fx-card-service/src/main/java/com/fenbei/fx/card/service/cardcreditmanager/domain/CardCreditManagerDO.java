package com.fenbei.fx.card.service.cardcreditmanager.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditmanager.po.CardCreditManagerPO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerShowResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.converter.CardCreditManagerConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 国际卡额度申请退回管理表 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Slf4j
@Component
public class CardCreditManagerDO extends BaseDO<CardCreditManagerDTO, CardCreditManagerPO, CardCreditManagerConverter> {

    public static CardCreditManagerDO me() {
        return SpringUtil.getBean(CardCreditManagerDO.class);
    }

    public void checkCardCreditManagerAddReqDTO(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        if (Func.isEmpty(cardCreditManagerAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditManagerAddReqDTOList(final List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList) {
        if (Func.isEmpty(cardCreditManagerAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardCreditManagerModifyReqDTO(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        if (Func.isEmpty(cardCreditManagerModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardCreditManagerRemoveReqDTO(final CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO) {
        if (Func.isEmpty(cardCreditManagerRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardCreditManagerDTO buildListParamsDTO(final CardCreditManagerListReqDTO cardCreditManagerListReqDTO) {
        return converter.convertToCardCreditManagerDTO(cardCreditManagerListReqDTO);
    }

    public CardCreditManagerDTO buildPageParamsDTO(final CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO) {
        return converter.convertToCardCreditManagerDTO(cardCreditManagerPageReqDTO);
    }

    public CardCreditManagerDTO buildAddCardCreditManagerDTO(final CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO) {
        return converter.convertToCardCreditManagerDTO(cardCreditManagerAddReqDTO);
    }

    public List<CardCreditManagerDTO> buildAddBatchCardCreditManagerDTOList(final List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList) {
        return converter.convertToCardCreditManagerDTOList(cardCreditManagerAddReqDTOList);
    }

    public CardCreditManagerDTO buildModifyCardCreditManagerDTO(final CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO) {
        return converter.convertToCardCreditManagerDTO(cardCreditManagerModifyReqDTO);
    }

    public CardCreditManagerDTO buildRemoveCardCreditManagerDTO(final CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO) {
        return converter.convertToCardCreditManagerDTO(cardCreditManagerRemoveReqDTO);
    }

    public List<CardCreditManagerListResDTO> transferCardCreditManagerListResDTOList(final List<CardCreditManagerDTO> cardCreditManagerDTOList) {

        return converter.convertToCardCreditManagerListResDTOList(cardCreditManagerDTOList);
    }

    public CardCreditManagerListResDTO transferCardCreditManagerListResDTO(final CardCreditManagerDTO cardCreditManagerDTO) {

        return converter.convertToCardCreditManagerListResDTO(cardCreditManagerDTO);
    }

    public Page<CardCreditManagerPageResDTO> transferCardCreditManagerPageResDTOPage(final Page<CardCreditManagerDTO> cardCreditManagerDTOPage) {

        return converter.convertToCardCreditManagerPageResDTOPage(cardCreditManagerDTOPage);
    }

    public CardCreditManagerShowResDTO transferCardCreditManagerShowResDTO(final CardCreditManagerDTO cardCreditManagerDTO) {

        return converter.convertToCardCreditManagerShowResDTO(cardCreditManagerDTO);
    }

    public List<CardCreditManagerShowResDTO> transferCardCreditManagerShowResDTOList(final List<CardCreditManagerDTO> cardCreditManagerDTOList) {

        return converter.convertToCardCreditManagerShowResDTOList(cardCreditManagerDTOList);
    }
}
