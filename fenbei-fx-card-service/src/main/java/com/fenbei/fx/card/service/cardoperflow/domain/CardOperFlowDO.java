package com.fenbei.fx.card.service.cardoperflow.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;
import com.fenbei.fx.card.util.I18nUtils;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardoperflow.po.CardOperFlowPO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import com.fenbei.fx.card.service.cardoperflow.converter.CardOperFlowConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 卡操作流水 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardOperFlowDO extends BaseDO<CardOperFlowDTO, CardOperFlowPO, CardOperFlowConverter> {

    public static CardOperFlowDO me() {
        return SpringUtil.getBean(CardOperFlowDO.class);
    }

    public void checkCardOperFlowAddReqDTO(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        if (Func.isEmpty(cardOperFlowAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkCardOperFlowAddReqDTOList(final List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList) {
        if (Func.isEmpty(cardOperFlowAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("COLL_SIZE_NOT_EMPTY"));
        }
    }

    public void checkCardOperFlowModifyReqDTO(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        if (Func.isEmpty(cardOperFlowModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public void checkCardOperFlowRemoveReqDTO(final CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO) {
        if (Func.isEmpty(cardOperFlowRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("IN_PARAMS_NOT_EMPTY"));
        }
    }

    public CardOperFlowDTO buildListParamsDTO(final CardOperFlowListReqDTO cardOperFlowListReqDTO) {
        return converter.convertToCardOperFlowDTO(cardOperFlowListReqDTO);
    }

    public CardOperFlowDTO buildPageParamsDTO(final CardOperFlowPageReqDTO cardOperFlowPageReqDTO) {
        return converter.convertToCardOperFlowDTO(cardOperFlowPageReqDTO);
    }

    public CardOperFlowDTO buildAddCardOperFlowDTO(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        return converter.convertToCardOperFlowDTO(cardOperFlowAddReqDTO);
    }

    public List<CardOperFlowDTO> buildAddBatchCardOperFlowDTOList(final List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList) {
        return converter.convertToCardOperFlowDTOList(cardOperFlowAddReqDTOList);
    }

    public CardOperFlowDTO buildModifyCardOperFlowDTO(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        return converter.convertToCardOperFlowDTO(cardOperFlowModifyReqDTO);
    }

    public CardOperFlowDTO buildRemoveCardOperFlowDTO(final CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO) {
        return converter.convertToCardOperFlowDTO(cardOperFlowRemoveReqDTO);
    }

    public List<CardOperFlowListResDTO> transferCardOperFlowListResDTOList(final List<CardOperFlowDTO> cardOperFlowDTOList) {
        if (Func.isEmpty(cardOperFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOperFlowListResDTOList(cardOperFlowDTOList);
    }

    public CardOperFlowListResDTO transferCardOperFlowListResDTO(final CardOperFlowDTO cardOperFlowDTO) {
        if (Func.isEmpty(cardOperFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOperFlowListResDTO(cardOperFlowDTO);
    }

    public Page<CardOperFlowPageResDTO> transferCardOperFlowPageResDTOPage(final Page<CardOperFlowDTO> cardOperFlowDTOPage) {
        if (Func.isEmpty(cardOperFlowDTOPage) || Func.isEmpty(cardOperFlowDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOperFlowPageResDTOPage(cardOperFlowDTOPage);
    }

    public CardOperFlowShowResDTO transferCardOperFlowShowResDTO(final CardOperFlowDTO cardOperFlowDTO) {
        if (Func.isEmpty(cardOperFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOperFlowShowResDTO(cardOperFlowDTO);
    }

    public List<CardOperFlowShowResDTO> transferCardOperFlowShowResDTOList(final List<CardOperFlowDTO> cardOperFlowDTOList) {
        if (Func.isEmpty(cardOperFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOperFlowShowResDTOList(cardOperFlowDTOList);
    }
}
