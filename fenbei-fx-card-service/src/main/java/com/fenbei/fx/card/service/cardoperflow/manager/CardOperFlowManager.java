package com.fenbei.fx.card.service.cardoperflow.manager;

import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardoperflow.CardOperFlowDAO;
import com.fenbei.fx.card.dao.cardoperflow.po.CardOperFlowPO;
import com.fenbei.fx.card.service.cardoperflow.domain.CardOperFlowDO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import com.fenbei.fx.card.service.cardoperflow.converter.CardOperFlowConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 卡操作流水 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardOperFlowManager extends BaseManagerImpl<CardOperFlowDAO, CardOperFlowPO, CardOperFlowDTO, CardOperFlowConverter> {

    public static CardOperFlowManager me() {
        return SpringUtil.getBean(CardOperFlowManager.class);
    }

    public List<CardOperFlowListResDTO> list(final CardOperFlowListReqDTO cardOperFlowListReqDTO) {
        CardOperFlowDTO paramsDTO = CardOperFlowDO.me().buildListParamsDTO(cardOperFlowListReqDTO);

        List<CardOperFlowDTO> cardOperFlowDTOList = super.findList(paramsDTO);

        return CardOperFlowDO.me().transferCardOperFlowListResDTOList(cardOperFlowDTOList);
    }

    public CardOperFlowListResDTO listOne(final CardOperFlowListReqDTO cardOperFlowListReqDTO) {
        CardOperFlowDTO paramsDTO = CardOperFlowDO.me().buildListParamsDTO(cardOperFlowListReqDTO);

        CardOperFlowDTO cardOperFlowDTO = super.findOne(paramsDTO);

        return CardOperFlowDO.me().transferCardOperFlowListResDTO(cardOperFlowDTO);
    }

    public Page<CardOperFlowPageResDTO> pagination(final CardOperFlowPageReqDTO cardOperFlowPageReqDTO, final Integer current, final Integer size) {
        CardOperFlowDTO paramsDTO = CardOperFlowDO.me().buildPageParamsDTO(cardOperFlowPageReqDTO);

        Page<CardOperFlowDTO> cardOperFlowDTOPage = super.findPage(paramsDTO, current, size);

        return CardOperFlowDO.me().transferCardOperFlowPageResDTOPage(cardOperFlowDTOPage);
    }

    public Boolean add(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        CardOperFlowDO.me().checkCardOperFlowAddReqDTO(cardOperFlowAddReqDTO);

        CardOperFlowDTO addCardOperFlowDTO = CardOperFlowDO.me().buildAddCardOperFlowDTO(cardOperFlowAddReqDTO);

        return super.saveDTO(addCardOperFlowDTO);
    }

    public Boolean addAllColumn(final CardOperFlowAddReqDTO cardOperFlowAddReqDTO) {
        CardOperFlowDO.me().checkCardOperFlowAddReqDTO(cardOperFlowAddReqDTO);

        CardOperFlowDTO addCardOperFlowDTO = CardOperFlowDO.me().buildAddCardOperFlowDTO(cardOperFlowAddReqDTO);

        return super.saveAllColumn(addCardOperFlowDTO);
    }

    public Boolean addBatchAllColumn(final List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList) {
        CardOperFlowDO.me().checkCardOperFlowAddReqDTOList(cardOperFlowAddReqDTOList);

        List<CardOperFlowDTO> addBatchCardOperFlowDTOList = CardOperFlowDO.me().buildAddBatchCardOperFlowDTOList(cardOperFlowAddReqDTOList);

        return super.saveBatchAllColumn(addBatchCardOperFlowDTOList);
    }

    public CardOperFlowShowResDTO show(final String id) {
        CardOperFlowDTO cardOperFlowDTO = super.findById(id);

        return CardOperFlowDO.me().transferCardOperFlowShowResDTO(cardOperFlowDTO);
    }

    public List<CardOperFlowShowResDTO> showByIds(final List<String> ids) {
        CardOperFlowDO.me().checkIds(ids);

        List<CardOperFlowDTO> cardOperFlowDTOList = super.findBatchIds(ids);

        return CardOperFlowDO.me().transferCardOperFlowShowResDTOList(cardOperFlowDTOList);
    }

    public Boolean modify(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        CardOperFlowDO.me().checkCardOperFlowModifyReqDTO(cardOperFlowModifyReqDTO);

        CardOperFlowDTO modifyCardOperFlowDTO = CardOperFlowDO.me().buildModifyCardOperFlowDTO(cardOperFlowModifyReqDTO);

        return super.modifyById(modifyCardOperFlowDTO);
    }

    public Boolean modifyAllColumn(final CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO) {
        CardOperFlowDO.me().checkCardOperFlowModifyReqDTO(cardOperFlowModifyReqDTO);

        CardOperFlowDTO modifyCardOperFlowDTO = CardOperFlowDO.me().buildModifyCardOperFlowDTO(cardOperFlowModifyReqDTO);

        return super.modifyAllColumnById(modifyCardOperFlowDTO);
    }

    public Boolean removeByParams(final CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO) {
        CardOperFlowDO.me().checkCardOperFlowRemoveReqDTO(cardOperFlowRemoveReqDTO);

        CardOperFlowDTO removeCardOperFlowDTO = CardOperFlowDO.me().buildRemoveCardOperFlowDTO(cardOperFlowRemoveReqDTO);

        return super.remove(removeCardOperFlowDTO);
    }

    @Override
    protected CardOperFlowPO mapToPO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardOperFlowPO();
        }

        return BeanUtil.toBean(map, CardOperFlowPO.class);
    }

    @Override
    protected CardOperFlowDTO mapToDTO(final Map<String, Object> map) {
        if (Func.isEmpty(map)) {
            return new CardOperFlowDTO();
        }

        return BeanUtil.toBean(map, CardOperFlowDTO.class);
    }
}
