package com.fenbei.fx.card.common.enums;

import com.fenbei.fx.card.util.I18nUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 持卡人 Airwallex 状态
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/23
 */
public enum CardholderAirwallexStatusEnum {
    PENDING("PENDING", "审核中", -1),
    INCOMPLETE("INCOMPLETE", "审核中", -1),
    DISABLED("DISABLED", "已失效", CardholderStatusEnum.EXPIRED.getKey()),
    READY("READY", "生效中", CardholderStatusEnum.EFFECTIVE.getKey()),

    ;

    private final String status;
    private final String msg;
    private final Integer fbStatus;
    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("审核中", "cardholder.airwallex.status.pending");
        I18N_KEY_MAP.put("已失效", "cardholder.airwallex.status.disabled");
        I18N_KEY_MAP.put("生效中", "cardholder.airwallex.status.ready");
    }

    public static boolean isPendingOrIncomplete(String status){
        return (Objects.equals(status, PENDING.status) || Objects.equals(status, INCOMPLETE.status));
    }

    public static boolean isDisabled(String status){
        return Objects.equals(status, DISABLED.status);
    }

    public static boolean isReady(String status){
        return Objects.equals(status, READY.status);
    }

    public static CardholderAirwallexStatusEnum getEnum(String status){

        for(CardholderAirwallexStatusEnum item: values()){
            if (Objects.equals(status, item.getStatus())){
                return item;
            }
        }
        return null;
    }

    public String getStatus() {
        return status;
    }

    public String getMsg() {
        String i18nKey = I18N_KEY_MAP.get(msg);
        return i18nKey == null ? msg : I18nUtils.getMessage(i18nKey);
    }

    public Integer getFbStatus() {
        return fbStatus;
    }

    CardholderAirwallexStatusEnum(String status, String msg, Integer fbStatus) {
        this.status = status;
        this.msg = msg;
        this.fbStatus = fbStatus;
    }
}
