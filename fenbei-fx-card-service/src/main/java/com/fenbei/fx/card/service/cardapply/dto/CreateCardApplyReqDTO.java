package com.fenbei.fx.card.service.cardapply.dto;

import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 国际卡操作申请 添加 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateCardApplyReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 操作申请id
     */
    @ApiModelProperty(example = "0", value = "操作申请id")
    private String applyId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 企业商户ID
     */
    private String bankMchId;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    @ApiModelProperty(example = "0", value = "卡片形式：1-PHYSICAL、2-VIRTUAL")
    private Integer cardFormFactor;

    /**
     * 持卡人id
     */
    @ApiModelProperty(example = "0", value = "持卡人id")
    private String fxCardholderId;

    private String nationCode;

    /**
     * 申请人手机号+区号
     */
    @ApiModelProperty(example = "0", value = "申请人手机号+区号")
    private String applyerPhone;

    @ApiModelProperty(example = "0", value = "申请人手机号+区号")
    private String phone ;

    /**
     * 申请人邮箱
     */
    @ApiModelProperty(example = "0", value = "申请人邮箱")
    private String applyerEmail;

    /**
     * 申请人名
     */
    @ApiModelProperty(example = "0", value = "申请人名")
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    @ApiModelProperty(example = "0", value = "申请人姓")
    private String applyerLastName;

    /**
     * 卡用途
     */
    @ApiModelProperty(example = "0", value = "卡用途")
    private String cardPurpose;

    /**
     * 卡供应商
     */
    @ApiModelProperty(example = "AIRWALLEX", value = "发卡机构")
    private String cardPlatform;

    List<BaseAirwallexRpcDTO.Limit> showLimits ;

    private AddressDto postalAddressDto;

}
