package com.fenbei.fx.card.service.usercard.dto;

import com.fenbeitong.finhub.common.entity.KeyValueVO;
import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 */
@Data
public class UserCardTradeInfoDTO  implements Serializable {
    /**
     * "shopName": "中国银联无卡快捷支付业务二级商户信息测试",
     * 商户名称：对应库表merchant_name
     */
    private String shopName;
    /**
     * 结算金额，结算金额的折算币种
     */
    private TotalPrice totalPrice;
    /**
     * 交易金额
     */
    private TotalPrice tradePrice;
    /**
     * "bankAccountNo": "**** 7817",//卡号：masked_card_number,
     */
    private String bankAccountNo;
    /**
     * bankAccountNo
     */
    private String bankAccountNoMasked;
    /**
     * 核销状态
     */
    private KeyValueVO checkStatus;

    private KeyValueVO transactionType;
    /**
     * "createTime": "2022-12-28 16:52:57",
     */
    private Date createTime;
    /**
     * "createTimeShow": "2022/12/28 16:52",//交易时间transaction_date(格林威治时间) todo
     */
    private String createTimeShow;
    /**
     *  "monthType": "2022年12月",
     */
    private String monthType;
    /**
     * *  "orderId": "OBK221228165257145572972",//交易单号
     */
    private String orderId;
    /**
     * "tradeAddress":"USA-NEW",
     */
    private String tradeAddress;
    /**
     * "uncheckConsume": 0,//未核销金额
     */
    private BigDecimal uncheckConsume;
    /**
     *  "uncheckUse": 0,//未核销次数
     */
    private Integer uncheckUse;
    /**
     * 银行名称，通道
     */
    private String bankName;
}
