package com.fenbei.fx.card.service.card.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPhyscardResetPinResDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 卡ID
     */
    private String fxCardId;

    /**
     * 更新状态
     */
    private boolean resetStatus;

    /**
     * 正常和失败的msg
     */
    private String message;
}
