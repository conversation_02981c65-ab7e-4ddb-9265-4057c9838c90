package com.fenbei.fx.card.service.usercard.dto;

import com.fenbei.fx.card.common.constant.CoreConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户卡权限 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
public class UserCardPrivilegeDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 公司是否有海外卡权限
     * 0 无 1有
     */
    private Integer companyFxCardAuthStatus;

    /**
     * 员工是否有海外卡权限
     * 0 无 1有
     */
    private Integer employeeFxCardAuthStatus;


    /**
     * 公司是否有境内卡权限
     * 0 无 1有
     */
    private Integer companyBankCardAuthStatus;

    /**
     * 员工是否有境内卡权限
     * 0 无 1有
     */
    private Integer employeeBankCardAuthStatus;

    public UserCardPrivilegeDTO(){
        companyFxCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
        employeeFxCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
        companyBankCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
        employeeBankCardAuthStatus = CoreConstant.CARD_OPER_AUTH_NOT;
    }

}
