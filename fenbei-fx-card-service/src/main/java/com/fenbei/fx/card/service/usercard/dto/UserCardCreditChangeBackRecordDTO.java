package com.fenbei.fx.card.service.usercard.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午2:38
 */
@Data
public class UserCardCreditChangeBackRecordDTO extends BaseDTO implements Serializable {

    /**
     * 记录id
     */
     private String recordId;

    /**
     * 退还标题
     */
    private String backTitle;

    /**
     * 退还时间
     */
    private String backTime;

    /**
     * 退还金额 $1
     */
    private String backAmount;

    /**
     * 操作类型
     */
    private String operationTypeDesc;

    /**
     * 申请单单号
     */
    private String applyTransNo;

    /**
     * 原申请单单号
     */
     private String oriApplyTransNo;
    /**
     * 备用金类型,对应card_model
     *
     */
     private Integer pettyType;
    /**
     * 申请单类型
     */
    private String pettyTypeDesc;
    /**
     * 申请单类型
     */
    private Integer applyOrderType;
    /**
     * 申请单类型
     *
     */
    private String applyOrderTypeDesc = "企业发放";
    /**
     * 关联的额度申请单
     */
    private List<RelationApply> grantRecords;
}
