package com.fenbei.fx.card.service.cardholder.manager;

import com.fenbei.fx.card.common.enums.*;
import com.finhub.framework.common.manager.impl.BaseManagerImpl;
import com.finhub.framework.core.page.Page;

import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.CardHolderRpcRespDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.GetAllCardHoldersRpcReqDTO;
import com.fenbeitong.dech.api.model.dto.airwallex.cardholder.GetAllCardHoldersRpcRespDTO;
import com.fenbeitong.dech.api.service.airwallex.IAirWallexCardHolderService;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.dao.cardholder.CardholderDAO;
import com.fenbei.fx.card.dao.cardholder.po.CardholderPO;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.UpdateCardStatusReqDTO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardholder.converter.CardholderConverter;
import com.fenbei.fx.card.service.cardholder.dto.CardholderByPageReqDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderByPageResDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderEnableReqDTO;
import com.fenbei.fx.card.service.cardholder.dto.CardholderOrApplyListReqDTO;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 持卡人 Manager
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Slf4j
@Component
public class CardholderManager extends BaseManagerImpl<CardholderDAO, CardholderPO, CardholderDTO, CardholderConverter> {

    public static CardholderManager me() {
        return SpringUtil.getBean(CardholderManager.class);
    }

    @DubboReference
    private IAirWallexCardHolderService iAirWallexCardHolderService;

    public Page<CardholderByPageResDTO> findCardholderPageByStatus(CardholderByPageReqDTO reqDTO, CardholderShowStatusEnum showStatusEnum) {
        Page<CardholderByPageResDTO> resPage;

        QueryWrapper<CardholderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getPhone()), CardholderPO.DB_COL_PHONE, reqDTO.getPhone());
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getName()), CardholderPO.DB_COL_NAME, reqDTO.getName());
        queryWrapper.eq(CardholderPO.DB_COL_COMPANY_ID, reqDTO.getCompanyId());
        queryWrapper.eq(CardholderPO.DB_COL_DELETE_FLAG, 0);
        if(Objects.nonNull(showStatusEnum)) {
            //condition 未命中，会执行后面代码，但是sql里不会拼接
            queryWrapper.in( CardholderPO.DB_COL_HOLDER_STATUS, showStatusEnum.getStatus());
        }
        queryWrapper.orderByDesc(CardholderPO.DB_COL_CREATE_TIME);

        Page<CardholderDTO> page = this.findPage(queryWrapper, reqDTO.getOffset(), reqDTO.getPageSize());
        List<CardholderByPageResDTO> cardholderByPageResDTOS = convert2CardholderByPageResDTO(page.getRecords());
        if (page.getTotal() == 0) {
            return new Page<>();
        }
        resPage = new Page(page.getCurrent(), page.getSize(), page.getTotal());
        resPage.setRecords(cardholderByPageResDTOS);
        resPage.setTotal(page.getTotal());
        return resPage;
    }


    public List<CardholderDTO> findCardholderByEmployeeId(CardholderOrApplyListReqDTO reqDto) {
        if (StringUtils.isBlank(reqDto.getEmployeeId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardholderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDto.getPhone()), CardholderPO.DB_COL_PHONE, reqDto.getPhone());
        queryWrapper.eq(CardholderPO.DB_COL_EMPLOYEE_ID, reqDto.getEmployeeId());
        queryWrapper.eq(StringUtils.isNotBlank(reqDto.getCardPlatform()), CardholderPO.DB_COL_CARD_PLATFORM, reqDto.getCardPlatform());
        queryWrapper.eq(CardholderPO.DB_COL_DELETE_FLAG, 0);
        //queryWrapper.in(CardholderPO.DB_COL_HOLDER_STATUS, Arrays.asList(CardholderStatusEnum.EFFECTIVE.getKey()));
        List<CardholderDTO> list = this.findList(queryWrapper);

        return list;
    }

    private List<CardholderByPageResDTO> convert2CardholderByPageResDTO(List<CardholderDTO> list) {
        List<CardholderByPageResDTO> resList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)){
            return resList;
        }
        for (CardholderDTO cardholderDTO : list) {
            CardholderByPageResDTO resDTO = new CardholderByPageResDTO();
            resDTO.setFxCardholderId(cardholderDTO.getFxCardholderId());
            resDTO.setEmployeeId(cardholderDTO.getEmployeeId());
            resDTO.setName(cardholderDTO.getName());
            resDTO.setPhone(cardholderDTO.getPhone());
            resDTO.setCreateTime(cardholderDTO.getCreateTime());
            resDTO.setStatus(cardholderDTO.getHolderStatus());
            CardholderStatusEnum enumByStatus = CardholderStatusEnum.getEnum(cardholderDTO.getHolderStatus());
            if (null != enumByStatus) {
                resDTO.setStatusStr(enumByStatus.getMsg());
            }

            CardPlatformEnum cardPlatformEnum=CardPlatformEnum.getPlatform(cardholderDTO.getCardPlatform());
            resDTO.setCardPlatformName(cardPlatformEnum.getName());
            CardholderShowStatusEnum holderStatusEnum = CardholderShowStatusEnum.getEnumWhithHolderStatus(cardholderDTO.getHolderStatus());
            if(Objects.nonNull(holderStatusEnum)){
                resDTO.setShowStatusStr(holderStatusEnum.getDesc());
                resDTO.setShowStatus(holderStatusEnum.getKey());
            }

            resList.add(resDTO);
        }
        return resList;
    }

    public CardholderDTO findByCardholderId(String cardholderId) {
        if (StringUtils.isBlank(cardholderId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardholderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderPO.DB_COL_FX_CARDHOLDER_ID, cardholderId);
        queryWrapper.eq(CardholderPO.DB_COL_DELETE_FLAG, 0);
        return this.findOne(queryWrapper);
    }

    public CardholderDTO findByBankCardholderId(String bankCardholderId) {
        if (StringUtils.isBlank(bankCardholderId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        QueryWrapper<CardholderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardholderPO.DB_COL_BANK_CARDHOLDER_ID, bankCardholderId);
        return this.findOne(queryWrapper);
    }

    public boolean updateHolderStatus(String cardholderId, Integer holderStatus) {
        if (StringUtils.isBlank(cardholderId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        UpdateWrapper<CardholderPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq(CardholderPO.DB_COL_FX_CARDHOLDER_ID, cardholderId);
        updateWrapper.set(CardholderPO.DB_COL_HOLDER_STATUS, holderStatus);
        return this.update(updateWrapper);
    }


    public void allCompare() {
        GetAllCardHoldersRpcRespDTO allCardHolder = null;
        Integer pageNum = 0;
        Integer pageSize = 100;
        GetAllCardHoldersRpcReqDTO rpcReqDTO = new GetAllCardHoldersRpcReqDTO();
        do {
            rpcReqDTO.setPage_num(pageNum);
            rpcReqDTO.setPage_size(pageSize);

            //从bank分页获取持卡人数据
            allCardHolder = iAirWallexCardHolderService.getAllCardHolder(rpcReqDTO);
            if (Objects.isNull(allCardHolder)){
                break;
            }
            List<CardHolderRpcRespDTO> items = allCardHolder.getItems();
            if (CollectionUtils.isEmpty(items)){
                break;
            }

            items.forEach(p -> {
                try {
                    cardholderHandle(p);
                } catch (Exception e){
                    log.warn("持卡人信息更新比对失败，cardholderId={},status={}", p.getCardholder_id(), p.getStatus(), e);
                }
            });
            pageNum++;

        }while (allCardHolder.isHas_more() && pageNum < 1000);//防止死锁



    }

    private void cardholderHandle(CardHolderRpcRespDTO p) {
        //判断每一条数据的状态，如果是在银行审核中的，不处理，
        String status = p.getStatus();
        String cardholderId = p.getCardholder_id();

        CardholderAirwallexStatusEnum anEnum = CardholderAirwallexStatusEnum.getEnum(status);
        if (Objects.isNull(anEnum)){
            log.warn("当前持卡人状态不能被识别，cardholderId={},status={}", cardholderId, status);
            return;
        }
        if (CardholderAirwallexStatusEnum.isPendingOrIncomplete(status)){
            log.info("持卡人处于审核中，不用处理，cardholderId={},status={}", cardholderId, status);
            return;
        }

        //如果是其它状态的，查询本地数据，状态比对，如果一样的忽略
        CardholderDTO cardholderDTO = this.findByBankCardholderId(cardholderId);
        if (Objects.isNull(cardholderDTO)){
            log.warn("持卡人信息本地不存在，cardholderId={},status={}", cardholderId, status);
            return;
        }
        Integer holderStatus = cardholderDTO.getHolderStatus();
        if (Objects.equals(anEnum.getFbStatus(), holderStatus)){
            return;
        }

        //如果是本地禁用（是企业禁用），此条需要忽略
        if (CardholderStatusEnum.isDisabled(holderStatus)){
            log.warn("持卡人本地是被禁用状态，cardholderId={},status={},fbStatus={}", cardholderId, status, holderStatus);
            return;
        }

        //更新持卡人状态
        Integer fbStatus = anEnum.getFbStatus();
        String fxCardholderId = cardholderDTO.getFxCardholderId();
        log.info("更新持卡人状态，cardholderId={},status={}，fxCardholderId={}, fbStatus={}", cardholderId, status, fxCardholderId, fbStatus);
        boolean b = this.updateHolderStatus(fxCardholderId, fbStatus);
        if (!b){
            log.warn("持卡人状态更新失败，cardholderId={},status={},fbStatus={}", cardholderId, status, holderStatus);
        }
    }


    /**
     * 持卡人启用禁用
     */
    public void enableSwitch(CardholderEnableReqDTO reqDTO){
        log.info("持卡人状态更新,fxCardholderId={}, holderStatus={}",reqDTO.getFxCardholderId(), reqDTO.getHolderStatus());
        if (StringUtils.isBlank(reqDTO.getFxCardholderId()) || Objects.isNull(reqDTO.getHolderStatus())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        if(!CardholderStatusEnum.isEffective(reqDTO.getHolderStatus()) && !CardholderStatusEnum.isDisabled(reqDTO.getHolderStatus()) ){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        CardholderDTO cardholderDTO = this.findByCardholderId(reqDTO.getFxCardholderId());
        if (Objects.isNull(cardholderDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.CARDHOLDER_NOT_EXIST);
        }

        boolean b = this.updateHolderStatus(reqDTO.getFxCardholderId(), reqDTO.getHolderStatus());
        if (!b){
            log.error("持卡人状态更新失败,fxCardholderId={}, holderStatus={}",reqDTO.getFxCardholderId(), reqDTO.getHolderStatus());
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.CARDHOLDER_ENABLE_SUPPORT_UPDATE);
        }

        //把所有持卡人相关的有效卡都同时禁用
        String employeeId = cardholderDTO.getEmployeeId();
        String companyId = cardholderDTO.getCompanyId();
        List<CardDTO> cardDTOS = CardManager.me().userActiveCards(employeeId, companyId);
        if (CollectionUtils.isEmpty(cardDTOS)){
            return;
        }
        for (CardDTO cardDTO:cardDTOS){
            UpdateCardStatusReqDTO cardStatusReqDTO = new UpdateCardStatusReqDTO();
            cardStatusReqDTO.setFxCardId(cardDTO.getFxCardId());
            cardStatusReqDTO.setCardStatus(CardStatusEnum.DISABLE.getStatus());
            CardManager.me().updateCardStatus(cardStatusReqDTO);
        }
    }

}
