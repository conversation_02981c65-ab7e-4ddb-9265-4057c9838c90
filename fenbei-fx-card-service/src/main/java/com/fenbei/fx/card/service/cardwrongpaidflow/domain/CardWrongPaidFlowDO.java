package com.fenbei.fx.card.service.cardwrongpaidflow.domain;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardwrongpaidflow.po.CardWrongPaidFlowPO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowAddReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowShowResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.converter.CardWrongPaidFlowConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 错花还款流水表 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Slf4j
@Component
public class CardWrongPaidFlowDO extends BaseDO<CardWrongPaidFlowDTO, CardWrongPaidFlowPO, CardWrongPaidFlowConverter> {

    public static CardWrongPaidFlowDO me() {
        return SpringUtil.getBean(CardWrongPaidFlowDO.class);
    }

    public void checkCardWrongPaidFlowAddReqDTO(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        if (Func.isEmpty(cardWrongPaidFlowAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardWrongPaidFlowAddReqDTOList(final List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList) {
        if (Func.isEmpty(cardWrongPaidFlowAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardWrongPaidFlowModifyReqDTO(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        if (Func.isEmpty(cardWrongPaidFlowModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardWrongPaidFlowRemoveReqDTO(final CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO) {
        if (Func.isEmpty(cardWrongPaidFlowRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardWrongPaidFlowDTO buildListParamsDTO(final CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO) {
        return converter.convertToCardWrongPaidFlowDTO(cardWrongPaidFlowListReqDTO);
    }

    public CardWrongPaidFlowDTO buildPageParamsDTO(final CardWrongPaidFlowPageReqDTO cardWrongPaidFlowPageReqDTO) {
        return converter.convertToCardWrongPaidFlowDTO(cardWrongPaidFlowPageReqDTO);
    }

    public CardWrongPaidFlowDTO buildAddCardWrongPaidFlowDTO(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        return converter.convertToCardWrongPaidFlowDTO(cardWrongPaidFlowAddReqDTO);
    }

    public List<CardWrongPaidFlowDTO> buildAddBatchCardWrongPaidFlowDTOList(final List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList) {
        return converter.convertToCardWrongPaidFlowDTOList(cardWrongPaidFlowAddReqDTOList);
    }

    public CardWrongPaidFlowDTO buildModifyCardWrongPaidFlowDTO(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        return converter.convertToCardWrongPaidFlowDTO(cardWrongPaidFlowModifyReqDTO);
    }

    public CardWrongPaidFlowDTO buildRemoveCardWrongPaidFlowDTO(final CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO) {
        return converter.convertToCardWrongPaidFlowDTO(cardWrongPaidFlowRemoveReqDTO);
    }

    public List<CardWrongPaidFlowListResDTO> transferCardWrongPaidFlowListResDTOList(final List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList) {
        if (Func.isEmpty(cardWrongPaidFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardWrongPaidFlowListResDTOList(cardWrongPaidFlowDTOList);
    }

    public CardWrongPaidFlowListResDTO transferCardWrongPaidFlowListResDTO(final CardWrongPaidFlowDTO cardWrongPaidFlowDTO) {
        if (Func.isEmpty(cardWrongPaidFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardWrongPaidFlowListResDTO(cardWrongPaidFlowDTO);
    }

    public Page<CardWrongPaidFlowPageResDTO> transferCardWrongPaidFlowPageResDTOPage(final Page<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOPage) {
        if (Func.isEmpty(cardWrongPaidFlowDTOPage) || Func.isEmpty(cardWrongPaidFlowDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardWrongPaidFlowPageResDTOPage(cardWrongPaidFlowDTOPage);
    }

    public CardWrongPaidFlowShowResDTO transferCardWrongPaidFlowShowResDTO(final CardWrongPaidFlowDTO cardWrongPaidFlowDTO) {
        if (Func.isEmpty(cardWrongPaidFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardWrongPaidFlowShowResDTO(cardWrongPaidFlowDTO);
    }

    public List<CardWrongPaidFlowShowResDTO> transferCardWrongPaidFlowShowResDTOList(final List<CardWrongPaidFlowDTO> cardWrongPaidFlowDTOList) {
        if (Func.isEmpty(cardWrongPaidFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardWrongPaidFlowShowResDTOList(cardWrongPaidFlowDTOList);
    }
}
