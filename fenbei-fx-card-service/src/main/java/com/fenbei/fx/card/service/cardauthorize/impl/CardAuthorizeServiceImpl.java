package com.fenbei.fx.card.service.cardauthorize.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardauthorize.po.CardAuthorizePO;
import com.fenbei.fx.card.service.cardauthorize.CardAuthorizeService;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import com.fenbei.fx.card.service.cardauthorize.manager.CardAuthorizeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 国际卡授权表 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Service
public class CardAuthorizeServiceImpl extends BaseServiceImpl<CardAuthorizeManager, CardAuthorizePO, CardAuthorizeDTO> implements CardAuthorizeService {

    @Override
    public List<CardAuthorizeListResDTO> list(final CardAuthorizeListReqDTO cardAuthorizeListReqDTO) {
        return manager.list(cardAuthorizeListReqDTO);
    }

    @Override
    public CardAuthorizeListResDTO listOne(final CardAuthorizeListReqDTO cardAuthorizeListReqDTO) {
        return manager.listOne(cardAuthorizeListReqDTO);
    }

    @Override
    public Page<CardAuthorizePageResDTO> pagination(final CardAuthorizePageReqDTO cardAuthorizePageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardAuthorizePageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        return manager.add(cardAuthorizeAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO) {
        return manager.addAllColumn(cardAuthorizeAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList) {
        return manager.addBatchAllColumn(cardAuthorizeAddReqDTOList);
    }

    @Override
    public CardAuthorizeShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardAuthorizeShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        return manager.modify(cardAuthorizeModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO) {
        return manager.modifyAllColumn(cardAuthorizeModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO) {
        return manager.removeByParams(cardAuthorizeRemoveReqDTO);
    }

    @Override
    public CardAuthorizeDTO findByTradeId(String tradeId,String tradeType) {
        return manager.findByTradeId(tradeId,tradeType);
    }

    @Override
    public CardAuthorizeDTO findByTradeIdAndSub(String tradeId, String subTradeId, String tradeType) {
        return manager.findByTradeIdAndSub(tradeId,subTradeId,tradeType);
    }
}
