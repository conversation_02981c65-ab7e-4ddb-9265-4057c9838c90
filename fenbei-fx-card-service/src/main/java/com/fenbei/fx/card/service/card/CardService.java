package com.fenbei.fx.card.service.card;

import com.fenbei.fx.card.service.card.dto.*;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.page.Page;
import cn.hutool.extra.spring.SpringUtil;

import java.util.List;

/**
 * 国际卡 Service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
public interface CardService extends BaseService<CardDTO> {

    static CardService me() {
        return SpringUtil.getBean(CardService.class);
    }

    /**
     * First查询
     *
     * @param cardListReqDTO 入参DTO
     * @return
     */
    CardListResDTO listOne(CardListReqDTO cardListReqDTO);

    /**
     * 分页
     *
     * @param cardPageReqDTO 入参DTO
     * @param current        当前页
     * @param size           每页大小
     * @return
     */
    Page<CardPageResDTO> pagination(CardPageReqDTO cardPageReqDTO, Integer current, Integer size);

    Page<CardPageResDTO> allCard(CardPageReqDTO cardPageReqDTO, Integer current, Integer size);

    /**
     * 新增
     *
     * @param cardAddReqDTO 入参DTO
     * @return
     */
    Boolean add(CardAddReqDTO cardAddReqDTO);

    /**
     * 新增(所有字段)
     *
     * @param cardAddReqDTO 入参DTO
     * @return
     */
    Boolean addAllColumn(CardAddReqDTO cardAddReqDTO);

    /**
     * 批量新增(所有字段)
     *
     * @param cardAddReqDTOList 入参DTO
     * @return
     */
    Boolean addBatchAllColumn(List<CardAddReqDTO> cardAddReqDTOList);

    /**
     * 详情
     *
     * @param fxCardId
     * @return
     */
    CardShowResDTO cardDetail(String fxCardId);

    CardSumBalanceResDTO cardSumBalance(final String companyId);
    List<CardSumBalanceListResDTO> cardSumBalanceList(String companyId);

    /**
     * 详情
     *
     * @param cardId
     * @return
     */
    CardDTO cardDetailByCardId(String cardId);

    CardDTO cardDetailByFxCardId(String fxCardId);

    CardDTO cardDetailByBankAccountNo(String bankAccountNo);

    List<CardDTO> queryByEmployeeId(String employeeId);

    /**
     * 批量详情
     *
     * @param ids 主键IDs
     * @return
     */
    List<CardShowResDTO> showByIds(List<String> ids);

    /**
     * 修改
     *
     * @param cardModifyReqDTO 入参DTO
     * @return
     */
    Boolean modify(CardModifyReqDTO cardModifyReqDTO);

    /**
     * 修改(所有字段)
     *
     * @param cardModifyReqDTO 入参DTO
     * @return
     */
    Boolean modifyAllColumn(CardModifyReqDTO cardModifyReqDTO);

    /**
     * 参数删除
     *
     * @param cardRemoveReqDTO 入参DTO
     * @return
     */
    Boolean removeByParams(CardRemoveReqDTO cardRemoveReqDTO);

    /**
     * 更新卡状态
     */
    Boolean updateCardStatus(UpdateCardStatusReqDTO updateCardStatusReqDTO);

    Boolean updateLimits (UpdateCardStatusReqDTO updateCardStatusReqDTO);

    Boolean activateCard(ActivateCardReqDTO activateCardReqDTO);


    CardCaptchaApplyResDTO captchaApply(CardCaptchaApplyReqDTO cardCaptchaApplyReqDTO);

    Boolean physcardActivate(CardPhyscardActiveReqDTO cardPhyscardActiveReqDTO);

    Boolean physcardResetPinCheck(CardPhyscardResetPinCheckReqDTO physcardResetPinCheckDTO);

    Boolean physcardResetPin(CardPhyscardResetPinReqDTO physcardResetPinReqDTO);

    Page<CardWebListPageResDTO> queryCardListForNoAuth(CardWebListPageReqDTO cardWebListPageReqDTO, int current, int size);
}
