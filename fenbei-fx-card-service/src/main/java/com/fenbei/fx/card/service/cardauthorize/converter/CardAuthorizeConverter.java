package com.fenbei.fx.card.service.cardauthorize.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardauthorize.po.CardAuthorizePO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡授权表 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardAuthorizeConverter extends BaseConverter<CardAuthorizeDTO, CardAuthorizePO> {

    static CardAuthorizeConverter me() {
        return SpringUtil.getBean(CardAuthorizeConverter.class);
    }

    CardAuthorizeDTO convertToCardAuthorizeDTO(CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO);

    CardAuthorizeDTO convertToCardAuthorizeDTO(CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO);

    CardAuthorizeDTO convertToCardAuthorizeDTO(CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO);

    CardAuthorizeDTO convertToCardAuthorizeDTO(CardAuthorizeListReqDTO cardAuthorizeListReqDTO);

    CardAuthorizeDTO convertToCardAuthorizeDTO(CardAuthorizePageReqDTO cardAuthorizePageReqDTO);

    CardAuthorizeShowResDTO convertToCardAuthorizeShowResDTO(CardAuthorizeDTO cardAuthorizeDTO);

    List<CardAuthorizeShowResDTO> convertToCardAuthorizeShowResDTOList(List<CardAuthorizeDTO> cardAuthorizeDTOList);

    CardAuthorizeListResDTO convertToCardAuthorizeListResDTO(CardAuthorizeDTO cardAuthorizeDTO);

    List<CardAuthorizeListResDTO> convertToCardAuthorizeListResDTOList(List<CardAuthorizeDTO> cardAuthorizeDTOList);

    List<CardAuthorizeDTO> convertToCardAuthorizeDTOList(List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList);

    CardAuthorizePageResDTO convertToCardAuthorizePageResDTO(CardAuthorizeDTO cardAuthorizeDTO);

    List<CardAuthorizePageResDTO> convertToCardAuthorizePageResDTOList(List<CardAuthorizeDTO> cardAuthorizeDTOList);

    Page<CardAuthorizePageResDTO> convertToCardAuthorizePageResDTOPage(Page<CardAuthorizeDTO> cardAuthorizeDTOPage);
}
