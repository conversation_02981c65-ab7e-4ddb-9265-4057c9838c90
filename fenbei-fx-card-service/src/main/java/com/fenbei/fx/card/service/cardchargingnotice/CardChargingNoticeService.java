package com.fenbei.fx.card.service.cardchargingnotice;

import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeDTO;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeRetryDTO;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-31 下午3:09
 */

public interface CardChargingNoticeService {

    /**
     * 计费通知落裤并异步通知fx_pay
     * @param cardChargingNoticeDTO
     * @return
     */
    int saveChargingNotice(CardChargingNoticeDTO cardChargingNoticeDTO);

    void noticeRetry(CardChargingNoticeRetryDTO cardChargingNoticeRetryDTO);
}
