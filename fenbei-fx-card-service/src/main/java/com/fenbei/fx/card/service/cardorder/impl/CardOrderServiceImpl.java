package com.fenbei.fx.card.service.cardorder.impl;

import com.fenbei.fx.card.dao.cardorder.po.CardOrderPO;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderManager;
import com.fenbei.fx.card.service.usercard.dto.UserCardTradeInfosDTO;
import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;

/**
 * 国际卡订单 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Service
public class CardOrderServiceImpl extends BaseServiceImpl<CardOrderManager, CardOrderPO, CardOrderDTO> implements CardOrderService {

    @Override
    public List<CardOrderListResDTO> list(final CardOrderListReqDTO cardOrderListReqDTO) {
        return manager.list(cardOrderListReqDTO);
    }

    @Override
    public CardOrderListResDTO listOne(final CardOrderListReqDTO cardOrderListReqDTO) {
        return manager.listOne(cardOrderListReqDTO);
    }

    @Override
    public Page<CardTradeInfoWebPageResDTO> pagination(final CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardTradeInfoWebPageReqDTO, current, size);
    }

    @Override
    public Page<CardTradeInfoAppPageResDTO> pagination(final CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO, final int current,
        final int size) {
        return manager.pagination(cardTradeInfoAppPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        return manager.add(cardOrderAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        return manager.addAllColumn(cardOrderAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardOrderAddReqDTO> cardOrderAddReqDTOList) {
        return manager.addBatchAllColumn(cardOrderAddReqDTOList);
    }

    @Override
    public CardOrderShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public CardTradeInfoAppShowResDTO appShow(final String bizNo, final Integer transactionType) {
        return manager.appShow(bizNo,transactionType);
    }

    @Override
    public List<CardOrderShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        return manager.modify(cardOrderModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        return manager.modifyAllColumn(cardOrderModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardOrderRemoveReqDTO cardOrderRemoveReqDTO) {
        return manager.removeByParams(cardOrderRemoveReqDTO);
    }
    /**
     * 查询最近交易信息
     * @return List<UserCardTradeInfosDTO>
     */
    @Override
    public UserCardTradeInfosDTO latestTradeInfo(final String fxCardId) {
        return manager.queryUnCheckInfoByFxCard(fxCardId);
    }

    @Override
    public CardOrderDTO findByTradeId(String tradeId,Integer tradeType) {
        return manager.findByTradeId(tradeId,tradeType);
    }
    @Override
    public List<CardOrderDTO> findListByTradeId(String tradeId,Integer tradeType){
        return manager.findListByTradeId(tradeId,tradeType);
    }

    @Override
    public CardOrderDTO findByTradeIdAndSub(String tradeId, String subTradeId, Integer tradeType) {
        return manager.findByTradeIdAndSub(tradeId,subTradeId,tradeType);
    }

    @Override
    public UserCardTradeInfosDTO latestTradeInfoByEmployeeId(final String employeeId) {
        return manager.queryUnCheckInfo(employeeId);
    }


    @Override
    public Boolean appRemark(CardTradeInfoAppRemarkReqDTO cardTradeInfoAppRemarkReqDTO) {
        return manager.appRemark(cardTradeInfoAppRemarkReqDTO);
    }

    @Override
    public void costBind(List<String> bizNos, Integer costId) {
        manager.costBind(bizNos ,costId);
    }

    @Override
    public void costUnBind(List<String> bizNos, Integer costId) {
        manager.costUnBind(bizNos ,costId);
    }

    @Override
    public boolean applyInit(FxVerificationReqDTO initDTO) {
        return manager.applyInit(initDTO) ;
    }

    @Override
    public boolean applyDel(FxVerificationReqDTO delDTO) {
        return manager.applyDel(delDTO);
    }

    @Override
    public boolean applyDisCardDel(FxVerificationReqDTO delDTO) {
        return manager.applyDisCardDel(delDTO);
    }

    @Override
    public boolean applyDoneNew(FxVerificationReqDTO doneDTO) {
        return manager.applyDoneNew(doneDTO);
    }

    @Override
    public void updateExchangeRate(String id, BigDecimal rate) {
        manager.updateExchangeRate(id, rate);
    }

    @Override
    public void updateOrderShow(String id) {
        manager.updateOrderShow(id);
    }

    @Override
    public void updateHistoryValue(String id) {
        manager.updateExchangeRate2025(id);

    }
}
