package com.fenbei.fx.card.service.usercard.dto;

import com.finhub.framework.common.dto.BaseDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午2:38
 */
@Data
public class UserCardCreditChangeGrantRecordDTO extends BaseDTO implements Serializable {

    /**
     * 记录id
     */
     private String recordId;
    /**
     * 标题
     */
     private String applyTitle;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 卡模式
     */
    private String cardModelDesc;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 申请状态
     */
    private String applyStatusDesc;

    /**
     * 申请金额 $1
     */
    private String applyAmount;

    /**
     * 申请单编号
     */
    private String applyTransNo;

    /**
     * 备用金类型,对应card_model
     *
     */
    private Integer pettyType;
    /**
     * 申请单类型
     */
    private String pettyTypeDesc;
    /**
     * 申请单类型: 企业发放43,系统下发44
     */
    private Integer applyOrderType = 43;
    /**
     * 申请单类型
     *
     */
    private String applyOrderTypeDesc = "企业发放";

    /**
     * 申请单ID web下发=applyOrderId，申请单下发=applyId
     */
    private String applyId;

    /**
     * 发放单ID
     */
    private String applyOrderId;
}
