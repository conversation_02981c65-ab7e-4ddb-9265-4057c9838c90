package com.fenbei.fx.card.service.cardverificationflow.domain;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardverificationflow.po.CardVerificationFlowPO;
import com.fenbei.fx.card.service.cardverificationflow.converter.CardVerificationFlowConverter;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 核销记录表 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Slf4j
@Component
public class CardVerificationFlowDO extends BaseDO<CardVerificationFlowDTO, CardVerificationFlowPO, CardVerificationFlowConverter> {

    public static CardVerificationFlowDO me() {
        return SpringUtil.getBean(CardVerificationFlowDO.class);
    }

    public void checkCardVerificationFlowAddReqDTO(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        if (Func.isEmpty(cardVerificationFlowAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardVerificationFlowAddReqDTOList(final List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList) {
        if (Func.isEmpty(cardVerificationFlowAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardVerificationFlowModifyReqDTO(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        if (Func.isEmpty(cardVerificationFlowModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardVerificationFlowRemoveReqDTO(final CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO) {
        if (Func.isEmpty(cardVerificationFlowRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardVerificationFlowDTO buildListParamsDTO(final CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO) {
        return converter.convertToCardVerificationFlowDTO(cardVerificationFlowListReqDTO);
    }

    public CardVerificationFlowDTO buildPageParamsDTO(final CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO) {
        return converter.convertToCardVerificationFlowDTO(cardVerificationFlowPageReqDTO);
    }

    public CardVerificationFlowDTO buildAddCardVerificationFlowDTO(final CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO) {
        return converter.convertToCardVerificationFlowDTO(cardVerificationFlowAddReqDTO);
    }

    public List<CardVerificationFlowDTO> buildAddBatchCardVerificationFlowDTOList(final List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList) {
        return converter.convertToCardVerificationFlowDTOList(cardVerificationFlowAddReqDTOList);
    }

    public CardVerificationFlowDTO buildModifyCardVerificationFlowDTO(final CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO) {
        return converter.convertToCardVerificationFlowDTO(cardVerificationFlowModifyReqDTO);
    }

    public CardVerificationFlowDTO buildRemoveCardVerificationFlowDTO(final CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO) {
        return converter.convertToCardVerificationFlowDTO(cardVerificationFlowRemoveReqDTO);
    }

    public List<CardVerificationFlowListResDTO> transferCardVerificationFlowListResDTOList(final List<CardVerificationFlowDTO> cardVerificationFlowDTOList) {
        if (Func.isEmpty(cardVerificationFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardVerificationFlowListResDTOList(cardVerificationFlowDTOList);
    }

    public CardVerificationFlowListResDTO transferCardVerificationFlowListResDTO(final CardVerificationFlowDTO cardVerificationFlowDTO) {
        if (Func.isEmpty(cardVerificationFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardVerificationFlowListResDTO(cardVerificationFlowDTO);
    }

    public Page<CardVerificationFlowPageResDTO> transferCardVerificationFlowPageResDTOPage(final Page<CardVerificationFlowDTO> cardVerificationFlowDTOPage) {
        if (Func.isEmpty(cardVerificationFlowDTOPage) || Func.isEmpty(cardVerificationFlowDTOPage.getRecords())) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardVerificationFlowPageResDTOPage(cardVerificationFlowDTOPage);
    }

    public CardVerificationFlowShowResDTO transferCardVerificationFlowShowResDTO(final CardVerificationFlowDTO cardVerificationFlowDTO) {
        if (Func.isEmpty(cardVerificationFlowDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardVerificationFlowShowResDTO(cardVerificationFlowDTO);
    }

    public List<CardVerificationFlowShowResDTO> transferCardVerificationFlowShowResDTOList(final List<CardVerificationFlowDTO> cardVerificationFlowDTOList) {
        if (Func.isEmpty(cardVerificationFlowDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardVerificationFlowShowResDTOList(cardVerificationFlowDTOList);
    }
}
