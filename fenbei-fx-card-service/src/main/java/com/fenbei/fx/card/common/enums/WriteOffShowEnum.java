package com.fenbei.fx.card.common.enums;


import com.fenbei.fx.card.util.I18nUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单详情核销状态
 */
@Getter
@AllArgsConstructor
public enum WriteOffShowEnum {

    NO_COST(1, "添加费用"),
    BIND_COST(2, "已创建费用"),
    NO_COMMIT(3,"已创建核销单，未提交审批"),
    PENDING(4,"核销单审批中"),
    PASS(5, "核销单审批通过");

    private int key;
    private String value;


    private static final Map<String, String> I18N_KEY_MAP = new HashMap<>();

    static {
        I18N_KEY_MAP.put("添加费用", "cost.none");
        I18N_KEY_MAP.put("已创建费用", "cost.bind");
        I18N_KEY_MAP.put("已创建核销单，未提交审批", "cost.not.commit");
        I18N_KEY_MAP.put("核销单审批中", "cost.pending");
        I18N_KEY_MAP.put("核销单审批通过", "cost.pass");
    }

    public static WriteOffShowEnum getEnum(Integer key) {
        if (key == null) {
            return null;
        }
        for (WriteOffShowEnum item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return null;
    }

    public static WriteOffShowEnum getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        for (WriteOffShowEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    public static String getNameFromCode(Integer code) {
        return getEnum(code).getValue();
    }

    public String getValue() {
        String i18nKey = I18N_KEY_MAP.get(value);
        return i18nKey == null ? value : I18nUtils.getMessage(i18nKey);
    }

}
