package com.fenbei.fx.card.common.threadpool;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/25 15:33
 * @Description: 线程池
 */
public class AirThreadPoolExecutor {

    /**
     * 线程池
     */
    public static ThreadPoolExecutor airQueryExecutorInstance = new ThreadPoolExecutor(
            3,
            10,
            120,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(100));

    public static ThreadPoolExecutor consumeExecutorInstance = new ThreadPoolExecutor(
        3,
        10,
        120,
        TimeUnit.SECONDS,
        new ArrayBlockingQueue<>(100));
    public static ThreadPoolExecutor cardRetryExecutorInstance = new ThreadPoolExecutor(
        3,
        10,
        120,
        TimeUnit.SECONDS,
        new ArrayBlockingQueue<>(100));

}
