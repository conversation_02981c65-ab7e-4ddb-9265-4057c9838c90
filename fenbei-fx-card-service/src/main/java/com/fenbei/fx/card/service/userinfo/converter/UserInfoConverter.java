package com.fenbei.fx.card.service.userinfo.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.userinfo.po.UserInfoPO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoAddReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoListResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoModifyReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoPageResDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoRemoveReqDTO;
import com.fenbei.fx.card.service.userinfo.dto.UserInfoShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 用户信息 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-27
 */
@Mapper(config = BaseConverterConfig.class)
public interface UserInfoConverter extends BaseConverter<UserInfoDTO, UserInfoPO> {

    static UserInfoConverter me() {
        return SpringUtil.getBean(UserInfoConverter.class);
    }

    UserInfoDTO convertToUserInfoDTO(UserInfoAddReqDTO userInfoAddReqDTO);

    UserInfoDTO convertToUserInfoDTO(UserInfoModifyReqDTO userInfoModifyReqDTO);

    UserInfoDTO convertToUserInfoDTO(UserInfoRemoveReqDTO userInfoRemoveReqDTO);

    UserInfoDTO convertToUserInfoDTO(UserInfoListReqDTO userInfoListReqDTO);

    UserInfoDTO convertToUserInfoDTO(UserInfoPageReqDTO userInfoPageReqDTO);

    UserInfoShowResDTO convertToUserInfoShowResDTO(UserInfoDTO userInfoDTO);

    List<UserInfoShowResDTO> convertToUserInfoShowResDTOList(List<UserInfoDTO> userInfoDTOList);

    UserInfoListResDTO convertToUserInfoListResDTO(UserInfoDTO userInfoDTO);

    List<UserInfoListResDTO> convertToUserInfoListResDTOList(List<UserInfoDTO> userInfoDTOList);

    List<UserInfoDTO> convertToUserInfoDTOList(List<UserInfoAddReqDTO> userInfoAddReqDTOList);

    UserInfoPageResDTO convertToUserInfoPageResDTO(UserInfoDTO userInfoDTO);

    List<UserInfoPageResDTO> convertToUserInfoPageResDTOList(List<UserInfoDTO> userInfoDTOList);

    Page<UserInfoPageResDTO> convertToUserInfoPageResDTOPage(Page<UserInfoDTO> userInfoDTOPage);
}
