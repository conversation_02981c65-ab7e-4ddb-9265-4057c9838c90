package com.fenbei.fx.card.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.harmony.api.unicode.CodeService;
import com.fenbeitong.harmony.api.unicode.dto.CodeReqDTO;
import com.fenbeitong.harmony.api.unicode.dto.CodeResDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class CommonCodeGeneration {

    @DubboReference
    private CodeService codeService;

    /**
     * 平台统一单据编辑生成工具类
     * @param companyId
     * @param billTypeTag 规则编码标识(前缀)
     * @param count 一次生成的数量
     * @return
     */
    public List<String> generatioCodes(String companyId,String billTypeTag,int count) {
        CodeReqDTO reqDTO = new CodeReqDTO();
        reqDTO.setCompanyId(companyId);
        reqDTO.setBillTypeTag(billTypeTag);
        reqDTO.setCount(count<=0 ? 1 : count);
        try {
            log.info("发号器param:"+ JSONObject.toJSONString(reqDTO));
            CodeResDTO codeResDTO = codeService.produce(reqDTO);
            return codeResDTO.getUnique();
        } catch (Throwable e) {
            log.error("Unicode-Client RPC调用发号器生成编码失败，req:{}", reqDTO, e);
        }
        return null;
    }
}
