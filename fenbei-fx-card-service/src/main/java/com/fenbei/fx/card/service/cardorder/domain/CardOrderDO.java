package com.fenbei.fx.card.service.cardorder.domain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fenbei.fx.card.common.enums.ApplyBindStatus;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.service.cardorder.dto.*;
import com.fenbeitong.finhub.common.constant.CheckStatusEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.converter.MapstructContextHolder;
import com.finhub.framework.core.domain.BaseDO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.exception.constant.enums.MessageResponseEnum;

import com.fenbeitong.finhub.auth.UserAuthHolder;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fenbei.fx.card.dao.card.po.CardPO;
import com.fenbei.fx.card.dao.cardorder.po.CardOrderPO;
import com.fenbei.fx.card.service.cardorder.converter.CardOrderConverter;
import com.fenbei.fx.card.util.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 国际卡订单 DO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Component
public class CardOrderDO extends BaseDO<CardOrderDTO, CardOrderPO, CardOrderConverter> {

    public static CardOrderDO me() {
        return SpringUtil.getBean(CardOrderDO.class);
    }

    public void checkCardOrderAddReqDTO(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        if (Func.isEmpty(cardOrderAddReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardOrderAddReqDTOList(final List<CardOrderAddReqDTO> cardOrderAddReqDTOList) {
        if (Func.isEmpty(cardOrderAddReqDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkIds(final List<String> ids) {
        if (Func.isEmpty(ids)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "集合不能为空且大小大于0");
        }
    }

    public void checkCardOrderModifyReqDTO(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        if (Func.isEmpty(cardOrderModifyReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public void checkCardOrderRemoveReqDTO(final CardOrderRemoveReqDTO cardOrderRemoveReqDTO) {
        if (Func.isEmpty(cardOrderRemoveReqDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, "入参不能为空");
        }
    }

    public CardOrderDTO buildListParamsDTO(final CardOrderListReqDTO cardOrderListReqDTO) {
        return converter.convertToCardOrderDTO(cardOrderListReqDTO);
    }

    public QueryWrapper<CardOrderPO> buildWebPageQueryWrapper(final List<String> fxCardIdList, final CardTradeInfoWebPageReqDTO cardTradeInfoWebPageReqDTO) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, UserAuthHolder.getCurrentUser().getCompany_id());

        if (Func.isNotEmpty(fxCardIdList)) {
            queryWrapper.in(CardOrderPO.DB_COL_FX_CARD_ID, fxCardIdList);
        }

        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getTransactionType())) {
            queryWrapper.eq(CardOrderPO.DB_COL_TYPE, cardTradeInfoWebPageReqDTO.getTransactionType());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getTransactionGeDate())) {
            queryWrapper.ge(CardOrderPO.DB_COL_TRADE_TIME, cardTradeInfoWebPageReqDTO.getTransactionGeDate());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getTransactionLeDate())) {
            queryWrapper.le(CardOrderPO.DB_COL_TRADE_TIME, cardTradeInfoWebPageReqDTO.getTransactionLeDate());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getCreateGeDate())) {
            queryWrapper.ge(CardOrderPO.DB_COL_CREATE_TIME, cardTradeInfoWebPageReqDTO.getCreateGeDate());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getCreateLeDate())) {
            queryWrapper.le(CardOrderPO.DB_COL_CREATE_TIME, cardTradeInfoWebPageReqDTO.getCreateLeDate());
        }
        if (Func.isNotBlank(cardTradeInfoWebPageReqDTO.getTransactionCurrency())) {
            queryWrapper.eq(CardOrderPO.DB_COL_TRADE_CURRENCY, cardTradeInfoWebPageReqDTO.getTransactionCurrency());
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getTransactionGeAmount())) {
            queryWrapper.ge(CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(cardTradeInfoWebPageReqDTO.getTransactionGeAmount()));
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getTransactionLeAmount())) {
            queryWrapper.le(CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen((cardTradeInfoWebPageReqDTO.getTransactionLeAmount())));
        }
        if (Func.isNotNull(cardTradeInfoWebPageReqDTO.getCheckStatus())) {
            queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, cardTradeInfoWebPageReqDTO.getCheckStatus());
        }
        // 按交易时间降序排列
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_TRADE_TIME);

        return queryWrapper;
    }

    public LambdaQueryWrapper<CardOrderPO> buildAppPageQueryWrapper(CardTradeInfoAppPageReqDTO cardTradeInfoAppPageReqDTO) {
        LambdaQueryWrapper<CardOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CardOrderPO::getCompanyId, UserAuthHolder.getCurrentUser().getCompany_id());
        queryWrapper.eq(CardOrderPO::getEmployeeId, UserAuthHolder.getCurrentUser().getUser_id());
        queryWrapper.eq(CardOrderPO::getOrderShow, 1);
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getTransactionType())) {
            queryWrapper.eq(CardOrderPO::getType, cardTradeInfoAppPageReqDTO.getTransactionType());
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getTradeGeTime())) {
            queryWrapper.ge(CardOrderPO::getTradeTime, cardTradeInfoAppPageReqDTO.getTradeGeTime());
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getTradeLeTime())) {
            queryWrapper.le(CardOrderPO::getTradeTime, cardTradeInfoAppPageReqDTO.getTradeLeTime());
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getTradeGeAmount())) {
            queryWrapper.ge(CardOrderPO::getTradeAmount, cardTradeInfoAppPageReqDTO.getTradeGeAmount());
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getTradeLeAmount())) {
            queryWrapper.le(CardOrderPO::getTradeAmount, cardTradeInfoAppPageReqDTO.getTradeLeAmount());
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getCheckStatus())) {
            //未核销的数据
            if (Objects.equals(CheckStatusEnum.UNCHECK.getKey(), cardTradeInfoAppPageReqDTO.getCheckStatus())){
                queryWrapper.eq(CardOrderPO::getType, TransactionTypeEnum.CONSUME.getKey());
                queryWrapper.eq(CardOrderPO::getCheckStatus, CheckStatusEnum.UNCHECK.getKey());
                queryWrapper.gt(CardOrderPO::getUncheckedAmount, BigDecimal.ZERO);
                queryWrapper.eq(CardOrderPO::getApplyBind, ApplyBindStatus.NO.getKey());
            } else{
                queryWrapper.eq(CardOrderPO::getCheckStatus, cardTradeInfoAppPageReqDTO.getCheckStatus());
            }
        }
        if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getBizNo()) && cardTradeInfoAppPageReqDTO.isAppShow()){
            queryWrapper.and(wrapper -> wrapper.and(wrapper1 -> wrapper1.eq(CardOrderPO::getBizNo, cardTradeInfoAppPageReqDTO.getBizNo()).in(CardOrderPO::getType,TransactionTypeEnum.getTradeEnumValues()))
                .or(wrapper2 ->  wrapper2.eq(CardOrderPO::getOriBizNo, cardTradeInfoAppPageReqDTO.getBizNo()).in(CardOrderPO::getType,TransactionTypeEnum.getTradeEnumValues())));
        }else {
            if (Func.isNotNull(cardTradeInfoAppPageReqDTO.getBizNo())) {
                queryWrapper.eq(CardOrderPO::getBizNo, cardTradeInfoAppPageReqDTO.getBizNo());
                queryWrapper.in(CardOrderPO::getType,TransactionTypeEnum.getTradeEnumValues());
            }
        }

        // 按交易时间降序排列
        queryWrapper.orderByDesc(CardOrderPO::getTradeTime);

        return queryWrapper;
    }


    public QueryWrapper<CardOrderPO> buildStereoPageQueryWrapper(final CardTradeInfoStereoPageReqDTO reqDTO) {
        QueryWrapper<CardOrderPO> queryWrapper = new QueryWrapper<>();
        if (Func.isNotBlank(reqDTO.getCompanyId())) {
            queryWrapper.eq(CardOrderPO.DB_COL_COMPANY_ID, reqDTO.getCompanyId());
        }

        if (Func.isNotBlank(reqDTO.getCardHolderName())) {
            queryWrapper.likeRight(CardPO.DB_COL_NAME_ON_CARD, reqDTO.getCardHolderName());
        }
        if (Func.isNotBlank(reqDTO.getMaskedCardNumber())) {
            queryWrapper.likeRight(CardPO.DB_COL_BANK_CARD_NO, reqDTO.getMaskedCardNumber());
        }
        if (Func.isNotNull(reqDTO.getBankCardType())) {
            queryWrapper.eq(CardPO.DB_COL_CARD_FORM_FACTOR, reqDTO.getBankCardType());
        }

        if (Func.isNotNull(reqDTO.getTransactionType())) {
            queryWrapper.eq(CardOrderPO.DB_COL_TYPE, reqDTO.getTransactionType());
        }
        if (Func.isNotNull(reqDTO.getTransactionGeDate())) {
            queryWrapper.ge(CardOrderPO.DB_COL_TRADE_TIME, reqDTO.getTransactionGeDate());
        }
        if (Func.isNotNull(reqDTO.getTransactionLeDate())) {
            queryWrapper.le(CardOrderPO.DB_COL_TRADE_TIME, reqDTO.getTransactionLeDate());
        }
        if (Func.isNotNull(reqDTO.getCreateGeDate())) {
            queryWrapper.ge(CardOrderPO.DB_COL_CREATE_TIME, reqDTO.getCreateGeDate());
        }
        if (Func.isNotNull(reqDTO.getCreateLeDate())) {
            queryWrapper.le(CardOrderPO.DB_COL_CREATE_TIME, reqDTO.getCreateLeDate());
        }
        if (Func.isNotBlank(reqDTO.getTransactionCurrency())) {
            queryWrapper.eq(CardOrderPO.DB_COL_TRADE_CURRENCY, reqDTO.getTransactionCurrency());
        }
        if (Func.isNotNull(reqDTO.getTransactionGeAmount())) {
            queryWrapper.ge(CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen(reqDTO.getTransactionGeAmount()));
        }
        if (Func.isNotNull(reqDTO.getTransactionLeAmount())) {
            queryWrapper.le(CardOrderPO.DB_COL_TRADE_AMOUNT, BigDecimalUtils.yuan2fen((reqDTO.getTransactionLeAmount())));
        }
        if (Func.isNotNull(reqDTO.getCheckStatus())) {
            queryWrapper.eq(CardOrderPO.DB_COL_CHECK_STATUS, reqDTO.getCheckStatus());
        }
        if (Func.isNotNull(reqDTO.getCardPlatform())) {
            queryWrapper.eq(CardOrderPO.DB_BASE_CARD_PLATFORM, reqDTO.getCardPlatform());
        }
        // 按交易时间降序排列
        queryWrapper.orderByDesc(CardOrderPO.DB_COL_TRADE_TIME);

        return queryWrapper;
    }

    public CardOrderDTO buildAddCardOrderDTO(final CardOrderAddReqDTO cardOrderAddReqDTO) {
        return converter.convertToCardOrderDTO(cardOrderAddReqDTO);
    }

    public List<CardOrderDTO> buildAddBatchCardOrderDTOList(final List<CardOrderAddReqDTO> cardOrderAddReqDTOList) {
        return converter.convertToCardOrderDTOList(cardOrderAddReqDTOList);
    }

    public CardOrderDTO buildModifyCardOrderDTO(final CardOrderModifyReqDTO cardOrderModifyReqDTO) {
        return converter.convertToCardOrderDTO(cardOrderModifyReqDTO);
    }

    public CardOrderDTO buildRemoveCardOrderDTO(final CardOrderRemoveReqDTO cardOrderRemoveReqDTO) {
        return converter.convertToCardOrderDTO(cardOrderRemoveReqDTO);
    }

    public List<CardOrderListResDTO> transferCardOrderListResDTOList(final List<CardOrderDTO> cardOrderDTOList) {
        if (Func.isEmpty(cardOrderDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOrderListResDTOList(cardOrderDTOList);
    }

    public CardOrderListResDTO transferCardOrderListResDTO(final CardOrderDTO cardOrderDTO) {
        return converter.convertToCardOrderListResDTO(cardOrderDTO);
    }

    public Page<CardTradeInfoWebPageResDTO> transferCardTradeInfoWebPageResDTOPage(final Page<CardOrderDTO> cardOrderDTOPage, final Map<String, CardPO> fxCardIdAndCardPOMap) {
        MapstructContextHolder.putContext(fxCardIdAndCardPOMap);
        return converter.convertToCardTradeInfoWebPageResDTOPage(cardOrderDTOPage);
    }

    public Page<CardTradeInfoAppPageResDTO> transferCardTradeInfoAppPageResDTOPage(final Page<CardOrderDTO> cardOrderDTOPage, final Map<String, CardPO> fxCardIdAndCardPOMap) {
        MapstructContextHolder.putContext(fxCardIdAndCardPOMap);
        return converter.convertToCardTradeInfoAppPageResDTOPage(cardOrderDTOPage);
    }

    public Page<CardTradeInfoStereoPageResDTO> transferCardTradeInfoStereoPageResDTOPage(final Page<CardOrderDTO> cardOrderDTOPage, final Map<String, CardPO> fxCardIdAndCardPOMap) {
        MapstructContextHolder.putContext(fxCardIdAndCardPOMap);
        return converter.convertToCardTradeInfoStereoPageResDTOPage(cardOrderDTOPage);
    }

    public CardOrderShowResDTO transferCardOrderShowResDTO(final CardOrderDTO cardOrderDTO) {
        if (Func.isEmpty(cardOrderDTO)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOrderShowResDTO(cardOrderDTO);
    }

    public CardTradeInfoAppShowResDTO transferCardTradeInfoAppShowResDTO(final CardOrderDTO cardOrderDTO, final Map<String, CardPO> fxCardIdAndCardPOMap) {
        MapstructContextHolder.putContext(fxCardIdAndCardPOMap);
        return converter.convertToCardTradeInfoAppShowResDTO(cardOrderDTO);
    }

    public List<CardOrderShowResDTO> transferCardOrderShowResDTOList(final List<CardOrderDTO> cardOrderDTOList) {
        if (Func.isEmpty(cardOrderDTOList)) {
            MessageResponseEnum.COMMON_ERROR.newException(ResponseCodeEnum.SUCCESS, I18nUtils.getMessage("NO_FIND_RECORDS"));
        }

        return converter.convertToCardOrderShowResDTOList(cardOrderDTOList);
    }
}
