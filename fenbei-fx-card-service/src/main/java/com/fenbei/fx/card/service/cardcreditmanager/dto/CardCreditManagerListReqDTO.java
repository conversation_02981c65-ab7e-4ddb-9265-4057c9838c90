package com.fenbei.fx.card.service.cardcreditmanager.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡额度申请退回管理表 列表 ReqDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditManagerListReqDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    private String id;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 类型: 1额度申请,2额度退回
     */
    private Integer applyType;

    /**
     * 状态: 1成功,0失败
     */
    private Integer applyStatus;

    /**
     * 申请单ID
     */
    private String applyTransNo;

    /**
     * 关联业务方id
     */
    private String bizNo;

    /**
     * 申请单批次: 同一申请单可追加
     */
    private String applyTransBatchNo;

    /**
     * 额度退回时关联申请单ID
     */
    private String oriApplyTransNo;

    /**
     * 申请单meaningNo
     */
    private String applyMeaningNo;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 币种 美元-USD
     */
    private String currency;

    /**
     * 申请金额
     */
    private BigDecimal amount;

    /**
     * 已核销金额
     */
    private BigDecimal writenOffAmount;

    /**
     * 待核销金额
     */
    private BigDecimal uncheckedAmount;

    /**
     * 核销中金额
     */
    private BigDecimal writingOffAmount;

    /**
     * 无需核销金额
     */
    private BigDecimal unwriteOffAmount;

    /**
     * 可用余额
     */
    private BigDecimal avalibleAmount;

    /**
     * 已退还金额
     */
    private BigDecimal returnedAmount;

    /**
     * 开卡渠道
     */
    private String cardPlatform;

    /**
     * 操作类型 4申请额度,5退还额度,51企业回收额度,52系统回收额度
     */
    private Integer operationType;

    /**
     * 发起人
     */
    private String operationUserId;

    /**
     * 申请人姓名
     */
    private String operationUserName;

    /**
     * 申请人部门
     */
    private String operationUserDept;

    /**
     * 卡模式: 1.普通模式,2备用金模式
     */
    private Integer cardModel;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    private Integer deleteFlag;

    /**
     * 费用类别
     */
    private String costType;

    /**
     * 费用归属
     */
    private String costAttribution;

    /**
     * 美元兑人民币汇率
     */
    private BigDecimal usdCnyExchangeRate;

    /**
     * 人名币金额
     */
    private BigDecimal cnyAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 申请事由补充说明
     */
    private String applyReasonDesc;

    /**
     * 费用归属名称
     */
    private String costTypeName;

    /**
     * 审批单类型15海外卡额度申请单19备用金申请单40额度发放单
     */
    private Integer applyOrderType;

}
