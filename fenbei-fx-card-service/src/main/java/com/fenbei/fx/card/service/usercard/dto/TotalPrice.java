package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TotalPrice implements Serializable {
    /**
     *    "totalPrice": {
     *    "color": "#333333",
     *    "price": 1.0,
     *    "showPrice": "-¥1.00" //正负号+消费币种 + 金额
     *    },
     */
    /**
     * 颜色
     */
    private String color;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 显示金额
     */
    private String showPrice;

}
