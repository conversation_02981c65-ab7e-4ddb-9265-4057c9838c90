package com.fenbei.fx.card.service.usercard.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 额度发放记录,用于关联
 * <AUTHOR>
 */
@Data
public class UserCardCreditGrantRecordDetail implements Serializable {
    /**
     * 费控审批单ID
     */
    private String applyId;
    /**
     * 支付额度申请单ID
     */
    private String applyTransNo;
    /**
     * 申请事由
     */
    private String applyTitle;
    /**
     * 申请事由
     */
    private String applyReason;
    /**
     * 申请时间
     */
    private String applyTime;
    /**
     * 未核销金额,为了客户端要求
     */
    private BigDecimal useBalance;

    private String useBalanceShow;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    private String applyAmountShow;
    /**
     * 是否自定义申请单
     */
    private Boolean isVirtualCustomForm = Boolean.TRUE;

    private BigDecimal operationAmount;
}
