package com.fenbei.fx.card.service.cardcreditapplyorder.converter;

import com.finhub.framework.core.converter.BaseConverter;
import com.finhub.framework.core.converter.BaseConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.dao.cardcreditapplyorder.po.CardCreditApplyOrderPO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderAddReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderShowResDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡额度发放单 Converter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-14
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardCreditApplyOrderConverter extends BaseConverter<CardCreditApplyOrderDTO, CardCreditApplyOrderPO> {

    static CardCreditApplyOrderConverter me() {
        return SpringUtil.getBean(CardCreditApplyOrderConverter.class);
    }

    CardCreditApplyOrderDTO convertToCardCreditApplyOrderDTO(CardCreditApplyOrderAddReqDTO cardCreditApplyOrderAddReqDTO);

    CardCreditApplyOrderDTO convertToCardCreditApplyOrderDTO(CardCreditApplyOrderModifyReqDTO cardCreditApplyOrderModifyReqDTO);

    CardCreditApplyOrderDTO convertToCardCreditApplyOrderDTO(CardCreditApplyOrderRemoveReqDTO cardCreditApplyOrderRemoveReqDTO);

    CardCreditApplyOrderDTO convertToCardCreditApplyOrderDTO(CardCreditApplyOrderListReqDTO cardCreditApplyOrderListReqDTO);

    CardCreditApplyOrderDTO convertToCardCreditApplyOrderDTO(CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO);

    CardCreditApplyOrderShowResDTO convertToCardCreditApplyOrderShowResDTO(CardCreditApplyOrderDTO cardCreditApplyOrderDTO);

    List<CardCreditApplyOrderShowResDTO> convertToCardCreditApplyOrderShowResDTOList(List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList);

    CardCreditApplyOrderListResDTO convertToCardCreditApplyOrderListResDTO(CardCreditApplyOrderDTO cardCreditApplyOrderDTO);

    List<CardCreditApplyOrderListResDTO> convertToCardCreditApplyOrderListResDTOList(List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList);

    List<CardCreditApplyOrderDTO> convertToCardCreditApplyOrderDTOList(List<CardCreditApplyOrderAddReqDTO> cardCreditApplyOrderAddReqDTOList);

    CardCreditApplyOrderPageResDTO convertToCardCreditApplyOrderPageResDTO(CardCreditApplyOrderDTO cardCreditApplyOrderDTO);

    List<CardCreditApplyOrderPageResDTO> convertToCardCreditApplyOrderPageResDTOList(List<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOList);

    Page<CardCreditApplyOrderPageResDTO> convertToCardCreditApplyOrderPageResDTOPage(Page<CardCreditApplyOrderDTO> cardCreditApplyOrderDTOPage);
}
