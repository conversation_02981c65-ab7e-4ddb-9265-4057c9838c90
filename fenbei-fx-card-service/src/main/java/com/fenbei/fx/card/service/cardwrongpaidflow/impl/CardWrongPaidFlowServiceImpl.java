package com.fenbei.fx.card.service.cardwrongpaidflow.impl;

import com.finhub.framework.common.service.impl.BaseServiceImpl;
import com.finhub.framework.core.page.Page;

import com.fenbei.fx.card.dao.cardwrongpaidflow.po.CardWrongPaidFlowPO;
import com.fenbei.fx.card.service.cardwrongpaidflow.CardWrongPaidFlowService;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowAddReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowListResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowPageResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.dto.CardWrongPaidFlowShowResDTO;
import com.fenbei.fx.card.service.cardwrongpaidflow.manager.CardWrongPaidFlowManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 错花还款流水表 ServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-20
 */
@Slf4j
@Service
public class CardWrongPaidFlowServiceImpl extends BaseServiceImpl<CardWrongPaidFlowManager, CardWrongPaidFlowPO, CardWrongPaidFlowDTO> implements CardWrongPaidFlowService {

    @Override
    public List<CardWrongPaidFlowListResDTO> list(final CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO) {
        return manager.list(cardWrongPaidFlowListReqDTO);
    }

    @Override
    public CardWrongPaidFlowListResDTO listOne(final CardWrongPaidFlowListReqDTO cardWrongPaidFlowListReqDTO) {
        return manager.listOne(cardWrongPaidFlowListReqDTO);
    }

    @Override
    public Page<CardWrongPaidFlowPageResDTO> pagination(final CardWrongPaidFlowPageReqDTO cardWrongPaidFlowPageReqDTO, final Integer current,
        final Integer size) {
        return manager.pagination(cardWrongPaidFlowPageReqDTO, current, size);
    }

    @Override
    public Boolean add(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        return manager.add(cardWrongPaidFlowAddReqDTO);
    }

    @Override
    public Boolean addAllColumn(final CardWrongPaidFlowAddReqDTO cardWrongPaidFlowAddReqDTO) {
        return manager.addAllColumn(cardWrongPaidFlowAddReqDTO);
    }

    @Override
    public Boolean addBatchAllColumn(final List<CardWrongPaidFlowAddReqDTO> cardWrongPaidFlowAddReqDTOList) {
        return manager.addBatchAllColumn(cardWrongPaidFlowAddReqDTOList);
    }

    @Override
    public CardWrongPaidFlowShowResDTO show(final String id) {
        return manager.show(id);
    }

    @Override
    public List<CardWrongPaidFlowShowResDTO> showByIds(final List<String> ids) {
        return manager.showByIds(ids);
    }

    @Override
    public Boolean modify(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        return manager.modify(cardWrongPaidFlowModifyReqDTO);
    }

    @Override
    public Boolean modifyAllColumn(final CardWrongPaidFlowModifyReqDTO cardWrongPaidFlowModifyReqDTO) {
        return manager.modifyAllColumn(cardWrongPaidFlowModifyReqDTO);
    }

    @Override
    public Boolean removeByParams(final CardWrongPaidFlowRemoveReqDTO cardWrongPaidFlowRemoveReqDTO) {
        return manager.removeByParams(cardWrongPaidFlowRemoveReqDTO);
    }
}
