package com.fenbei.fx.card.service.usercard.dto;

import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.finhub.framework.common.dto.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡 DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardInfoDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 卡id
     */
    private String fxCardId;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡编号
     */
    private String bankCardNo;

    /**
     * mask银行卡编号
     */
    private String maskBankCardNo;

    /**
     * 公司账户id
     */
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatform;

    /**
     * 发卡渠道 AIRWALLEX
     */
    private String cardPlatformName;

    /**
     * 发卡渠道 图标
     */
    private String cardPlatformIcon;

    /**
     * 发卡的品牌 VISA
     */
    private String cardBrand;

    /**
     * 发卡的品牌 图标
     */
    private String cardBrandIcon;

    /**
     * 发卡时间
     */
    private Date cardPublicTime;

    /**
     * 展示状态：1.待审核 2.审核中 3.审核失败 4.生效中 5.已失效 6.已拒绝 99.其他
     */
    private Integer cardShowStatus;

    /**
     * 展示状态 1.待审核 2.审核中 3.审核失败 4.生效中 5.已失效 6.已拒绝 99.其他
     */
    private KeyValueVO cardShowStatusVo;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    private Integer cardStatus;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private KeyValueVO activeStatusVo;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    private Integer activeStatus;


    /**
     * 是否可以激活 0.不可以 1.可以
     * 虚拟卡默认不可以
     * 实体卡目前是发卡5天后可激活
     */
    private Integer canActiveFlag = 0;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 卡用途
     */
    private String cardPurpose;

    /**
     * 币种 USD
     */
    private String currency;

    /**
     * 币种符号 $
     */
    private String currencySymbol;

    /**
     * 币种 美元
     */
    private String currencyName;

    /**
     * 卡可用余额
     */
    private BigDecimal balance;

    /**
     * 卡可用展示美元余额
     */
    private String showUSDBalance;

    /**
     * 管控规则：频率，币种，金额
     */
    private String cardLimits;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 手机号
     */
    private String phone;
    /**
     *
     */
    private String phoneAreaCode = "+86";

    /**
     * 实体卡支付密码
     */
    private String cardPin;  /**


     * 连连实体卡开关,测试阶段默认开启，上线后默认先关闭
     * 0 无 1有
     */
    private Integer cardFormFactorLianlianStatus=0;





}
