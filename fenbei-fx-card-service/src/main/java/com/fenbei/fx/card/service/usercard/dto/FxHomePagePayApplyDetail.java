package com.fenbei.fx.card.service.usercard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡-首页-申请详情
 */
@Data
public class FxHomePagePayApplyDetail implements Serializable {

    /**
     * id
     */
    private String bizNo;

    /**
     * 申请单ID
     */
    private String applyTransNo;
    /**
     * 申请单标题
     */
    private String applyName;

    private String applyReason;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    private BigDecimal applyAmount;
    /**
     * 显示金额
     */
    private String applyAmountShow;

    private BigDecimal balance;
    /**
     * 显示金额
     */
    private String balanceShow;
    /**
     * 是否可追加申请
     */
    private Boolean applyAppendFlag;
    /**
     * 是否可额度申请
     */
    private Boolean applyFlag = true;
    /**
     * 申请状态
     */
    private Integer applyStatus;
    /**
     * 申请状态描述
     */
    private String applyStatusDesc;
    /**
     * 费用类别
     */
    private String costType;
    /**
     * 费用类别描述
     */
    private String costTypeDesc;

}
