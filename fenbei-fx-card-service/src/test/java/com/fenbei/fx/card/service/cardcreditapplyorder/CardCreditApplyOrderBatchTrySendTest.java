package com.fenbei.fx.card.service.cardcreditapplyorder;

import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderBatchTrySendReqVO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderBatchTrySendResVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * 批量发放额度测试
 */
@SpringBootTest
public class CardCreditApplyOrderBatchTrySendTest {

    @Autowired
    private CardCreditApplyOrderService cardCreditApplyOrderService;

    @Test
    public void testBatchTrySend() {
        // 准备测试数据
        String companyId = "test_company_id";
        List<String> applyOrderIds = Arrays.asList(
            "apply_order_id_1",
            "apply_order_id_2",
            "apply_order_id_3"
        );

        CardCreditApplyOrderBatchTrySendReqVO reqVO = new CardCreditApplyOrderBatchTrySendReqVO();
        reqVO.setApplyOrderIds(applyOrderIds);

        // 执行批量发放
        CardCreditApplyOrderBatchTrySendResVO result = cardCreditApplyOrderService.batchTrySend(companyId, reqVO);

        // 验证结果
        System.out.println("批量发放结果：");
        System.out.println("总数：" + result.getTotalCount());
        System.out.println("成功数：" + result.getSuccessCount());
        System.out.println("失败数：" + result.getFailCount());

        if (result.getResults() != null) {
            for (CardCreditApplyOrderBatchTrySendResVO.BatchTrySendResult batchResult : result.getResults()) {
                System.out.println(String.format("申请单ID: %s, 成功: %s, 消息: %s", 
                    batchResult.getApplyOrderId(), batchResult.getSuccess(), batchResult.getMessage()));
            }
        }
    }
}
