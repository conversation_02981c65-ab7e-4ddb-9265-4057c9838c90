package com.fenbei.fx.card.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * EmailCheckUtils 测试类
 */
public class EmailCheckUtilsTest {

    @Test
    public void testEmailFormat() {
        // 测试正确的邮箱格式
        assertTrue(EmailCheckUtils.emailFormat("<EMAIL>"));
        assertTrue(EmailCheckUtils.emailFormat("<EMAIL>"));
        assertTrue(EmailCheckUtils.emailFormat("<EMAIL>"));

        // 测试错误的邮箱格式
        assertFalse(EmailCheckUtils.emailFormat("invalid-email"));
        assertFalse(EmailCheckUtils.emailFormat("@qq.com"));
        assertFalse(EmailCheckUtils.emailFormat("test@"));
        assertFalse(EmailCheckUtils.emailFormat(""));
        assertFalse(EmailCheckUtils.emailFormat(null));
    }

    @Test
    public void testIsAllowedPersonalEmailDomain() {
        // 测试允许的中国个人邮箱域名
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));

        // 测试允许的国际个人邮箱域名
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));

        // 测试不允许的企业邮箱域名
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));

        // 测试边界情况
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain(""));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain(null));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("invalid-email"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("@qq.com"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("test@"));
        assertFalse(EmailCheckUtils.isAllowedPersonalEmailDomain("@"));

        // 测试大小写不敏感
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
        assertTrue(EmailCheckUtils.isAllowedPersonalEmailDomain("<EMAIL>"));
    }
}
