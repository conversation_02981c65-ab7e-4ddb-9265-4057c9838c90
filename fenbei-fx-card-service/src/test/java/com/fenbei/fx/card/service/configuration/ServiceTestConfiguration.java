package com.fenbei.fx.card.service.configuration;

import cn.hutool.extra.spring.EnableSpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableSpringUtil
@ComponentScan(basePackages = {"com.fenbei.fx.card.service.*.converter", "com.fenbei.fx.card.service.*.domain"})
public class ServiceTestConfiguration {

}
