<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fenbei.fx.card</groupId>
        <artifactId>fenbei-fx-card</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-fx-card-service</artifactId>
    <version>${fenbei-fx-card.version}</version>
    <packaging>jar</packaging>

    <properties>
        <skip-maven-deploy>true</skip-maven-deploy>
    </properties>

    <dependencies>
        <!-- Module -->
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>travel-rule-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-dech-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-xxl-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>eventbus_2.11</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>harmony-unicode-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>usercenter-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong.fxpay</groupId>
            <artifactId>fenbei-fx-pay-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>fenbei-pay-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbei.mls</groupId>
            <artifactId>mls-i18n</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-agent-core</artifactId>
            <version>8.9.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>saas-plus-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbeitong.expense.management</groupId>
            <artifactId>expense-management-api</artifactId>
            <version>${expense-management-api.version}</version>
        </dependency>

        <!-- http工具 start -->
        <dependency>
            <groupId>com.jakewharton.retrofit</groupId>
            <artifactId>retrofit2-rxjava2-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-api</artifactId>
        </dependency>
        <!-- http工具 end -->
        <!-- END -->
    </dependencies>
</project>

