package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardCreditApplyOrderService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.ApplyOrderSubTypeEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderTypeEnum;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.service.cardcreditapplyorder.CardCreditApplyOrderService;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.*;
import com.fenbei.fx.card.util.BigDecimalUtils;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.saasplus.api.model.dto.attribution.CostAttributionDetailReqDTO;
import com.fenbeitong.saasplus.api.model.dto.attribution.CostAttributionStorageDetailRespDTO;
import com.fenbeitong.saasplus.api.service.cost.ICostAttributionStorageService;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * 额度申请单管理
 * <AUTHOR>
 */
@Slf4j
@Validated
@DubboService
public class CardCreditApplyOrderServiceRpcImpl implements ICardCreditApplyOrderService {

    @Autowired
    private CardCreditApplyOrderService cardCreditApplyOrderService;

    @DubboReference
    private ICostAttributionStorageService iCostAttributionStorageService;


    @DubboReference
    protected IBaseEmployeeExtService iBaseEmployeeExtService;
    /**
     * 创建额度申请单
     * @param createReqDTO 创建请求参数
     * @return 创建结果
     */
    @Override
    public CardCreditApplyOrderCreateRpcRespDTO createApplyOrder(CardCreditApplyOrderCreateRpcReqDTO createReqDTO) {
        FinhubLogger.info("创建额度申请单：{}", JsonUtils.toJson(createReqDTO));
        CardCreditApplyOrderCreateRpcRespDTO respDTO = new CardCreditApplyOrderCreateRpcRespDTO();

        try {
            // 转换请求参数
            CardCreditApplyOrderAddReqVO orderAddReqVO = convertToAddReqVO(createReqDTO);
            String companyId = createReqDTO.getCompanyId();
            String applyId = createReqDTO.getApplyId();
            CostAttributionDetailReqDTO detailReqDTO = new CostAttributionDetailReqDTO();
            detailReqDTO.setCompanyId(companyId);
            detailReqDTO.setApplyIds(Lists.newArrayList(applyId));
            List<CostAttributionStorageDetailRespDTO> costAttributionStorageDetailRespDTOS = iCostAttributionStorageService.queryCostAttributionStorage(detailReqDTO);
            if(CollectionUtils.isEmpty(costAttributionStorageDetailRespDTOS)){
                throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT, "费用归属配置不存在");
            }
            String jsonStore = JsonUtils.toJson(costAttributionStorageDetailRespDTOS.get(0));
            CostInfo costInfo=JsonUtils.toObj(jsonStore, CostInfo.class);
            orderAddReqVO.setCostInfo(costInfo);

            String fxCreditApplyOrderId = BizIdUtils.getFxCreditApplyOrderId();
            orderAddReqVO.setApplyOrderId(fxCreditApplyOrderId);
            orderAddReqVO.setApplyAmount(createReqDTO.getApplyAmount());

            if(StringUtils.isBlank(createReqDTO.getCreaterName())){
                EmployeeContract employeeContract = iBaseEmployeeExtService.queryEmployeeInfo(createReqDTO.getCreaterId(), companyId);
                orderAddReqVO.setCreaterName(employeeContract.getName());
            }
            // 调用服务创建申请单
            CardCreditApplyOrderDTO cardCreditApplyOrderDTO = cardCreditApplyOrderService.createApplyOrder(orderAddReqVO);
            BeanUtils.copyProperties(cardCreditApplyOrderDTO, respDTO);

        } catch (FxCardException fxCardException) {
            log.error("创建额度申请单异常：", fxCardException);
            throw fxCardException;
        } catch (Exception e) {
            log.error("创建额度申请单异常：", e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION, "创建额度申请单异常，请稍后重试");
        }
        return respDTO;
    }

    /**
     * 尝试发放额度
     * @param trySendReqDTO 发放请求参数
     * @return 发放结果
     */
    @Override
    public CardCreditApplyOrderTrySendRpcRespDTO trySend(CardCreditApplyOrderTrySendRpcReqDTO trySendReqDTO) {
        FinhubLogger.info("尝试发放额度：{}", JsonUtils.toJson(trySendReqDTO));
        CardCreditApplyOrderTrySendRpcRespDTO respDTO = new CardCreditApplyOrderTrySendRpcRespDTO();
        try {
            // 转换请求参数
            CardCreditApplyOrderTrySendReqVO serviceReqDTO = new CardCreditApplyOrderTrySendReqVO();
            serviceReqDTO.setApplyOrderId(trySendReqDTO.getApplyOrderId());

            // 调用服务发放额度
            CardCreditApplyOrderTrySendResVO trySendResVO = cardCreditApplyOrderService.trySend(trySendReqDTO.getCompanyId(), serviceReqDTO);

            BeanUtils.copyProperties(trySendResVO, respDTO);

        } catch (FxCardException fxCardException) {
            log.error("尝试发放额度异常：", fxCardException);
            respDTO.setSuccess(false);
            respDTO.setMessage(fxCardException.getMessage());
        } catch (Exception e) {
            log.error("尝试发放额度异常：", e);
            respDTO.setSuccess(false);
            respDTO.setMessage("系统异常，请稍后重试");
        }

        return respDTO;
    }

    /**
     * 批量发放额度
     * @param batchTrySendReqDTO 批量发放请求参数
     * @return 批量发放结果
     */
    @Override
    public CardCreditApplyOrderBatchTrySendRpcRespDTO batchTrySend(CardCreditApplyOrderBatchTrySendRpcReqDTO batchTrySendReqDTO) {
        FinhubLogger.info("批量发放额度：{}", JsonUtils.toJson(batchTrySendReqDTO));
        CardCreditApplyOrderBatchTrySendRpcRespDTO respDTO = new CardCreditApplyOrderBatchTrySendRpcRespDTO();

        try {
            // 转换请求参数
            CardCreditApplyOrderBatchTrySendReqVO serviceReqVO = new CardCreditApplyOrderBatchTrySendReqVO();
            serviceReqVO.setApplyOrderIds(batchTrySendReqDTO.getApplyOrderIds());

            // 调用服务批量发放额度
            CardCreditApplyOrderBatchTrySendResVO batchResult = cardCreditApplyOrderService.batchTrySend(
                batchTrySendReqDTO.getCompanyId(), serviceReqVO);

            // 转换响应结果
            BeanUtils.copyProperties(batchResult, respDTO);

            // 转换详细结果列表
            if (CollectionUtils.isNotEmpty(batchResult.getResults())) {
                List<CardCreditApplyOrderBatchTrySendRpcRespDTO.BatchTrySendResult> rpcResults = new ArrayList<>();
                for (CardCreditApplyOrderBatchTrySendResVO.BatchTrySendResult result : batchResult.getResults()) {
                    CardCreditApplyOrderBatchTrySendRpcRespDTO.BatchTrySendResult rpcResult =
                        new CardCreditApplyOrderBatchTrySendRpcRespDTO.BatchTrySendResult();
                    BeanUtils.copyProperties(result, rpcResult);
                    rpcResults.add(rpcResult);
                }
                respDTO.setResults(rpcResults);
            }

            respDTO.setSuccess(batchResult.getSuccessCount() > 0);
            respDTO.setMessage(String.format("批量发放完成：总数 %d，成功 %d，失败 %d",
                batchResult.getTotalCount(), batchResult.getSuccessCount(), batchResult.getFailCount()));

        } catch (FxCardException fxCardException) {
            log.error("批量发放额度业务异常：", fxCardException);
            respDTO.setSuccess(false);
            respDTO.setMessage(fxCardException.getMessage());
        } catch (Exception e) {
            log.error("批量发放额度异常：", e);
            respDTO.setSuccess(false);
            respDTO.setMessage("系统异常，请稍后重试");
        }

        return respDTO;
    }




    /**
     * 查询额度申请单详情
     * @param queryReqDTO 查询请求参数
     * @return 申请单详情
     */
    @Override
    public CardCreditApplyOrderDetailRpcRespDTO getApplyOrderDetail(CardCreditApplyOrderDetailRpcReqDTO queryReqDTO) {
        log.info("查询额度申请单详情：{}", JsonUtils.toJson(queryReqDTO));

        if (StringUtils.isBlank(queryReqDTO.getApplyOrderId())) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        try {
            // 查询申请单详情
            CardCreditApplyOrderShowResDTO orderDetail = cardCreditApplyOrderService.showByApplyOrderId(queryReqDTO.getCompanyId(), queryReqDTO.getApplyOrderId());

            if (orderDetail == null) {
                return null;
            }

            // 转换响应结果
            return convertToDetailRpcRespDTO(orderDetail);

        } catch (FinhubException e) {
            log.warn("查询额度申请单详情异常：{}", JsonUtils.toJson(queryReqDTO), e);
            throw e;
        } catch (Exception e) {
            log.error("查询额度申请单详情报错：{}", JsonUtils.toJson(queryReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }



    /**
     * 转换创建请求参数
     */
    private CardCreditApplyOrderAddReqVO convertToAddReqVO(CardCreditApplyOrderCreateRpcReqDTO createReqDTO) {
        CardCreditApplyOrderAddReqVO creditApplyOrderAddReqVO = new CardCreditApplyOrderAddReqVO();
        BeanUtils.copyProperties(createReqDTO, creditApplyOrderAddReqVO);
        creditApplyOrderAddReqVO.setCompanyId(createReqDTO.getCompanyId());
        creditApplyOrderAddReqVO.setApplyId(createReqDTO.getApplyId());
        creditApplyOrderAddReqVO.setMeaningNo(createReqDTO.getMeaningNo());
        creditApplyOrderAddReqVO.setApplyOrderType(ObjectUtils.isEmpty(createReqDTO.getApplyOrderType())? ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal() :createReqDTO.getApplyOrderType());
        creditApplyOrderAddReqVO.setApplyOrderSubType(ApplyOrderSubTypeEnum.QUOTA_BIZ_GRANT.getTypeReal());

        List<EmployeeBankInfoReq> employeeBankInfoReqs= new ArrayList<>();
        EmployeeBankInfoReq employeeBankInfoReq = new EmployeeBankInfoReq();
        employeeBankInfoReq.setFxCardId(createReqDTO.getFxCardId());
        employeeBankInfoReq.setBankName(createReqDTO.getBankName());
        employeeBankInfoReq.setEmployeeId(createReqDTO.getEmployeeId());
        employeeBankInfoReq.setBankAccountNo(createReqDTO.getBankAccountNo());
        employeeBankInfoReqs.add(employeeBankInfoReq);
        creditApplyOrderAddReqVO.setUserMap(employeeBankInfoReqs);

        creditApplyOrderAddReqVO.setEmployeeDept(true);

        return creditApplyOrderAddReqVO;
    }

    /**
     * 转换详情响应结果
     */
    private CardCreditApplyOrderDetailRpcRespDTO convertToDetailRpcRespDTO(CardCreditApplyOrderShowResDTO orderDetail) {
        CardCreditApplyOrderDetailRpcRespDTO respDTO = CopyUtils.convert(orderDetail, CardCreditApplyOrderDetailRpcRespDTO.class);
        // 可以在这里添加额外的转换逻辑
        return respDTO;
    }

    /**
     * 转换分页请求参数
     */
    private CardCreditApplyOrderPageReqDTO convertToPageReqDTO(CardCreditApplyOrderPageRpcReqDTO pageReqDTO) {
        CardCreditApplyOrderPageReqDTO serviceReqDTO = CopyUtils.convert(pageReqDTO, CardCreditApplyOrderPageReqDTO.class);
        // 可以在这里添加额外的转换逻辑
        return serviceReqDTO;
    }

    /**
     * 转换分页响应结果
     */
    private CardCreditApplyOrderPageRpcRespDTO convertToPageRpcRespDTO(CardCreditApplyOrderPageResDTO record) {
        CardCreditApplyOrderPageRpcRespDTO respDTO = CopyUtils.convert(record, CardCreditApplyOrderPageRpcRespDTO.class);
        // 可以在这里添加额外的转换逻辑
        return respDTO;
    }
}
