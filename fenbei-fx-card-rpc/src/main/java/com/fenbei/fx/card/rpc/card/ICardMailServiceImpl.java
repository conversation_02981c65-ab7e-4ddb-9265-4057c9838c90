package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.card.ICardMailService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.api.enums.FbtReceiveStatusEnum;
import com.fenbei.fx.card.api.enums.ForwardStatusEnum;
import com.fenbei.fx.card.api.enums.MailSignStatusEnum;
import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.dao.cardmail.CardMailDAO;
import com.fenbei.fx.card.dao.cardmail.po.CardMailPO;
import com.fenbei.fx.card.service.cardmail.CardMailService;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageReqDTO;
import com.fenbei.fx.card.service.cardmail.dto.CardMailPageResDTO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.common.utils.StringUtils;
import com.finhub.framework.core.json.JsonUtils;
import com.finhub.framework.core.page.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-13 下午8:19
 */
@Slf4j
@Validated
@DubboService
public class ICardMailServiceImpl implements ICardMailService {

    @Autowired
    private CardMailService cardMailService;

    @Resource
    private CardMailDAO cardMailDAO;

    @Override
    public PageDTO<CardMailRpcRespDTO> queryCardMailStereoPage(CardMailRpcReqDTO cardMailRpcReqDTO) {
        PageDTO<CardMailRpcRespDTO> cardMailRpcRespDTOPageDTO = new PageDTO<>();
        CardMailPageReqDTO cardMailPageReqDTO = buildCardMailPageReqDTO(cardMailRpcReqDTO);
        Page<CardMailPageResDTO> pagination = cardMailService.pagination(cardMailPageReqDTO, cardMailRpcReqDTO.getPageNo(), cardMailRpcReqDTO.getPageSize());
        if(Objects.nonNull(pagination)){
            cardMailRpcRespDTOPageDTO.setTotalSize((int) pagination.getTotal());
            cardMailRpcRespDTOPageDTO.setCurrentPage((int)pagination.getCurrent());
            cardMailRpcRespDTOPageDTO.setList(buildMailRpcRespDTOS(pagination.getRecords()));
        }
        return cardMailRpcRespDTOPageDTO;
    }

    private List<CardMailRpcRespDTO> buildMailRpcRespDTOS(List<CardMailPageResDTO> records) {
        List<CardMailRpcRespDTO> cardMailRpcRespDTOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(records)){
            for (CardMailPageResDTO record : records) {
                CardMailRpcRespDTO cardMailRpcRespDTO = new CardMailRpcRespDTO();
                cardMailRpcRespDTO.setId(record.getId());
                if(Objects.nonNull(record.getCardIssuanceTime())) {
                    cardMailRpcRespDTO.setCardOpenDate(DateUtils.formatTime(record.getCardIssuanceTime()));
                }
                cardMailRpcRespDTO.setEmployeeName(record.getEmployeeName());
                cardMailRpcRespDTO.setEmployeePhone(record.getEmployeePhone());
                cardMailRpcRespDTO.setCardPlatformDesc(CardPlatformEnum.getPlatform(record.getCardPlatform()).getName());
                cardMailRpcRespDTO.setCardNo(record.getBankCardNo());
                cardMailRpcRespDTO.setCardMailNo(record.getCardMailNo());
                cardMailRpcRespDTO.setForwardFlagDesc(0==record.getForwardFlag()?"否":"是");
                FbtReceiveStatusEnum anEnum = FbtReceiveStatusEnum.getEnum(record.getFbtReceiveStatus());
                cardMailRpcRespDTO.setFbtReceiveStatusDesc(anEnum!=null?anEnum.getName():null);
                ForwardStatusEnum forwardStatusEnum = ForwardStatusEnum.getEnum(record.getForwardStatus());
                cardMailRpcRespDTO.setForwardStatusDesc(forwardStatusEnum!=null?forwardStatusEnum.getName():null);
                cardMailRpcRespDTO.setForwardNo(record.getForwardNo());
                cardMailRpcRespDTO.setForwardSupplier(record.getForwardSupplier());
                MailSignStatusEnum mailSignStatusEnum = MailSignStatusEnum.getEnum(record.getSignStatus());
                cardMailRpcRespDTO.setSignStatusDesc(mailSignStatusEnum!=null?mailSignStatusEnum.getName():null);
                cardMailRpcRespDTO.setRemark(record.getRemark());
                if (!StringUtils.isBlank(record.getOriginalAddress())) {
                    AddressRpcDto userAddress = JsonUtils.toObj(record.getOriginalAddress(),AddressRpcDto.class);
                    cardMailRpcRespDTO.setUserPostalAddressDto(userAddress);
                }
                cardMailRpcRespDTO.setChangedAddress(record.getChangedAddress());
                cardMailRpcRespDTOS.add(cardMailRpcRespDTO);
            }
        }
        return cardMailRpcRespDTOS;
    }

    private CardMailPageReqDTO buildCardMailPageReqDTO(CardMailRpcReqDTO cardMailRpcReqDTO) {
        CardMailPageReqDTO cardMailPageReqDTO = new CardMailPageReqDTO();
        cardMailPageReqDTO.setEmployeeName(cardMailRpcReqDTO.getEmployeeName());
        cardMailPageReqDTO.setEmployeePhone(cardMailRpcReqDTO.getEmployeePhone());
        cardMailPageReqDTO.setBankCardNo(cardMailRpcReqDTO.getCardNo());
        cardMailPageReqDTO.setCardMailNo(cardMailRpcReqDTO.getCardMailNo());
        cardMailPageReqDTO.setForwardFlag(cardMailRpcReqDTO.getForwardFlag());
        cardMailPageReqDTO.setFbtReceiveStatus(cardMailRpcReqDTO.getFbtReceiveStatus());
        cardMailPageReqDTO.setForwardStatus(cardMailRpcReqDTO.getForwardStatus());
        cardMailPageReqDTO.setForwardNo(cardMailRpcReqDTO.getForwardNo());
        if(!StringUtils.isBlank(cardMailRpcReqDTO.getCreateTimeStart())&& !StringUtils.isBlank(cardMailRpcReqDTO.getCreateTimeEnd())){
            cardMailPageReqDTO.setCreateTimeStart(DateUtils.parseTime(cardMailRpcReqDTO.getCreateTimeStart()));
            cardMailPageReqDTO.setCreateTimeEnd(DateUtils.parseTime(cardMailRpcReqDTO.getCreateTimeEnd()));
        }
        cardMailPageReqDTO.setSignStatus(cardMailRpcReqDTO.getSignStatus());
        cardMailPageReqDTO.setCardPlatform(cardMailRpcReqDTO.getCardPlatform());
        return cardMailPageReqDTO;
    }

    @Override
    public Integer modifyCardMailStereo(CardMailModifyReqDTO cardMailModifyReqDTO) {
        if(StringUtils.isBlank(cardMailModifyReqDTO.getId())){
            throw new FinhubException(-1,"id为空");
        }
        Boolean needUpdate = false;
        CardMailPO cardMailPO = new CardMailPO();
        cardMailPO.setId(cardMailModifyReqDTO.getId());
        if(Objects.nonNull(cardMailModifyReqDTO.getChangedAddress())){
            cardMailPO.setChangedAddress(cardMailModifyReqDTO.getChangedAddress());
            needUpdate=true;
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getForwardNo())){
            needUpdate=true;
            cardMailPO.setForwardNo(cardMailModifyReqDTO.getForwardNo());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getForwardSupplier())){
            needUpdate=true;
            cardMailPO.setForwardSupplier(cardMailModifyReqDTO.getForwardSupplier());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getRemark())){
            needUpdate=true;
            cardMailPO.setRemark(cardMailModifyReqDTO.getRemark());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getChangedAddress())){
            needUpdate=true;
            cardMailPO.setChangedAddress(cardMailModifyReqDTO.getChangedAddress());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getFbtReceiveStatus())){
            needUpdate=true;
            cardMailPO.setFbtReceiveStatus(cardMailModifyReqDTO.getFbtReceiveStatus());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getForwardStatus())){
            needUpdate=true;
            cardMailPO.setForwardStatus(cardMailModifyReqDTO.getForwardStatus());
        }
        if(Objects.nonNull(cardMailModifyReqDTO.getSignStatus())){
            needUpdate=true;
            cardMailPO.setSignStatus(cardMailModifyReqDTO.getSignStatus());
        }
        if(needUpdate){
           return cardMailDAO.updateById(cardMailPO);
        }
        return 0;
    }
}
