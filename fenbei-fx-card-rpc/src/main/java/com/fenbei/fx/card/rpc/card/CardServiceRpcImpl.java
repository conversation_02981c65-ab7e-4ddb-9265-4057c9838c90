package com.fenbei.fx.card.rpc.card;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.api.card.ICardService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.*;
import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.CardDTO;
import com.fenbei.fx.card.service.card.dto.CardShowResDTO;
import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.CardApplyPageResDTO;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbei.fx.card.util.MaskUtils;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.fxpay.api.enums.FxAcctChannelEnum;
import com.fenbeitong.usercenter.api.model.dto.auth.VirtualCardAuthInfoDTO;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyVirtualCardIssuerDto;
import com.fenbeitong.usercenter.api.model.dto.company.account.CompanyForeignCurrencyAccountDTO;
import com.fenbeitong.usercenter.api.service.privilege.IRPrivilegeService;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.page.Page;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * CardServiceRpcImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@Slf4j
@Validated
@DubboService
public class CardServiceRpcImpl implements ICardService {

    @DubboReference
    private IRPrivilegeService irPrivilegeService;

    @Override
    public List<CardBalanceRpcRespDTO> getBalance(CardBalanceRpcReqDTO rpcReqDTO) {
        log.info("CardServiceRpcImpl getBalance params={}", JsonUtils.toJson(rpcReqDTO));
        if (StringUtils.isAnyBlank(rpcReqDTO.getCompanyId(), rpcReqDTO.getPlatform())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        try {
            List<Map<String, Object>> list = CardManager.me().cardAllBalance(rpcReqDTO.getCompanyId(), rpcReqDTO.getPlatform());
            List<CardBalanceRpcRespDTO> rpcRespDTOS = JSONObject.parseArray(JSON.toJSONString(list), CardBalanceRpcRespDTO.class);
            log.info("CardServiceRpcImpl getBalance res={}", JsonUtils.toJson(rpcRespDTOS));

            return rpcRespDTOS;
        } catch (FinhubException e){
            log.warn("CardServiceRpcImpl getBalance warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("CardServiceRpcImpl getBalance error params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public List<UserFxcardRpcRespDTO> findUserFxcardInfos(UserFxcardRpcReqDTO rpcReqDTO) {
        log.info("CardServiceRpcImpl findUserFxcardInfos params={}", JsonUtils.toJson(rpcReqDTO));
        if (StringUtils.isAnyBlank(rpcReqDTO.getCompanyId(), rpcReqDTO.getEmployeeId())){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        try {
            List<UserFxcardRpcRespDTO> userCardInfosDTO = new ArrayList<>();
            //查询该公司各种银行卡开通权限
            VirtualCardAuthInfoDTO infoDTO = irPrivilegeService.queryVirtualCardAuthInfo(rpcReqDTO.getCompanyId(), rpcReqDTO.getEmployeeId());
            log.info("userCardInfos virtualCardAuthInfoDTO={}", JsonUtils.toJson(infoDTO));
            if (Objects.isNull(infoDTO)){
                return userCardInfosDTO;
            }

            //企业开通海外卡权限：除了海外卡权限，如果国内卡列表中有连连也可以。后续改造Stereo-账户权限配置-企业外币账户权限配置
            List<CompanyVirtualCardIssuerDto> companyVirtualCardIssuerDtoList = infoDTO.getCompanyVirtualCardIssuerDtoList();
            boolean comHasFxCardAuth=false;
            //从国内的里面拿去连连
            if(ObjectUtils.isNotEmpty(companyVirtualCardIssuerDtoList)){
                for(CompanyVirtualCardIssuerDto cardIssuerDto:companyVirtualCardIssuerDtoList){
                    FxAcctChannelEnum channelEnum = FxAcctChannelEnum.matchChannel(cardIssuerDto.getCardIssuerCode());
                    if (!Objects.isNull(channelEnum)){
                        comHasFxCardAuth= true;
                        break;
                    }
                }
            }
            Boolean companyOverseas = infoDTO.getCompanyOverseas()||comHasFxCardAuth;
            Boolean employeeOverseas = infoDTO.getEmployeeOverseas();

            //如果公司或者个人没权限，直接返回
            if (!companyOverseas || !employeeOverseas){
                log.info("userCardInfos no privilege companyOverseas={},employeeOverseas={}", companyOverseas, employeeOverseas);
                return userCardInfosDTO;
            }

            //海外账号渠道的权限
            List<CompanyForeignCurrencyAccountDTO> companyAccountList = infoDTO.getForeignCurrencyAccountDTOList();
            if (CollectionUtils.isEmpty(companyAccountList)&&CollectionUtils.isEmpty(companyVirtualCardIssuerDtoList)){
                log.info("userCardInfos no privilege companyAccountList={}", JsonUtils.toJson(companyAccountList));
                return userCardInfosDTO;
            }

            //获取生效中的卡
            List<CardDTO> cardDTOS = CardManager.me().userActiveCards(rpcReqDTO.getEmployeeId(), rpcReqDTO.getCompanyId());
            log.info("CardServiceRpcImpl findUserFxcardInfos cardDTOS={}", JsonUtils.toJson(cardDTOS));

            convertCardInfo(cardDTOS, userCardInfosDTO);

            return userCardInfosDTO;
        } catch (FinhubException e){
            log.warn("CardServiceRpcImpl findUserFxcardInfos warn params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("CardServiceRpcImpl findUserFxcardInfos error params={}", JsonUtils.toJson(rpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }

    }

    @Override
    public Integer getCardBalanceGreaterZeroByCompanyId(String companyId) {
        log.info("CardServiceRpcImpl getCardBalanceGreaterZeroByCompanyId companyId={}", companyId);
        if (StringUtils.isAnyBlank(companyId)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        try {
            Integer count = CardManager.me().getCardBalanceGreaterZeroByCompanyId(companyId);
            log.info("CardServiceRpcImpl getCardBalanceGreaterZeroByCompanyId count={}", JsonUtils.toJson(count));

            return count;
        } catch (FinhubException e){
            log.warn("CardServiceRpcImpl getCardBalanceGreaterZeroByCompanyId warn companyId={}", companyId, e);
            throw e;
        } catch (Exception e){
            log.error("CardServiceRpcImpl getCardBalanceGreaterZeroByCompanyId error companyId={}", companyId, e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }
    }

    @Override
    public PageDTO<CardPageRpcResVO> getPendingCardsByPage(CardPageRpcReqVO cardPageReqVO) {
        log.info("getPendingCardsByPage req = {}", JSON.toJSONString(cardPageReqVO));

        CardApplyPageReqDTO cardPendingPageReqVO = CopyUtils.convert(cardPageReqVO,CardApplyPageReqDTO.class);
        Page<CardApplyPageResDTO> pageResDTOPage =  CardApplyManager.me().getAllStereoPendingCards(cardPendingPageReqVO,cardPageReqVO.getPageNo(),cardPageReqVO.getPageSize());

        List<CardPageRpcResVO> resultList = convertCardPageInfo(pageResDTOPage);

        PageDTO<CardPageRpcResVO> pageDTO = new PageDTO<>();
        pageDTO.setCurrentPage(cardPageReqVO.getPageNo());
        pageDTO.setList(resultList);
        pageDTO.setPageSize(Integer.parseInt(pageResDTOPage.getSize()+""));
        pageDTO.setTotalSize(Integer.parseInt(pageResDTOPage.getTotal()+""));
        log.info("getPendingCardsByPage req = {} res = {}", JSON.toJSONString(cardPageReqVO) ,JSON.toJSONString(pageDTO));

        return pageDTO;
    }

    private List<CardPageRpcResVO> convertCardPageInfo(Page<CardApplyPageResDTO> pageResDTOPage ){
        List<CardPageRpcResVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResDTOPage.getRecords())){
            for (CardApplyPageResDTO resDTO : pageResDTOPage.getRecords()){
                CardPageRpcResVO rpcResVO = CopyUtils.convert(resDTO,CardPageRpcResVO.class);
                if (ObjectUtils.isNotEmpty(resDTO.getPostalAddressDto())){
                    AddressRpcDto addressRpcDto = JSONObject.parseObject(JSON.toJSONString(resDTO.getPostalAddressDto()),AddressRpcDto.class);
                    rpcResVO.setPostalAddressDto(addressRpcDto);
                }
                if (ObjectUtils.isNotEmpty(resDTO.getUserPostalAddressDto())) {
                    AddressRpcDto userAddress = JSONObject.parseObject(JSON.toJSONString(resDTO.getUserPostalAddressDto()),AddressRpcDto.class);
                    rpcResVO.setUserPostalAddressDto(userAddress);
                }
                CardPlatformEnum cardPlatformEnum =  CardPlatformEnum.getPlatform(resDTO.getCardPlatform());
                if (cardPlatformEnum != null){
                    rpcResVO.setCardPlatform(cardPlatformEnum.getName());
                }else {
                    rpcResVO.setCardPlatform(resDTO.getCardPlatform());
                }
                resultList.add(rpcResVO);

            }
        }
        return resultList;
    }


    /**
     * lianlian无实体卡，so无需FBT运营审核，so无Stereo审批开卡，此接口对连连无效
     * @param getApplyId
     * @return
     */
    @Override
    public boolean approvedCardApply(String getApplyId) {
        return CardApplyManager.me().submitCard(getApplyId);
    }

    @Override
    public PageDTO<CardPageRpcResVO> getAllCardApplyByPage(CardPageRpcReqVO cardPageReqVO) {
        log.info("getAllCardApplyByPage req = {}", JSON.toJSONString(cardPageReqVO));

        CardApplyPageReqDTO cardPendingPageReqVO = CopyUtils.convert(cardPageReqVO,CardApplyPageReqDTO.class);
        cardPendingPageReqVO.setNameOnCard(cardPageReqVO.getName());
        Page<CardApplyPageResDTO> pageResDTOPage =  CardApplyManager.me().getAllCardInfo4Stereo(cardPendingPageReqVO,cardPageReqVO.getPageNo(),cardPageReqVO.getPageSize());

        List<CardPageRpcResVO> resultList = convertCardPageInfo(pageResDTOPage);

        PageDTO<CardPageRpcResVO> pageDTO = new PageDTO<>();
        pageDTO.setCurrentPage(cardPageReqVO.getPageNo());
        pageDTO.setTotalSize(Integer.parseInt(pageResDTOPage.getTotal()+""));
        pageDTO.setList(resultList);
        pageDTO.setPageSize(Integer.parseInt(pageResDTOPage.getSize()+""));
        log.info("getAllCardApplyByPage req = {} res = {}", JSON.toJSONString(cardPageReqVO) ,JSON.toJSONString(pageDTO));

        return pageDTO;
    }

    @Override
    public List<Map<Integer,String>> queryStatusEnum() {
        return CardShowStatusEnum.getKeyValueEnum();
    }

    private void convertCardInfo(List<CardDTO> cardDTOS, List<UserFxcardRpcRespDTO> userCardInfosDTO) {
        if (CollectionUtils.isNotEmpty(cardDTOS)){
            cardDTOS.forEach(p->{
                UserFxcardRpcRespDTO cardInfoDTO = new UserFxcardRpcRespDTO();
                BeanUtils.copyProperties(p, cardInfoDTO);
                //状态处理
                Integer showStatus = CardStatusEnum.getShowStatus(p.getCardStatus());
                cardInfoDTO.setCardShowStatus(showStatus);
                cardInfoDTO.setMaskBankCardNo(MaskUtils.leftData(p.getBankCardNo(),4));
                //渠道信息
//                CardPlatformEnum platform = CardPlatformEnum.getPlatform(p.getCardPlatform());
                FxAcctChannelEnum fxAcctChannelEnum = FxAcctChannelEnum.getChannelEnum(p.getCardPlatform());
                if (!Objects.isNull(fxAcctChannelEnum)){
                    cardInfoDTO.setCardPlatformName(fxAcctChannelEnum.getChannelName());
                    cardInfoDTO.setCardPlatformIcon(fxAcctChannelEnum.getBankIconWebSmall());
                }
                cardInfoDTO.setCardBrand(p.getCardBrand());
                CardBrandEnum brand = CardBrandEnum.getBrand(p.getCardBrand());
                if (Objects.isNull(brand)){
                    cardInfoDTO.setCardBrandIcon(CardBrandEnum.NULL_BRAND.getBrandIcon());
                }else{
                    cardInfoDTO.setCardBrandIcon(brand.getBrandIcon());
                }
                //币种
                CurrencyEnum currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(p.getCurrency());
                if (!Objects.isNull(currencyEnum)){
                    cardInfoDTO.setCurrencySymbol(currencyEnum.getSymbol());
                    cardInfoDTO.setCurrencyName(currencyEnum.getDisplayName());
                }
                userCardInfosDTO.add(cardInfoDTO);
            });
        }
    }


    @Override
    public UserFxCardRpcInfoDTO findUserCardInfoByFxCardId(String fxCardId) {
        CardShowResDTO cardShowResDTO = CardManager.me().cardDetail(fxCardId);
        return buildCardInfoDTO(cardShowResDTO);
    }

    private UserFxCardRpcInfoDTO buildCardInfoDTO(CardShowResDTO cardShowResDTO) {
        if(Objects.isNull(cardShowResDTO)){
            return null;
        }
        UserFxCardRpcInfoDTO userFxCardRpcInfoDTO = new UserFxCardRpcInfoDTO();
        userFxCardRpcInfoDTO.setFxCardId(cardShowResDTO.getFxCardId());
        userFxCardRpcInfoDTO.setBankCardId(cardShowResDTO.getBankCardId());
        userFxCardRpcInfoDTO.setBankCardNo(cardShowResDTO.getBankCardNo());
        userFxCardRpcInfoDTO.setCompanyAccountId(cardShowResDTO.getCompanyAccountId());
        userFxCardRpcInfoDTO.setCardIssueTo(cardShowResDTO.getCardIssueTo());
        userFxCardRpcInfoDTO.setCardFormFactor(cardShowResDTO.getCardFormFactor());
        userFxCardRpcInfoDTO.setCardCvv(cardShowResDTO.getCardCvv());
        userFxCardRpcInfoDTO.setCardExpiryYear(cardShowResDTO.getCardExpiryYear());
        userFxCardRpcInfoDTO.setCardExpiryMonth(cardShowResDTO.getCardExpiryMonth());
        userFxCardRpcInfoDTO.setNameOnCard(cardShowResDTO.getNameOnCard());
        userFxCardRpcInfoDTO.setCardPlatform(cardShowResDTO.getCardPlatform());
        CardPlatformEnum platform = CardPlatformEnum.getPlatform(cardShowResDTO.getCardPlatform());
        if (!Objects.isNull(platform)){
            userFxCardRpcInfoDTO.setCardPlatformName(platform.name());
            userFxCardRpcInfoDTO.setCardPlatformIcon(platform.getPlatformIcon());
        }
        userFxCardRpcInfoDTO.setCardBrand(cardShowResDTO.getCardBrand());

        CardBrandEnum brand = CardBrandEnum.getBrand(cardShowResDTO.getCardBrand());
        if (Objects.isNull(brand)){
            userFxCardRpcInfoDTO.setCardBrandIcon(CardBrandEnum.NULL_BRAND.getBrandIcon());
        }else{
            userFxCardRpcInfoDTO.setCardBrandIcon(brand.getBrandIcon());
        }
        userFxCardRpcInfoDTO.setCardPublicTime(cardShowResDTO.getCardPublicTime());
        userFxCardRpcInfoDTO.setCardStatus(cardShowResDTO.getCardStatus());
        userFxCardRpcInfoDTO.setActiveStatus(cardShowResDTO.getActiveStatus());
        userFxCardRpcInfoDTO.setFxCardholderId(cardShowResDTO.getFxCardholderId());
        userFxCardRpcInfoDTO.setCardPurpose(cardShowResDTO.getCardPurpose());
        userFxCardRpcInfoDTO.setCurrency(cardShowResDTO.getCurrency());
        CurrencyEnum currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(cardShowResDTO.getCurrency());
        if (!Objects.isNull(currencyEnum)){
            userFxCardRpcInfoDTO.setCurrencySymbol(currencyEnum.getSymbol());
            userFxCardRpcInfoDTO.setCurrencyName(currencyEnum.getDisplayName());
        }
        userFxCardRpcInfoDTO.setBalance(cardShowResDTO.getBalance());
        userFxCardRpcInfoDTO.setCardLimits(cardShowResDTO.getCardLimits());
        userFxCardRpcInfoDTO.setCreateUserId(cardShowResDTO.getCreateUserId());
        userFxCardRpcInfoDTO.setIsContinueCharging(CardStatusEnum.validContinueCharging(cardShowResDTO.getCardStatus()));
        return userFxCardRpcInfoDTO;
    }

    @Override
    public CompanyTotalAmountRpcResDTO getCompanyDistributeTotalAmount(CompanyTotalAmountRpcReqDTO companyTotalAmountRpcReqDTO) {
        CompanyTotalAmountRpcResDTO companyTotalAmountRpcResDTO = new CompanyTotalAmountRpcResDTO();

        if (Func.isNull(companyTotalAmountRpcReqDTO)) {
            return companyTotalAmountRpcResDTO;
        }

        String companyId = companyTotalAmountRpcReqDTO.getCompanyId();
        if (Func.isBlank(companyId)) {
            return companyTotalAmountRpcResDTO;
        }

        BankCardFlowDTO bankCardFlowDTO = new BankCardFlowDTO();
        bankCardFlowDTO.setCompanyId(companyId);

        companyTotalAmountRpcResDTO.setAllBalance(getApplyAllBalance(bankCardFlowDTO));
        companyTotalAmountRpcResDTO.setResult(true);

        return companyTotalAmountRpcResDTO;
    }

    @Override
    public EmployeeTotalAmountRpcResDTO getEmployeeDistributeTotalAmount(EmployeeTotalAmountRpcReqDTO employeeTotalAmountRpcReqDTO) {
        EmployeeTotalAmountRpcResDTO employeeTotalAmountRpcResDTO = new EmployeeTotalAmountRpcResDTO();

        if (Func.isNull(employeeTotalAmountRpcReqDTO)) {
            return employeeTotalAmountRpcResDTO;
        }

        String employeeId = employeeTotalAmountRpcReqDTO.getEmployeeId();
        if (Func.isBlank(employeeId)) {
            return employeeTotalAmountRpcResDTO;
        }

        BankCardFlowDTO bankCardFlowDTO = new BankCardFlowDTO();
        bankCardFlowDTO.setEmployeeId(employeeId);

        employeeTotalAmountRpcResDTO.setAllBalance(getApplyAllBalance(bankCardFlowDTO));
        employeeTotalAmountRpcResDTO.setResult(true);

        return employeeTotalAmountRpcResDTO;
    }

    private BigDecimal getApplyAllBalance(BankCardFlowDTO bankCardFlowDTO) {
        BigDecimal allBalance = new BigDecimal("0");

        List<BankCardFlowDTO> bankCardFlowDTOList = BankCardFlowService.me().find(bankCardFlowDTO);
        if (Func.isNotEmpty(bankCardFlowDTOList)) {
            for (BankCardFlowDTO dto : bankCardFlowDTOList) {
                if (CardOperationTypeEnum.REFUND.getCode().equals(dto.getOperationType())) {
                    allBalance = allBalance.subtract(dto.getOperationAmount());
                }

                if (CardOperationTypeEnum.APPLY.getCode().equals(dto.getOperationType())) {
                    allBalance = allBalance.add(dto.getOperationAmount());
                }
            }
        }

        return allBalance;
    }

    @Override
    public ConsumeTotalAmountRpcResDTO getEmployeeConsumeTotalAmount(ConsumeTotalAmountRpcReqDTO consumeTotalAmountRpcReqDTO) {
        ConsumeTotalAmountRpcResDTO consumeTotalAmountRpcResDTO = new ConsumeTotalAmountRpcResDTO();

        if (Func.isNull(consumeTotalAmountRpcReqDTO)) {
            return consumeTotalAmountRpcResDTO;
        }

        String employeeId = consumeTotalAmountRpcReqDTO.getEmployeeId();
        if (Func.isBlank(employeeId)) {
            return consumeTotalAmountRpcResDTO;
        }

        CardOrderDTO cardOrderDTO = new CardOrderDTO();
        cardOrderDTO.setEmployeeId(employeeId);

        consumeTotalAmountRpcResDTO.setAllBalance(getConsumeAllBalance(cardOrderDTO));
        consumeTotalAmountRpcResDTO.setResult(true);

        return consumeTotalAmountRpcResDTO;
    }

    private BigDecimal getConsumeAllBalance(CardOrderDTO cardOrderDTO) {
        BigDecimal allBalance = new BigDecimal("0");

        List<CardOrderDTO> cardOrderDTOList = CardOrderService.me().find(cardOrderDTO);
        if (Func.isNotEmpty(cardOrderDTOList)) {
            for (CardOrderDTO dto : cardOrderDTOList) {
                if (TransactionTypeEnum.REFUND.getKey().equals(dto.getType())) {
                    allBalance = allBalance.subtract(dto.getTradeAmount());
                }

                if (TransactionTypeEnum.CONSUME.getKey().equals(dto.getType())) {
                    allBalance = allBalance.add(dto.getTradeAmount());
                }
            }
        }

        return allBalance;
    }

    @Override
    public EmployeeFreezeCardRpcResDTO freezeEmployeeCardNotConsume(EmployeeFreezeCardRpcReqDTO employeeFreezeCardRpcReqDTO) {
        EmployeeFreezeCardRpcResDTO employeeFreezeCardRpcResDTO = new EmployeeFreezeCardRpcResDTO();

        if (employeeFreezeCardRpcReqDTO == null) {
            return employeeFreezeCardRpcResDTO;
        }

        String employeeId = employeeFreezeCardRpcReqDTO.getEmployeeId();
        if (StrUtil.isBlank(employeeId)) {
            return employeeFreezeCardRpcResDTO;
        }

        CardDTO cardDTO = CardManager.me().getByEmployeeId(employeeId);
        if (cardDTO == null) {
            return employeeFreezeCardRpcResDTO;
        }

        // 更新状态为冻结
        cardDTO.setCardStatus(CardStatusEnum.FREEZE.getStatus());
        employeeFreezeCardRpcResDTO.setResult(CardService.me().modify(cardDTO));

        return employeeFreezeCardRpcResDTO;
    }

    @Override
    public CardRpcRespVO queryCardInfo(CardRpcReqVO cardRpcReqVO) {
        CardDTO cardDTO =  CardManager.me().cardDetailByFxCardId(cardRpcReqVO.getFxCardId());
        CardRpcRespVO cardRpcRespVO = new CardRpcRespVO();
        if (cardDTO != null){
            BeanUtils.copyProperties(cardDTO,cardRpcRespVO);
        }
        return cardRpcRespVO;
    }
}
