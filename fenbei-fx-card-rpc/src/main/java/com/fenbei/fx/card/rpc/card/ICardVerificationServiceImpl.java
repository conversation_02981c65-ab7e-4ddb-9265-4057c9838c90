package com.fenbei.fx.card.rpc.card;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.api.card.ICardVerificationService;
import com.fenbei.fx.card.api.card.dto.FxCostBindReqRpcDTO;
import com.fenbei.fx.card.api.card.dto.FxPettyRespRpcDTO;
import com.fenbei.fx.card.api.card.dto.FxVerificationReqRpcDTO;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.cardorder.dto.FxVerificationReqDTO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * Created by FBT on 2023/6/5.
 */
@Slf4j
@Validated
@DubboService
public class ICardVerificationServiceImpl implements ICardVerificationService {

    @Autowired
    private CardOrderService cardOrderService;

    @Override
    public boolean costBind(FxCostBindReqRpcDTO reqRpcDTO) {
        log.info("====创建费用开始costBind:{}", JSON.toJSONString(reqRpcDTO));
        if (CollectionUtils.isEmpty(reqRpcDTO.getBizNos()) || reqRpcDTO.getCostId() == null){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        cardOrderService.costBind(reqRpcDTO.getBizNos() , reqRpcDTO.getCostId());
        return true;
    }

    @Override
    public boolean costUnBind(FxCostBindReqRpcDTO reqRpcDTO) {
        log.info("====解除费用开始costBind:{}", JSON.toJSONString(reqRpcDTO));
        if (CollectionUtils.isEmpty(reqRpcDTO.getBizNos()) ||  reqRpcDTO.getCostId() == null ){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        cardOrderService.costUnBind(reqRpcDTO.getBizNos()  , reqRpcDTO.getCostId() );
        return true;
    }

    @Override
    public boolean applyInit(FxVerificationReqRpcDTO initDTO) {
        log.info("====核销单提交处理核销金额start :{}", JSON.toJSONString(initDTO));
        if (CollectionUtils.isEmpty(initDTO.getItems()) ||  StringUtils.isBlank(initDTO.getDuringApplyId()) ){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        FxVerificationReqDTO fxVerificationReqDTO = JSONObject.parseObject(JSONObject.toJSONString(initDTO), FxVerificationReqDTO.class);
        fxVerificationReqDTO.setVerificationId(initDTO.getDuringApplyId());
        //FxVerificationReqDTO fxVerificationReqDTO = CopyUtils.convert(initDTO,FxVerificationReqDTO.class);
        return cardOrderService.applyInit(fxVerificationReqDTO);
    }

    @Override
    public boolean applyDel(FxVerificationReqRpcDTO delDTO) {
        log.info("====核销单审批拒绝处理核销金额start :{}", JSON.toJSONString(delDTO));
        if (CollectionUtils.isEmpty(delDTO.getItems()) ||  StringUtils.isBlank(delDTO.getDuringApplyId())){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        FxVerificationReqDTO fxVerificationReqDTO = JSONObject.parseObject(JSONObject.toJSONString(delDTO), FxVerificationReqDTO.class);
        fxVerificationReqDTO.setVerificationId(delDTO.getDuringApplyId());
        //FxVerificationReqDTO fxVerificationReqDTO = CopyUtils.convert(delDTO,FxVerificationReqDTO.class);
        return cardOrderService.applyDel(fxVerificationReqDTO);
    }

    @Override
    public boolean applyDisCardDel(FxVerificationReqRpcDTO delDTO) {
        log.info("====核销单撤回处理核销金额start :{}", JSON.toJSONString(delDTO));
        if (CollectionUtils.isEmpty(delDTO.getItems())  || StringUtils.isBlank(delDTO.getDuringApplyId())){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        FxVerificationReqDTO fxVerificationReqDTO = JSONObject.parseObject(JSONObject.toJSONString(delDTO), FxVerificationReqDTO.class);
        fxVerificationReqDTO.setVerificationId(delDTO.getDuringApplyId());
        //FxVerificationReqDTO fxVerificationReqDTO = CopyUtils.convert(delDTO,FxVerificationReqDTO.class);
        return cardOrderService.applyDisCardDel(fxVerificationReqDTO);
    }

    @Override
    public boolean applyDoneNew(FxVerificationReqRpcDTO doneDTO) {
        log.info("====核销单审核通过处理核销金额start :{}", JSON.toJSONString(doneDTO));
        if (CollectionUtils.isEmpty(doneDTO.getItems())  || StringUtils.isBlank(doneDTO.getDuringApplyId())){
            throw new FinhubException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT.getCode(), "参数错误");
        }
        FxVerificationReqDTO fxVerificationReqDTO = JSONObject.parseObject(JSONObject.toJSONString(doneDTO), FxVerificationReqDTO.class);
        fxVerificationReqDTO.setVerificationId(doneDTO.getDuringApplyId());
        //FxVerificationReqDTO fxVerificationReqDTO = CopyUtils.convert(doneDTO,FxVerificationReqDTO.class);
        return cardOrderService.applyDoneNew(fxVerificationReqDTO);
    }

    @Override
    public FxPettyRespRpcDTO queryRoundPettyByOrderIdList(List<String> orderIdList) {
        FxPettyRespRpcDTO fxPettyRespRpcDTO = FxPettyRespRpcDTO.builder().isRoundPetty(false).build();
        return fxPettyRespRpcDTO;
    }
}
