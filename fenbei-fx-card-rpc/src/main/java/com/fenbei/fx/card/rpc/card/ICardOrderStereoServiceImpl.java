package com.fenbei.fx.card.rpc.card;

import com.fenbei.fx.card.api.base.PriceRpcVo;
import com.fenbei.fx.card.api.card.ICardOrderStereoService;
import com.fenbei.fx.card.api.card.dto.*;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoStereoPageReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoStereoPageResDTO;
import com.fenbei.fx.card.service.cardorder.manager.CardOrderManager;
import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.finhub.framework.core.page.Page;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description stereo交易记录
 * @date 2024-04-22
 */
@Slf4j
@Validated
@DubboService
public class ICardOrderStereoServiceImpl implements ICardOrderStereoService {

    @DubboReference
    private IRCompanyService irCompanyService;


    @Override
    public PageDTO<CardTradeInfoStereoPageRpcResVO> pagination(CardTradeInfoStereoPageRpcReqDTO pageRpcReqDTO) {

        log.info("获取stereo的交易记录，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO));
        try {
            checkPageParam(pageRpcReqDTO);

            CardTradeInfoStereoPageReqDTO pageReqDTO = CopyUtils.convert(pageRpcReqDTO, CardTradeInfoStereoPageReqDTO.class);

            Page<CardTradeInfoStereoPageResDTO> pagination = CardOrderManager.me().pagination(pageReqDTO, pageRpcReqDTO.getPageNo(), pageRpcReqDTO.getPageSize());

            PageDTO<CardTradeInfoStereoPageRpcResVO> pageDTO = new PageDTO<>();
            if(Objects.nonNull(pagination)){
                pageDTO.setTotalSize((int) pagination.getTotal());
                pageDTO.setCurrentPage((int)pagination.getCurrent());
                List<CardTradeInfoStereoPageRpcResVO> pageRpcResVOS = new ArrayList<>();

                if(CollectionUtils.isNotEmpty(pagination.getRecords())){
                    pagination.getRecords().forEach(p->{
                        CardTradeInfoStereoPageRpcResVO resVO = CopyUtils.convert(p, CardTradeInfoStereoPageRpcResVO.class);
                        pageRpcResVOS.add(resVO);
                        convertAmountPrice(resVO, p);
                    });
                    fillOtherInfos(pageRpcResVOS);
                    pageDTO.setList(pageRpcResVOS);
                }

            }

            return pageDTO;
        } catch (FinhubException e){
            log.warn("获取stereo的交易记录警告，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取stereo的交易记录异常，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }

    }
    private void checkPageParam(CardTradeInfoStereoPageRpcReqDTO pageRpcReqDTO){
        if (Objects.isNull(pageRpcReqDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        exportTimeCheck(pageRpcReqDTO.getCreateLeDate(), pageRpcReqDTO.getCreateGeDate());

    }

    private void exportTimeCheck(Date commitTimeStart, Date commitTimeEnd) {
        if (Objects.isNull(commitTimeStart)||Objects.isNull(commitTimeEnd)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.PAYMENT_EXPORT_TIME_MOBTH_SCOPE_ERROR);
        }
        dateIntervalCheck(commitTimeStart, commitTimeEnd);
    }

    private void dateIntervalCheck(Date date1, Date date2) {
        int daysBetweenDates = DateUtils.getDaysBetweenDates(date2, date1);
        if (daysBetweenDates > 31){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.PAYMENT_EXPORT_TIME_MOBTH_SCOPE_ERROR);
        }
    }

    private void fillOtherInfos(List<CardTradeInfoStereoPageRpcResVO> resVOS){
        if (CollectionUtils.isEmpty(resVOS)) {
            return ;
        }
        List<String> companyIds = resVOS.stream().map(p -> p.getCompanyId()).distinct().collect(Collectors.toList());
        //获取公司名称
        List<Company> companies = irCompanyService.listIdMatchCompanies(companyIds);
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companies)){
            companyMap = companies.stream().collect(Collectors.toMap(p -> p.getId(), Function.identity(), (x, y) -> x));
        }

        for (CardTradeInfoStereoPageRpcResVO resVO : resVOS) {
            Company company = companyMap.get(resVO.getCompanyId());
            if (Objects.nonNull(company)){
                resVO.setCompanyName(company.getName());
            }
            if (Objects.equals(TransactionTypeEnum.CONSUME.getKey(), resVO.getTransactionTypeCode()) && BigDecimalUtils.hasPrice(resVO.getUncheckConsume())){
                resVO.setTradeCheckedStatus(false);
            }else {
                resVO.setTradeCheckedStatus(true);
            }
        }
    }

    private void convertAmountPrice(CardTradeInfoStereoPageRpcResVO resVO, CardTradeInfoStereoPageResDTO p){
        if (Objects.isNull(resVO) || Objects.isNull(p) ) {
            return ;
        }

        resVO.setCheckedConsumeDesc(getPriceRpcVo(p.getCheckedConsumeDesc()));
        resVO.setRefundConsumeDesc(getPriceRpcVo(p.getRefundConsumeDesc()));
        resVO.setTransactionAmountDesc(getPriceRpcVo(p.getTransactionAmountDesc()));
        resVO.setUncheckConsumeDesc(getPriceRpcVo(p.getUncheckConsumeDesc()));
        resVO.setObversionTotalPriceDesc(getPriceRpcVo(p.getObversionTotalPriceDesc()));
        resVO.setWrongPaidConsumeDesc(getPriceRpcVo(p.getWrongPaidConsumeDesc()));
        resVO.setUnNeedCheckConsumeDesc(getPriceRpcVo(p.getUnNeedCheckConsumeDesc()));
    }

    private PriceRpcVo getPriceRpcVo(TotalPrice checkedConsumeDesc) {
        if (Objects.isNull(checkedConsumeDesc)){
            return null;
        }
        PriceRpcVo priceRpcVo = new PriceRpcVo();
        priceRpcVo.setPrice(checkedConsumeDesc.getPrice());
        priceRpcVo.setShowPrice(checkedConsumeDesc.getShowPrice());
        return priceRpcVo;
    }


}
