package com.fenbei.fx.card.rpc.card.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.finhub.framework.core.converter.BaseConverterConfig;
import org.mapstruct.Mapper;

/**
 * CardRpcConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@Mapper(config = BaseConverterConfig.class)
public interface CardRpcConverter {

    static CardRpcConverter me() {
        return SpringUtil.getBean(CardRpcConverter.class);
    }


    //CardBalanceRespDTO convertToRpcResDTO(CardBalanceRpcRespDTO resDTO);
}
