package com.fenbei.fx.card.rpc.card;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fenbei.fx.card.api.card.ICardCreditStereoService;
import com.fenbei.fx.card.api.card.dto.CardCreditStereoRpcListDTO;
import com.fenbei.fx.card.api.card.dto.CardCreditStereoRpcQueryDTO;
import com.fenbei.fx.card.api.card.dto.PageDTO;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.CardPlatformCaseEnum;
import com.fenbei.fx.card.common.enums.CreditApplyStatusEnum;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditStereoPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.finhub.framework.core.page.Page;
import com.google.common.collect.Lists;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description stereo额度下发记录
 * @date 2024-04-22
 */
@Slf4j
@Validated
@DubboService
public class ICardCreditStereoServiceImpl implements ICardCreditStereoService {

    @DubboReference
    private IRCompanyService irCompanyService;


    @Override
    public PageDTO<CardCreditStereoRpcListDTO> applyList(CardCreditStereoRpcQueryDTO pageRpcReqDTO) {

        log.info("获取stereo的额度申请记录，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO));
        try {
            checkPageParam(pageRpcReqDTO);

            CardCreditStereoPageReqDTO pageReqDTO = CopyUtils.convert(pageRpcReqDTO, CardCreditStereoPageReqDTO.class);
            pageReqDTO.setOperationUserName(pageRpcReqDTO.getApplyOperationUserName());
            pageReqDTO.setBeginTime(pageRpcReqDTO.getBeginApplyTime());
            pageReqDTO.setEndTime(pageRpcReqDTO.getEndApplyTime());
            Page<CardCreditManagerPageResDTO> pagination = CardCreditManagerManager.me().pagination(pageReqDTO, pageRpcReqDTO.getPageNo(), pageRpcReqDTO.getPageSize());

            PageDTO<CardCreditStereoRpcListDTO> pageDTO = new PageDTO<>();
            if(Objects.nonNull(pagination)){
                pageDTO.setTotalSize((int) pagination.getTotal());
                pageDTO.setCurrentPage((int)pagination.getCurrent());
                List<CardCreditStereoRpcListDTO> pageRpcResVOS = buildCreditListDTO(pagination.getRecords());

                fillOtherInfos(pageRpcResVOS);
                pageDTO.setList(pageRpcResVOS);
            }

            return pageDTO;
        } catch (FinhubException e){
            log.warn("获取stereo的额度申请记录，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO), e);
            throw e;
        } catch (Exception e){
            log.error("获取stereo的额度申请记录，pageRpcReqDTO={}", JsonUtils.toJson(pageRpcReqDTO), e);
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.EXCEPTION);
        }

    }
    private void checkPageParam(CardCreditStereoRpcQueryDTO pageRpcReqDTO){
        if (Objects.isNull(pageRpcReqDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }

        exportTimeCheck(pageRpcReqDTO.getBeginApplyTime(), pageRpcReqDTO.getEndApplyTime());

    }

    private void exportTimeCheck(Date commitTimeStart, Date commitTimeEnd) {
        if (Objects.isNull(commitTimeStart)||Objects.isNull(commitTimeEnd)) {
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.PAYMENT_EXPORT_TIME_YEAR_SCOPE_ERROR);
        }
        dateIntervalCheck(commitTimeStart, commitTimeEnd);
    }

    private void dateIntervalCheck(Date date1, Date date2) {
        int daysBetweenDates = DateUtils.getDaysBetweenDates(date2, date1);
        if (daysBetweenDates > 366){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.PAYMENT_EXPORT_TIME_YEAR_SCOPE_ERROR);
        }
    }

    private void fillOtherInfos(List<CardCreditStereoRpcListDTO> resVOS){
        if (CollectionUtils.isEmpty(resVOS)) {
            return ;
        }
        List<String> companyIds = resVOS.stream().map(p -> p.getCompanyId()).distinct().collect(Collectors.toList());
        //获取公司名称
        List<Company> companies = irCompanyService.listIdMatchCompanies(companyIds);
        Map<String, Company> companyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(companies)){
            companyMap = companies.stream().collect(Collectors.toMap(p -> p.getId(), Function.identity(), (x, y) -> x));
        }

        for (CardCreditStereoRpcListDTO resVO : resVOS) {
            Company company = companyMap.get(resVO.getCompanyId());
            if (Objects.nonNull(company)){
                resVO.setCompanyName(company.getName());
            }

        }
    }

    private List<CardCreditStereoRpcListDTO> buildCreditListDTO(List<CardCreditManagerPageResDTO> records) {
        List<CardCreditStereoRpcListDTO> userCardCreditGrantListDTOS = Lists.newArrayList();
        if(!org.springframework.util.CollectionUtils.isEmpty(records)){
            for (CardCreditManagerPageResDTO record : records) {
                CardCreditStereoRpcListDTO userCardCreditGrantListDTO = new CardCreditStereoRpcListDTO();
                userCardCreditGrantListDTO.setCompanyId(record.getCompanyId());
                userCardCreditGrantListDTO.setRecordId(String.valueOf(record.getId()));
                userCardCreditGrantListDTO.setApplyReason(record.getApplyReason());
                userCardCreditGrantListDTO.setApplyTitle(record.getApplyTitle());
                userCardCreditGrantListDTO.setApplyOperationUserName(record.getOperationUserName());
                userCardCreditGrantListDTO.setApplyOperationUserDept(record.getOperationUserDept());

                userCardCreditGrantListDTO.setApplyAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAmount())));
                userCardCreditGrantListDTO.setCostType(record.getCostType());
                userCardCreditGrantListDTO.setCostTypeName(record.getCostTypeName());
                //费用转换
                String costAttribution = "";
                if (StringUtils.isNotBlank(record.getCostAttribution())){
                    List<BudgetCostAttributionDTO> costAttributionDTOS = JsonUtils.toObj(record.getCostAttribution(), new TypeReference<List<BudgetCostAttributionDTO>>() {});
                    for (BudgetCostAttributionDTO budgetCostAttributionDTO:costAttributionDTOS){
                        costAttribution = budgetCostAttributionDTO.getCost_attribution_category() + ":" + budgetCostAttributionDTO.getCost_attribution_name();
                    }
                }
                userCardCreditGrantListDTO.setCostAttribution(costAttribution);
                userCardCreditGrantListDTO.setApplyTime(DateUtils.formatTime(record.getCreateTime()));
                userCardCreditGrantListDTO.setApplyStatusDesc(CreditApplyStatusEnum.getEnum(record.getApplyStatus()).getName());
                userCardCreditGrantListDTO.setBackedAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getReturnedAmount())));
                userCardCreditGrantListDTO.setWritenOffAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getWritenOffAmount())));
                userCardCreditGrantListDTO.setWriteOffIngAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getWritingOffAmount())));
                userCardCreditGrantListDTO.setUnWriteOffAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getUnwriteOffAmount())));
                userCardCreditGrantListDTO.setAvailableAmount(CurrencyNumberFormatUtil.moneyFormart(CardPlatformCaseEnum.getEnumByCardPlatformCode(record.getCardPlatform()).getCurrencyEnum(),BigDecimalUtils.fen2yuan(record.getAvalibleAmount())));
                userCardCreditGrantListDTO.setBizNo(record.getBizNo());
                userCardCreditGrantListDTO.setApplyMeaningNo(record.getApplyMeaningNo());
                userCardCreditGrantListDTO.setApplyOperationUserName(record.getOperationUserName());
                userCardCreditGrantListDTO.setApplyOperationUserDept(record.getOperationUserDept());
                userCardCreditGrantListDTO.setApplyTransNo(record.getApplyTransNo());
                userCardCreditGrantListDTOS.add(userCardCreditGrantListDTO);
            }
        }
        return userCardCreditGrantListDTOS;
    }

}
