package com.fenbei.fx.card.rpc;

import com.fenbei.fx.card.rpc.configuration.RpcConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Import;

/**
 * <pre>
 * Rpc Server 启动类
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@SpringBootApplication
@Import({RpcConfiguration.class})
public class RpcServer {

    public static void start(SpringApplicationBuilder springApplicationBuilder, String[] args) {
        springApplicationBuilder.run(args); // 默认为 Web 方式启动
    }
}
