package com.fenbei.fx.card.rpc.configuration;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * RpcConfiguration
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@Configuration
@EnableDubbo(scanBasePackages = {"com.fenbei.fx.card.rpc"})
@ComponentScan(basePackages = {"com.fenbei.fx.card.rpc"})
public class RpcConfiguration {

}
