package com.fenbei.fx.card.web.bankcardflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.math.BigDecimal;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 分页 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@ApiModel("国际卡的操作流水,包含额度申请退回和消费退款 分页 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankCardFlowPageReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "xxx", value = "id")
    private Long id;

    /**
     * 卡ID
     */
    @ApiModelProperty(example = "xxx", value = "卡ID")
    private String fxCardId;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "xxx", value = "公司id")
    private String companyId;

    /**
     * 员工id
     */
    @ApiModelProperty(example = "xxx", value = "员工id")
    private String employeeId;

    /**
     * 
     */
    @ApiModelProperty(example = "xxx", value = "")
    private String bizNo;

    /**
     * 4申请额度,5退还额度,11消费,12退款,16冲正,41还款,51企业回收额度,52系统回收额度,53还款退回
     */
    @ApiModelProperty(example = "xxx", value = "4申请额度,5退还额度,11消费,12退款,16冲正,41还款,51企业回收额度,52系统回收额度,53还款退回")
    private Integer operationType;

    /**
     * 当前额度
     */
    @ApiModelProperty(example = "xxx", value = "当前额度")
    private BigDecimal currentAmount;

    /**
     * 操作金额 单位：分
     */
    @ApiModelProperty(example = "xxx", value = "操作金额 单位：分")
    private BigDecimal operationAmount;

    /**
     * 卡的余额
     */
    @ApiModelProperty(example = "xxx", value = "卡的余额")
    private BigDecimal balance;

    /**
     * 1普通模式 2备用金模式
     */
    @ApiModelProperty(example = "xxx", value = "1普通模式 2备用金模式")
    private Integer cardModel;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

}
