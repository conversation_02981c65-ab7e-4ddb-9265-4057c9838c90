package com.fenbei.fx.card.web.cardcreditapplyorder.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderAddReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderListResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderPageResDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.CardCreditApplyOrderShowResDTO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderAddReqVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderListReqVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderListResVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderModifyReqVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderPageReqVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderPageResVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderRemoveReqVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderShowResVO;
import com.fenbei.fx.card.web.cardcreditapplyorder.vo.CardCreditApplyOrderVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡额度发放单 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-22
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardCreditApplyOrderVOConverter extends BaseVOConverter<CardCreditApplyOrderDTO, CardCreditApplyOrderVO> {

    static CardCreditApplyOrderVOConverter me() {
        return SpringUtil.getBean(CardCreditApplyOrderVOConverter.class);
    }

    CardCreditApplyOrderAddReqDTO convertToCardCreditApplyOrderAddReqDTO(CardCreditApplyOrderAddReqVO cardCreditApplyOrderAddReqVO);

    CardCreditApplyOrderShowResVO convertToCardCreditApplyOrderShowResVO(CardCreditApplyOrderShowResDTO cardCreditApplyOrderShowResDTO);

    CardCreditApplyOrderModifyReqDTO convertToCardCreditApplyOrderModifyReqDTO(CardCreditApplyOrderModifyReqVO cardCreditApplyOrderModifyReqVO);

    CardCreditApplyOrderRemoveReqDTO convertToCardCreditApplyOrderRemoveReqDTO(CardCreditApplyOrderRemoveReqVO cardCreditApplyOrderRemoveReqVO);

    CardCreditApplyOrderListReqDTO convertToCardCreditApplyOrderListReqDTO(CardCreditApplyOrderListReqVO cardCreditApplyOrderReqVO);

    CardCreditApplyOrderPageReqDTO convertToCardCreditApplyOrderPageReqDTO(CardCreditApplyOrderPageReqVO cardCreditApplyOrderPageReqVO);

    CardCreditApplyOrderListResVO convertToCardCreditApplyOrderListResVO(CardCreditApplyOrderListResDTO cardCreditApplyOrderListResDTO);

    List<CardCreditApplyOrderListResVO> convertToCardCreditApplyOrderListResVOList(List<CardCreditApplyOrderListResDTO> cardCreditApplyOrderListResDTOList);

    Page<CardCreditApplyOrderPageResVO> convertToCardCreditApplyOrderPageResVOPage(Page<CardCreditApplyOrderPageResDTO> cardCreditApplyOrderPageResDTOPage);

    List<CardCreditApplyOrderAddReqDTO> convertToCardCreditApplyOrderAddReqDTOList(List<CardCreditApplyOrderAddReqVO> cardCreditApplyOrderAddReqVOList);

    List<CardCreditApplyOrderShowResVO> convertToCardCreditApplyOrderShowResVOList(List<CardCreditApplyOrderShowResDTO> cardCreditApplyOrderShowResDTOList);
}
