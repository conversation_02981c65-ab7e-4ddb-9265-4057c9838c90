package com.fenbei.fx.card.web.common.base;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbeitong.finhub.auth.constant.ConfigConstant;
import com.fenbeitong.finhub.auth.constant.UserAttributeConstant;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.model.dto.privilege.EmployeePrivilegeDto;
import com.fenbeitong.usercenter.api.model.enums.employee.EmployeeStatus;
import com.fenbeitong.usercenter.api.service.privilege.IPrivilegeService;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.web.dto.CurrentUser;
import com.finhub.framework.swift.servlet.HttpRequest;
import com.finhub.framework.web.controller.BaseController;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.dubbo.config.annotation.DubboReference;

import java.io.Serializable;
import java.util.Map;

import static com.fenbei.fx.card.common.constant.GlobalCoreResponseCode.*;

/**
 * 公共controller
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/18
 */
public class CommonController<S extends BaseService, C extends BaseVOConverter> extends BaseController<S, C> {

    public static final Integer WEB_TYPE = 1;
    public static final String MENU_CODE = "menuCode";

    /*@DubboReference
    private IPrivilegeService privilegeService;

    *//**
     * 获取当前登录用户对象
     *
     * @return {CurrentUser}
     *//*
    public CurrentUser getCurrentUser() {
        UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
        CurrentUser user = new CurrentUser();
        if (Func.isNotEmpty(userComInfo) && Func.isNotEmpty(userComInfo.getUser_id())) {
            user.setId(userComInfo.getUser_id());
            user.setName(userComInfo.getUser_name());
            Map<String, Serializable> companyMap = user.getCONTEXT_MAP();
            companyMap.put("companyId", userComInfo.getCompany_id());
        }
        return user;
    }
    public void checkPrivilege() {
        UserComInfoVO userComInfo = (UserComInfoVO) request.getAttribute(UserAttributeConstant.USER_COM_INFO);
        String currentUserId = userComInfo.getUser_id();
        String currentUserName = userComInfo.getUser_name();
        String currentUserPhone = userComInfo.getUser_phone();
        String currentUserOrgName = userComInfo.getOrgUnit_name();
        String companyId = userComInfo.getCompany_id();
        if (ObjUtils.isEmpty(currentUserId) || ObjUtils.isEmpty(currentUserName) || ObjUtils.isEmpty(currentUserPhone) || ObjUtils.isEmpty(currentUserOrgName) || ObjUtils.isEmpty(companyId)) {
            throw new FinhubException(ConfigConstant.NO_AUTH_VALUE);
        }

        String menuCode = request.getHeader(MENU_CODE);
        if (ObjUtils.isEmpty(menuCode)) {
            throw new FxCardException(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        FinhubLogger.info("菜单权限请求参数companyId={},currentUserId={},menuCode={},resourceType={}",companyId.toString(), currentUserId.toString(), menuCode, WEB_TYPE);
        EmployeePrivilegeDto employeePrivilege = privilegeService.queryEmployeeMenuPrivilegeByCode(companyId.toString(), currentUserId.toString(), menuCode, WEB_TYPE);
        FinhubLogger.info("菜单权限请求结果companyId={},currentUserId={},menuCode={},resourceType={},result={}",companyId.toString(), currentUserId.toString(), menuCode, WEB_TYPE, JsonUtils.toJson(employeePrivilege));
        if (employeePrivilege == null) {
            throw new FxCardException(GlobalCoreResponseCode.RemoteService401);
        }
        //HasDredge 是否开通菜单权限 HasAuth 是否有菜单权限
        if (!employeePrivilege.getHasDredge() || !employeePrivilege.getHasAuth()) {
            throw new FxCardException(GlobalCoreResponseCode.RemoteService401);
        }
    }
    *//**
     * 获取当前登录用户id
     *
     * @return {String}
     *//*
    public String getUserId() {
        return this.getCurrentUser().getId();
    }

    *//**
     * 获取当前登录用户名
     *
     * @return {String}
     *//*
    public String getStaffName() {
        return this.getCurrentUser().getName();
    }

    public String getCompanyId() {
        return (String) getCurrentUser().getCONTEXT_MAP().get("companyId");
    }*/

    /*protected void checkEmployeeStatus(EmployeeContract employeeData) {
        if (ObjUtils.isEmpty(employeeData)) {
            //为空，员工不存在或者已经被删除离职
            throw new FxCardException(BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST2);
        }
        if (employeeData.getStatus() == EmployeeStatus.INACTIVE.getKey()) {
            //员工禁用
            throw new FxCardException(BANK_ACCOUNT_CARD_ENABLE_EMPLOYEE_NOT_EXIST3);
        }
    }*/

}
