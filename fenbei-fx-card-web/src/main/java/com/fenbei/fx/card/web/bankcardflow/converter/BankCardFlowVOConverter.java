package com.fenbei.fx.card.web.bankcardflow.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowAddReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowListReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowListResVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowModifyReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowPageReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowPageResVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowRemoveReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowShowResVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface BankCardFlowVOConverter extends BaseVOConverter<BankCardFlowDTO, BankCardFlowVO> {

    static BankCardFlowVOConverter me() {
        return SpringUtil.getBean(BankCardFlowVOConverter.class);
    }

    BankCardFlowAddReqDTO convertToBankCardFlowAddReqDTO(BankCardFlowAddReqVO bankCardFlowAddReqVO);

    BankCardFlowShowResVO convertToBankCardFlowShowResVO(BankCardFlowShowResDTO bankCardFlowShowResDTO);

    BankCardFlowModifyReqDTO convertToBankCardFlowModifyReqDTO(BankCardFlowModifyReqVO bankCardFlowModifyReqVO);

    BankCardFlowRemoveReqDTO convertToBankCardFlowRemoveReqDTO(BankCardFlowRemoveReqVO bankCardFlowRemoveReqVO);

    BankCardFlowListReqDTO convertToBankCardFlowListReqDTO(BankCardFlowListReqVO bankCardFlowReqVO);

    BankCardFlowPageReqDTO convertToBankCardFlowPageReqDTO(BankCardFlowPageReqVO bankCardFlowPageReqVO);

    BankCardFlowListResVO convertToBankCardFlowListResVO(BankCardFlowListResDTO bankCardFlowListResDTO);

    List<BankCardFlowListResVO> convertToBankCardFlowListResVOList(List<BankCardFlowListResDTO> bankCardFlowListResDTOList);

    Page<BankCardFlowPageResVO> convertToBankCardFlowPageResVOPage(Page<BankCardFlowPageResDTO> bankCardFlowPageResDTOPage);

    List<BankCardFlowAddReqDTO> convertToBankCardFlowAddReqDTOList(List<BankCardFlowAddReqVO> bankCardFlowAddReqVOList);

    List<BankCardFlowShowResVO> convertToBankCardFlowShowResVOList(List<BankCardFlowShowResDTO> bankCardFlowShowResDTOList);
}
