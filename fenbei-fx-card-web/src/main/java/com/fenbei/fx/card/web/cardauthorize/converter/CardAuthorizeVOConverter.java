package com.fenbei.fx.card.web.cardauthorize.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeAddReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeListReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeListResVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeModifyReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizePageReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizePageResVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeRemoveReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeShowResVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡授权表 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardAuthorizeVOConverter extends BaseVOConverter<CardAuthorizeDTO, CardAuthorizeVO> {

    static CardAuthorizeVOConverter me() {
        return SpringUtil.getBean(CardAuthorizeVOConverter.class);
    }

    CardAuthorizeAddReqDTO convertToCardAuthorizeAddReqDTO(CardAuthorizeAddReqVO cardAuthorizeAddReqVO);

    CardAuthorizeShowResVO convertToCardAuthorizeShowResVO(CardAuthorizeShowResDTO cardAuthorizeShowResDTO);

    CardAuthorizeModifyReqDTO convertToCardAuthorizeModifyReqDTO(CardAuthorizeModifyReqVO cardAuthorizeModifyReqVO);

    CardAuthorizeRemoveReqDTO convertToCardAuthorizeRemoveReqDTO(CardAuthorizeRemoveReqVO cardAuthorizeRemoveReqVO);

    CardAuthorizeListReqDTO convertToCardAuthorizeListReqDTO(CardAuthorizeListReqVO cardAuthorizeReqVO);

    CardAuthorizePageReqDTO convertToCardAuthorizePageReqDTO(CardAuthorizePageReqVO cardAuthorizePageReqVO);

    CardAuthorizeListResVO convertToCardAuthorizeListResVO(CardAuthorizeListResDTO cardAuthorizeListResDTO);

    List<CardAuthorizeListResVO> convertToCardAuthorizeListResVOList(List<CardAuthorizeListResDTO> cardAuthorizeListResDTOList);

    Page<CardAuthorizePageResVO> convertToCardAuthorizePageResVOPage(Page<CardAuthorizePageResDTO> cardAuthorizePageResDTOPage);

    List<CardAuthorizeAddReqDTO> convertToCardAuthorizeAddReqDTOList(List<CardAuthorizeAddReqVO> cardAuthorizeAddReqVOList);

    List<CardAuthorizeShowResVO> convertToCardAuthorizeShowResVOList(List<CardAuthorizeShowResDTO> cardAuthorizeShowResDTOList);
}
