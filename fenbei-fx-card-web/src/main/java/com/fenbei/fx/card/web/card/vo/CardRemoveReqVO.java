package com.fenbei.fx.card.web.card.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡 删除 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("国际卡 删除 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardRemoveReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private Long id;

    /**
     * 卡id
     */
    @ApiModelProperty(example = "0", value = "卡id")
    private String fxCardId;

    /**
     * 银行卡id
     */
    @ApiModelProperty(example = "0", value = "银行卡id")
    private String bankCardId;

    /**
     * 银行卡编号
     */
    @ApiModelProperty(example = "0", value = "银行卡编号")
    private String bankCardNo;

    /**
     * 公司账户id
     */
    @ApiModelProperty(example = "0", value = "公司账户id")
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    @ApiModelProperty(example = "0", value = "发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL")
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    @ApiModelProperty(example = "0", value = "卡片形式：1-PHYSICAL、2-VIRTUAL")
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    @ApiModelProperty(example = "0", value = "卡的cvv")
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    @ApiModelProperty(example = "0", value = "卡的到期年份")
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    @ApiModelProperty(example = "0", value = "卡的到期月份")
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    @ApiModelProperty(example = "0", value = "卡片上的姓名")
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    @ApiModelProperty(example = "0", value = "发卡渠道 AIRWALLEX")
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    @ApiModelProperty(example = "0", value = "发卡的品牌 VISA")
    private String cardBrand;

    /**
     * 发卡时间
     */
    @ApiModelProperty(example = "0", value = "发卡时间")
    private Date cardPublicTime;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    @ApiModelProperty(example = "0", value = "卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结")
    private Integer cardStatus;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    @ApiModelProperty(example = "0", value = "实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功")
    private Integer activeStatus;

    /**
     * 持卡人id
     */
    @ApiModelProperty(example = "0", value = "持卡人id")
    private String fxCardholderId;

    /**
     * 卡用途
     */
    @ApiModelProperty(example = "0", value = "卡用途")
    private String cardPurpose;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "0", value = "币种 美元-USD")
    private String currency;

    /**
     * 卡可用余额
     */
    @ApiModelProperty(example = "0", value = "卡可用余额")
    private BigDecimal balance;

    /**
     * 管控规则：频率，币种，金额
     */
    @ApiModelProperty(example = "0", value = "管控规则：频率，币种，金额")
    private String cardLimits;

    /**
     * 创建人
     */
    @ApiModelProperty(example = "0", value = "创建人")
    private String createUserId;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "0", value = "公司id")
    private String companyId;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "0", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

}
