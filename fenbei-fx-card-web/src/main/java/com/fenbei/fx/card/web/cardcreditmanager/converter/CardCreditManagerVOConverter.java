package com.fenbei.fx.card.web.cardcreditmanager.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerShowResDTO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerAddReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerListReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerListResVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerModifyReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerPageReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerPageResVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerRemoveReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerShowResVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡额度申请退回管理表 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardCreditManagerVOConverter extends BaseVOConverter<CardCreditManagerDTO, CardCreditManagerVO> {

    static CardCreditManagerVOConverter me() {
        return SpringUtil.getBean(CardCreditManagerVOConverter.class);
    }

    CardCreditManagerAddReqDTO convertToCardCreditManagerAddReqDTO(CardCreditManagerAddReqVO cardCreditManagerAddReqVO);

    CardCreditManagerShowResVO convertToCardCreditManagerShowResVO(CardCreditManagerShowResDTO cardCreditManagerShowResDTO);

    CardCreditManagerModifyReqDTO convertToCardCreditManagerModifyReqDTO(CardCreditManagerModifyReqVO cardCreditManagerModifyReqVO);

    CardCreditManagerRemoveReqDTO convertToCardCreditManagerRemoveReqDTO(CardCreditManagerRemoveReqVO cardCreditManagerRemoveReqVO);

    CardCreditManagerListReqDTO convertToCardCreditManagerListReqDTO(CardCreditManagerListReqVO cardCreditManagerReqVO);

    CardCreditManagerPageReqDTO convertToCardCreditManagerPageReqDTO(CardCreditManagerPageReqVO cardCreditManagerPageReqVO);

    CardCreditManagerListResVO convertToCardCreditManagerListResVO(CardCreditManagerListResDTO cardCreditManagerListResDTO);

    List<CardCreditManagerListResVO> convertToCardCreditManagerListResVOList(List<CardCreditManagerListResDTO> cardCreditManagerListResDTOList);

    Page<CardCreditManagerPageResVO> convertToCardCreditManagerPageResVOPage(Page<CardCreditManagerPageResDTO> cardCreditManagerPageResDTOPage);

    List<CardCreditManagerAddReqDTO> convertToCardCreditManagerAddReqDTOList(List<CardCreditManagerAddReqVO> cardCreditManagerAddReqVOList);

    List<CardCreditManagerShowResVO> convertToCardCreditManagerShowResVOList(List<CardCreditManagerShowResDTO> cardCreditManagerShowResDTOList);
}
