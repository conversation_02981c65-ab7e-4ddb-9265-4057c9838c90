package com.fenbei.fx.card.web.cardholder.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
@Data
public class CardholderApproveReqVo implements Serializable {
    private static final long serialVersionUID = 2463406841496400119L;

    @NotBlank
    private String applyId;

    @NotNull
    private Integer status;

    private String reason;
}
