package com.fenbei.fx.card.web.cardemployeemodelconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡员工使用模式配置 删除 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@ApiModel("国际卡员工使用模式配置 删除 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardEmployeeModelConfigRemoveReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    @ApiModelProperty(example = "xxx", value = "主键ID")
    private String id;

    /**
     * 企业ID
     */
    @ApiModelProperty(example = "xxx", value = "企业ID")
    private String companyId;

    /**
     * 员工ID
     */
    @ApiModelProperty(example = "xxx", value = "员工ID")
    private String employeeId;

    /**
     * 国际卡员工生效模式: 1.普通模式,2.备用金模式
     */
    @ApiModelProperty(example = "xxx", value = "国际卡员工生效模式: 1.普通模式,2.备用金模式")
    private Integer activeModel;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

}
