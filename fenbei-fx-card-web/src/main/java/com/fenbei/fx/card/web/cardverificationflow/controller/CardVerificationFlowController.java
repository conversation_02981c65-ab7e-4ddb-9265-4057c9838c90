package com.fenbei.fx.card.web.cardverificationflow.controller;

import com.fenbei.fx.card.service.cardverificationflow.CardVerificationFlowService;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.fenbei.fx.card.web.cardverificationflow.converter.CardVerificationFlowVOConverter;
import com.fenbei.fx.card.web.cardverificationflow.vo.*;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 核销记录表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Api(tags = {"核销记录表 API 接口"})
@Slf4j
@Validated
@RestController
public class CardVerificationFlowController extends BaseController<CardVerificationFlowService, CardVerificationFlowVOConverter> {

    @ApiOperation(value = "核销记录表-列表")
    @RequestMapping(value = "cardVerificationFlow/list", method = {RequestMethod.GET})
    public ItemsResult<CardVerificationFlowListResVO> list(CardVerificationFlowListReqVO cardVerificationFlowListReqVO) {
        CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO = converter.convertToCardVerificationFlowListReqDTO(cardVerificationFlowListReqVO);

        List<CardVerificationFlowListResDTO> cardVerificationFlowListResDTOList = service.list(cardVerificationFlowListReqDTO);
        List<CardVerificationFlowListResVO> items = converter.convertToCardVerificationFlowListResVOList(cardVerificationFlowListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "核销记录表-First查询")
    @RequestMapping(value = "cardVerificationFlow/listOne", method = {RequestMethod.GET})
    public ItemResult<CardVerificationFlowListResVO> listOne(CardVerificationFlowListReqVO cardVerificationFlowListReqVO) {
        CardVerificationFlowListReqDTO cardVerificationFlowListReqDTO = converter.convertToCardVerificationFlowListReqDTO(cardVerificationFlowListReqVO);

        CardVerificationFlowListResDTO cardVerificationFlowListResDTO = service.listOne(cardVerificationFlowListReqDTO);
        CardVerificationFlowListResVO item = converter.convertToCardVerificationFlowListResVO(cardVerificationFlowListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "核销记录表-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardVerificationFlow/page", method = {RequestMethod.GET})
    public PageResult<CardVerificationFlowPageResVO> page(CardVerificationFlowPageReqVO cardVerificationFlowPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardVerificationFlowPageReqDTO cardVerificationFlowPageReqDTO = converter.convertToCardVerificationFlowPageReqDTO(cardVerificationFlowPageReqVO);

        Page<CardVerificationFlowPageResDTO> cardVerificationFlowPageResDTOPage = service.pagination(cardVerificationFlowPageReqDTO, current, size);
        Page<CardVerificationFlowPageResVO> cardVerificationFlowPageResVOPage = converter.convertToCardVerificationFlowPageResVOPage(cardVerificationFlowPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardVerificationFlowPageResVOPage.getTotal(), cardVerificationFlowPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "核销记录表-新增")
    @RequestMapping(value = "cardVerificationFlow/add", method = {RequestMethod.POST})
    public MessageResult add(CardVerificationFlowAddReqVO cardVerificationFlowAddReqVO) {
        CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO = converter.convertToCardVerificationFlowAddReqDTO(cardVerificationFlowAddReqVO);

        Boolean isSuccess = service.add(cardVerificationFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-新增(所有字段)")
    @RequestMapping(value = "cardVerificationFlow/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardVerificationFlowAddReqVO cardVerificationFlowAddReqVO) {
        CardVerificationFlowAddReqDTO cardVerificationFlowAddReqDTO = converter.convertToCardVerificationFlowAddReqDTO(cardVerificationFlowAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardVerificationFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-批量新增(所有字段)")
    @RequestMapping(value = "cardVerificationFlow/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardVerificationFlowAddReqVO> cardVerificationFlowAddReqVOList) {
        List<CardVerificationFlowAddReqDTO> cardVerificationFlowAddReqDTOList = converter.convertToCardVerificationFlowAddReqDTOList(cardVerificationFlowAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardVerificationFlowAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardVerificationFlow/show", method = {RequestMethod.GET})
    public ItemResult<CardVerificationFlowShowResVO> show(String id) {
        CardVerificationFlowShowResDTO cardVerificationFlowShowResDTO = service.show(id);

        CardVerificationFlowShowResVO item = converter.convertToCardVerificationFlowShowResVO(cardVerificationFlowShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "核销记录表-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardVerificationFlow/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardVerificationFlowShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardVerificationFlowShowResDTO> cardVerificationFlowShowResDTOList = service.showByIds(ids.getItems());

        List<CardVerificationFlowShowResVO> items = converter.convertToCardVerificationFlowShowResVOList(cardVerificationFlowShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "核销记录表-更新")
    @RequestMapping(value = "cardVerificationFlow/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardVerificationFlowModifyReqVO cardVerificationFlowModifyReqVO) {
        CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO = converter.convertToCardVerificationFlowModifyReqDTO(cardVerificationFlowModifyReqVO);

        Boolean isSuccess = service.modify(cardVerificationFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-更新(所有字段)")
    @RequestMapping(value = "cardVerificationFlow/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardVerificationFlowModifyReqVO cardVerificationFlowModifyReqVO) {
        CardVerificationFlowModifyReqDTO cardVerificationFlowModifyReqDTO = converter.convertToCardVerificationFlowModifyReqDTO(cardVerificationFlowModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardVerificationFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardVerificationFlow/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardVerificationFlow/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "核销记录表-删除(参数)")
    @RequestMapping(value = "cardVerificationFlow/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardVerificationFlowRemoveReqVO cardVerificationFlowRemoveReqVO) {
        CardVerificationFlowRemoveReqDTO cardVerificationFlowRemoveReqDTO = converter.convertToCardVerificationFlowRemoveReqDTO(cardVerificationFlowRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardVerificationFlowRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
