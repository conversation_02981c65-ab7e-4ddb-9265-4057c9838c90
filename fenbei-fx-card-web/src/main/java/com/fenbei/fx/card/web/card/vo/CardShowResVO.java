package com.fenbei.fx.card.web.card.vo;

import com.fenbei.fx.card.service.card.dto.CardCanOperationDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import com.fenbeitong.dech.api.model.dto.airwallex.BaseAirwallexRpcDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 国际卡 详情 ResVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("国际卡 详情 ResVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardShowResVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    @ApiModelProperty(example = "0", value = "申请类型 1.创建 2.更新")
    private Integer applyType;

    @ApiModelProperty(example = "0", value = "申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功")
    private Integer applyStatus;

    /**
     * 申请人名
     */
    @ApiModelProperty(example = "0", value = "申请人名")
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    @ApiModelProperty(example = "0", value = "申请人姓")
    private String applyerLastName;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private Long id;

    /**
     * 操作申请id
     */
    @ApiModelProperty(example = "0", value = "操作申请id")
    private String applyId;

    /**
     * 卡id
     */
    @ApiModelProperty(example = "0", value = "卡id")
    private String fxCardId;

    /**
     * 银行卡id
     */
    @ApiModelProperty(example = "0", value = "银行卡id")
    private String bankCardId;

    /**
     * 银行卡编号
     */
    @ApiModelProperty(example = "0", value = "银行卡编号")
    private String bankCardNo;

    private String maskBankCardNo;

    /**
     * 公司账户id
     */
    @ApiModelProperty(example = "0", value = "公司账户id")
    private String companyAccountId;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    @ApiModelProperty(example = "0", value = "发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL")
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    @ApiModelProperty(example = "0", value = "卡片形式：1-PHYSICAL、2-VIRTUAL")
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    @ApiModelProperty(example = "0", value = "卡的cvv")
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    @ApiModelProperty(example = "0", value = "卡的到期年份")
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    @ApiModelProperty(example = "0", value = "卡的到期月份")
    private String cardExpiryMonth;


    /**
     * 实体卡支付密码
     */
    @ApiModelProperty(example = "0", value = "卡的密码")
    private String cardPin;


    /**
     * 卡片上的姓名
     */
    @ApiModelProperty(example = "0", value = "卡片上的姓名")
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    @ApiModelProperty(example = "0", value = "发卡渠道 AIRWALLEX")
    private String cardPlatform;

    /**
     * 发卡渠道 AIRWALLEX
     */
    @ApiModelProperty(example = "0", value = "发卡渠道中文名称 AIRWALLEX")
    private String cardPlatformName;
    /**
     * 发卡渠道 图标
     */
    @ApiModelProperty(example = "0", value = "发卡渠道 AIRWALLEX图标")
    private String cardPlatformIcon;

    /**
     * 发卡的品牌 VISA
     */
    @ApiModelProperty(example = "0", value = "发卡的品牌 VISA")
    private String cardBrand;

    /**
     * 发卡的品牌 图标
     */
    private String cardBrandIcon;

    /**
     * 发卡时间
     */
    @ApiModelProperty(example = "0", value = "发卡时间")
    private Date cardPublicTime;

    /**
     * 卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结
     */
    @ApiModelProperty(example = "0", value = "卡状态：1.生效中 2.已禁用 3.挂失 4.被盗 5.已注销 6.冻结")
    private Integer cardStatus;

    private String cardStatusStr;

    /**
     * 实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功
     */
    @ApiModelProperty(example = "0", value = "实体卡激活状态：0.无需激活 1.待激活 2.激活中 3.激活失败 4.激活成功")
    private Integer activeStatus;

    /**
     * 持卡人id
     */
    @ApiModelProperty(example = "0", value = "持卡人id")
    private String fxCardholderId;

    /**
     * 卡用途
     */
    @ApiModelProperty(example = "0", value = "卡用途")
    private String cardPurpose;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "0", value = "币种 美元-USD")
    private String currency;

    /**
     * 币种符号 $
     */
    private String currencySymbol;

    /**
     * 币种 美元
     */
    private String currencyName;

    /**
     * 卡可用余额
     */
    @ApiModelProperty(example = "0", value = "卡可用余额")
    private BigDecimal balance;

    List<BaseAirwallexRpcDTO.Limit> showLimits ;

    /**
     * 创建人
     */
    @ApiModelProperty(example = "0", value = "创建人")
    private String createUserId;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "0", value = "公司id")
    private String companyId;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "0", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

    /**
     * 申请人手机号+区号
     */
    private String applyerPhone;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    private CardCanOperationDTO operationDTO;

    private String cardHolderName;

    /**
     * 手机号
     */
    private String phone;
    /**
     *
     */
    private String phoneAreaCode = "+86";

    /**
     * 地址
     */
    private String address;

    /**
     * 邮寄地址
     */
    private String postalAddress;

    /**
     * 用户地址
     */
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;

    /**
     * 卡可用展示美元余额
     */
    private String showUSDBalance;

    /**
     * 消费模版id
     */
    private String formId;




}
