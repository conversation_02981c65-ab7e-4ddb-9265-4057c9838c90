package com.fenbei.fx.card.web.cardoperflow.controller;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.cardoperflow.CardOperFlowService;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import com.fenbei.fx.card.web.cardoperflow.converter.CardOperFlowVOConverter;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowAddReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowListReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowListResVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowModifyReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowPageReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowPageResVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowRemoveReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 卡操作流水 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Api(tags = {"卡操作流水 API 接口"})
@Slf4j
@Validated
@RestController
public class CardOperFlowController extends BaseController<CardOperFlowService, CardOperFlowVOConverter> {

    @ApiOperation(value = "卡操作流水-列表")
    @RequestMapping(value = "cardOperFlow/list", method = {RequestMethod.GET})
    public ItemsResult<CardOperFlowListResVO> list(CardOperFlowListReqVO cardOperFlowListReqVO) {
        CardOperFlowListReqDTO cardOperFlowListReqDTO = converter.convertToCardOperFlowListReqDTO(cardOperFlowListReqVO);

        List<CardOperFlowListResDTO> cardOperFlowListResDTOList = service.list(cardOperFlowListReqDTO);
        List<CardOperFlowListResVO> items = converter.convertToCardOperFlowListResVOList(cardOperFlowListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "卡操作流水-First查询")
    @RequestMapping(value = "cardOperFlow/listOne", method = {RequestMethod.GET})
    public ItemResult<CardOperFlowListResVO> listOne(CardOperFlowListReqVO cardOperFlowListReqVO) {
        CardOperFlowListReqDTO cardOperFlowListReqDTO = converter.convertToCardOperFlowListReqDTO(cardOperFlowListReqVO);

        CardOperFlowListResDTO cardOperFlowListResDTO = service.listOne(cardOperFlowListReqDTO);
        CardOperFlowListResVO item = converter.convertToCardOperFlowListResVO(cardOperFlowListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "卡操作流水-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardOperFlow/page", method = {RequestMethod.GET})
    public PageResult<CardOperFlowPageResVO> page(CardOperFlowPageReqVO cardOperFlowPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardOperFlowPageReqDTO cardOperFlowPageReqDTO = converter.convertToCardOperFlowPageReqDTO(cardOperFlowPageReqVO);

        Page<CardOperFlowPageResDTO> cardOperFlowPageResDTOPage = service.pagination(cardOperFlowPageReqDTO, current, size);
        Page<CardOperFlowPageResVO> cardOperFlowPageResVOPage = converter.convertToCardOperFlowPageResVOPage(cardOperFlowPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardOperFlowPageResVOPage.getTotal(), cardOperFlowPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "卡操作流水-新增")
    @RequestMapping(value = "cardOperFlow/add", method = {RequestMethod.POST})
    public MessageResult add(CardOperFlowAddReqVO cardOperFlowAddReqVO) {
        CardOperFlowAddReqDTO cardOperFlowAddReqDTO = converter.convertToCardOperFlowAddReqDTO(cardOperFlowAddReqVO);

        Boolean isSuccess = service.add(cardOperFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-新增(所有字段)")
    @RequestMapping(value = "cardOperFlow/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardOperFlowAddReqVO cardOperFlowAddReqVO) {
        CardOperFlowAddReqDTO cardOperFlowAddReqDTO = converter.convertToCardOperFlowAddReqDTO(cardOperFlowAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardOperFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-批量新增(所有字段)")
    @RequestMapping(value = "cardOperFlow/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardOperFlowAddReqVO> cardOperFlowAddReqVOList) {
        List<CardOperFlowAddReqDTO> cardOperFlowAddReqDTOList = converter.convertToCardOperFlowAddReqDTOList(cardOperFlowAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardOperFlowAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardOperFlow/show", method = {RequestMethod.GET})
    public ItemResult<CardOperFlowShowResVO> show(String id) {
        CardOperFlowShowResDTO cardOperFlowShowResDTO = service.show(id);

        CardOperFlowShowResVO item = converter.convertToCardOperFlowShowResVO(cardOperFlowShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "卡操作流水-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardOperFlow/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardOperFlowShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardOperFlowShowResDTO> cardOperFlowShowResDTOList = service.showByIds(ids.getItems());

        List<CardOperFlowShowResVO> items = converter.convertToCardOperFlowShowResVOList(cardOperFlowShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "卡操作流水-更新")
    @RequestMapping(value = "cardOperFlow/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardOperFlowModifyReqVO cardOperFlowModifyReqVO) {
        CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO = converter.convertToCardOperFlowModifyReqDTO(cardOperFlowModifyReqVO);

        Boolean isSuccess = service.modify(cardOperFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-更新(所有字段)")
    @RequestMapping(value = "cardOperFlow/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardOperFlowModifyReqVO cardOperFlowModifyReqVO) {
        CardOperFlowModifyReqDTO cardOperFlowModifyReqDTO = converter.convertToCardOperFlowModifyReqDTO(cardOperFlowModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardOperFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardOperFlow/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardOperFlow/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "卡操作流水-删除(参数)")
    @RequestMapping(value = "cardOperFlow/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardOperFlowRemoveReqVO cardOperFlowRemoveReqVO) {
        CardOperFlowRemoveReqDTO cardOperFlowRemoveReqDTO = converter.convertToCardOperFlowRemoveReqDTO(cardOperFlowRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardOperFlowRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
