package com.fenbei.fx.card.web.cardoperflow.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowListResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardoperflow.dto.CardOperFlowShowResDTO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowAddReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowListReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowListResVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowModifyReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowPageReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowPageResVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowRemoveReqVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowShowResVO;
import com.fenbei.fx.card.web.cardoperflow.vo.CardOperFlowVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 卡操作流水 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardOperFlowVOConverter extends BaseVOConverter<CardOperFlowDTO, CardOperFlowVO> {

    static CardOperFlowVOConverter me() {
        return SpringUtil.getBean(CardOperFlowVOConverter.class);
    }

    CardOperFlowAddReqDTO convertToCardOperFlowAddReqDTO(CardOperFlowAddReqVO cardOperFlowAddReqVO);

    CardOperFlowShowResVO convertToCardOperFlowShowResVO(CardOperFlowShowResDTO cardOperFlowShowResDTO);

    CardOperFlowModifyReqDTO convertToCardOperFlowModifyReqDTO(CardOperFlowModifyReqVO cardOperFlowModifyReqVO);

    CardOperFlowRemoveReqDTO convertToCardOperFlowRemoveReqDTO(CardOperFlowRemoveReqVO cardOperFlowRemoveReqVO);

    CardOperFlowListReqDTO convertToCardOperFlowListReqDTO(CardOperFlowListReqVO cardOperFlowReqVO);

    CardOperFlowPageReqDTO convertToCardOperFlowPageReqDTO(CardOperFlowPageReqVO cardOperFlowPageReqVO);

    CardOperFlowListResVO convertToCardOperFlowListResVO(CardOperFlowListResDTO cardOperFlowListResDTO);

    List<CardOperFlowListResVO> convertToCardOperFlowListResVOList(List<CardOperFlowListResDTO> cardOperFlowListResDTOList);

    Page<CardOperFlowPageResVO> convertToCardOperFlowPageResVOPage(Page<CardOperFlowPageResDTO> cardOperFlowPageResDTOPage);

    List<CardOperFlowAddReqDTO> convertToCardOperFlowAddReqDTOList(List<CardOperFlowAddReqVO> cardOperFlowAddReqVOList);

    List<CardOperFlowShowResVO> convertToCardOperFlowShowResVOList(List<CardOperFlowShowResDTO> cardOperFlowShowResDTOList);
}
