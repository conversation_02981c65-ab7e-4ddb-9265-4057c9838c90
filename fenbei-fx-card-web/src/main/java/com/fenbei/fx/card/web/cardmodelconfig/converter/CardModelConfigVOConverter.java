package com.fenbei.fx.card.web.cardmodelconfig.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigListResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardmodelconfig.dto.CardModelConfigShowResDTO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigAddReqVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigListReqVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigListResVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigModifyReqVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigPageReqVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigPageResVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigRemoveReqVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigShowResVO;
import com.fenbei.fx.card.web.cardmodelconfig.vo.CardModelConfigVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡使用模式配置 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardModelConfigVOConverter extends BaseVOConverter<CardModelConfigDTO, CardModelConfigVO> {

    static CardModelConfigVOConverter me() {
        return SpringUtil.getBean(CardModelConfigVOConverter.class);
    }

    CardModelConfigAddReqDTO convertToCardModelConfigAddReqDTO(CardModelConfigAddReqVO cardModelConfigAddReqVO);

    CardModelConfigShowResVO convertToCardModelConfigShowResVO(CardModelConfigShowResDTO cardModelConfigShowResDTO);

    CardModelConfigModifyReqDTO convertToCardModelConfigModifyReqDTO(CardModelConfigModifyReqVO cardModelConfigModifyReqVO);

    CardModelConfigRemoveReqDTO convertToCardModelConfigRemoveReqDTO(CardModelConfigRemoveReqVO cardModelConfigRemoveReqVO);

    CardModelConfigListReqDTO convertToCardModelConfigListReqDTO(CardModelConfigListReqVO cardModelConfigReqVO);

    CardModelConfigPageReqDTO convertToCardModelConfigPageReqDTO(CardModelConfigPageReqVO cardModelConfigPageReqVO);

    CardModelConfigListResVO convertToCardModelConfigListResVO(CardModelConfigListResDTO cardModelConfigListResDTO);

    List<CardModelConfigListResVO> convertToCardModelConfigListResVOList(List<CardModelConfigListResDTO> cardModelConfigListResDTOList);

    Page<CardModelConfigPageResVO> convertToCardModelConfigPageResVOPage(Page<CardModelConfigPageResDTO> cardModelConfigPageResDTOPage);

    List<CardModelConfigAddReqDTO> convertToCardModelConfigAddReqDTOList(List<CardModelConfigAddReqVO> cardModelConfigAddReqVOList);

    List<CardModelConfigShowResVO> convertToCardModelConfigShowResVOList(List<CardModelConfigShowResDTO> cardModelConfigShowResDTOList);
}
