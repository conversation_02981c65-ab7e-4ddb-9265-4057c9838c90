package com.fenbei.fx.card.web.card.controller.web;

import com.fenbei.fx.card.service.cardorder.manager.CardOrderManager;
import com.fenbei.fx.card.service.usercard.UserCardCreditManager;
import com.fenbei.fx.card.service.usercard.dto.FxWrongPaidRefundDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardCreditGrantRecordDTO;
import com.fenbei.fx.card.web.card.vo.FxWrongPaidReturnReqVO;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.ItemR;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-20
 */
@Api(tags = {"用户国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/web/usercard" )
public class UserCardCreditWebController extends ControllerSupport {


    /**
     * 关联额度申请单
     * @return ItemR<UserCardCreditGrantRecordDTO>
     */
    @RequestMapping(value = "/credit/refund/relation", method = {RequestMethod.GET})
    public ItemR<UserCardCreditGrantRecordDTO> userCards(@RequestParam("fxCardId")String fxCardId) {
        if (StringUtils.isEmpty(fxCardId)){
            return responseItemR(ResponseCodeEnum.SUCCESS, new UserCardCreditGrantRecordDTO());
        }
        UserCardCreditGrantRecordDTO userCardCreditGrantRecordDTO = UserCardCreditManager.me().relationApplyRecord(fxCardId);
        return responseItemR(ResponseCodeEnum.SUCCESS, userCardCreditGrantRecordDTO);
    }

    /**
     * 错花还款
     * @return ItemR<UserCardCreditGrantRecordDTO>
     */
    @RequestMapping(value = "/wrong/paid/refund", method = {RequestMethod.POST})
    public ItemR<FxWrongPaidRefundDTO> wrongPaidRefund(@RequestBody FxWrongPaidReturnReqVO fxWrongPaidReturnReqVO) {
        FxWrongPaidRefundDTO fxWrongPaidRefundDTO = new FxWrongPaidRefundDTO();
        if (fxWrongPaidReturnReqVO == null
            || StringUtils.isEmpty(fxWrongPaidReturnReqVO.getOrderId())
            || fxWrongPaidReturnReqVO.getAmount() == null
            || fxWrongPaidReturnReqVO.getOrderType() == null
        ){
            fxWrongPaidRefundDTO.setRefundResult(false);
            fxWrongPaidRefundDTO.setFailReason("请求参数有误");
            return responseItemR(ResponseCodeEnum.INTERNAL_ERROR, fxWrongPaidRefundDTO);
        }
        fxWrongPaidRefundDTO = CardOrderManager.me().wrongPaidRefund(fxWrongPaidReturnReqVO.getOrderId(),fxWrongPaidReturnReqVO.getOrderType(),fxWrongPaidReturnReqVO.getAmount());
        return responseItemR(ResponseCodeEnum.SUCCESS, fxWrongPaidRefundDTO);
    }
}
