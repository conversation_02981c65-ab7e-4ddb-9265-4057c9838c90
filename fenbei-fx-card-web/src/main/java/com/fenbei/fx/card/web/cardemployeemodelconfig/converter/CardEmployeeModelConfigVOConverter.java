package com.fenbei.fx.card.web.cardemployeemodelconfig.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigAddReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigListReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigListResVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigModifyReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigPageReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigPageResVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigRemoveReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigShowResVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡员工使用模式配置 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardEmployeeModelConfigVOConverter extends BaseVOConverter<CardEmployeeModelConfigDTO, CardEmployeeModelConfigVO> {

    static CardEmployeeModelConfigVOConverter me() {
        return SpringUtil.getBean(CardEmployeeModelConfigVOConverter.class);
    }

    CardEmployeeModelConfigAddReqDTO convertToCardEmployeeModelConfigAddReqDTO(CardEmployeeModelConfigAddReqVO cardEmployeeModelConfigAddReqVO);

    CardEmployeeModelConfigShowResVO convertToCardEmployeeModelConfigShowResVO(CardEmployeeModelConfigShowResDTO cardEmployeeModelConfigShowResDTO);

    CardEmployeeModelConfigModifyReqDTO convertToCardEmployeeModelConfigModifyReqDTO(CardEmployeeModelConfigModifyReqVO cardEmployeeModelConfigModifyReqVO);

    CardEmployeeModelConfigRemoveReqDTO convertToCardEmployeeModelConfigRemoveReqDTO(CardEmployeeModelConfigRemoveReqVO cardEmployeeModelConfigRemoveReqVO);

    CardEmployeeModelConfigListReqDTO convertToCardEmployeeModelConfigListReqDTO(CardEmployeeModelConfigListReqVO cardEmployeeModelConfigReqVO);

    CardEmployeeModelConfigPageReqDTO convertToCardEmployeeModelConfigPageReqDTO(CardEmployeeModelConfigPageReqVO cardEmployeeModelConfigPageReqVO);

    CardEmployeeModelConfigListResVO convertToCardEmployeeModelConfigListResVO(CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO);

    List<CardEmployeeModelConfigListResVO> convertToCardEmployeeModelConfigListResVOList(List<CardEmployeeModelConfigListResDTO> cardEmployeeModelConfigListResDTOList);

    Page<CardEmployeeModelConfigPageResVO> convertToCardEmployeeModelConfigPageResVOPage(Page<CardEmployeeModelConfigPageResDTO> cardEmployeeModelConfigPageResDTOPage);

    List<CardEmployeeModelConfigAddReqDTO> convertToCardEmployeeModelConfigAddReqDTOList(List<CardEmployeeModelConfigAddReqVO> cardEmployeeModelConfigAddReqVOList);

    List<CardEmployeeModelConfigShowResVO> convertToCardEmployeeModelConfigShowResVOList(List<CardEmployeeModelConfigShowResDTO> cardEmployeeModelConfigShowResDTOList);
}
