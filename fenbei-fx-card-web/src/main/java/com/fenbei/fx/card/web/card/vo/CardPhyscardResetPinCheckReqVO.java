package com.fenbei.fx.card.web.card.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 卡激活-修改密码 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-07 14:48:23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPhyscardResetPinCheckReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;
    /**
     * 卡ID
     */
    @NotBlank(message = "卡号不能为空")
    private String fxCardId;


    /**
     * 设置密码
     */
    @NotBlank(message = "旧密码不能为空")
    @Length(min=4,max=4,message = "旧密码应为4位数字")
    private String oldPin;


    /**
     * 设置密码
     */
    @NotBlank(message = "新密码不能为空")
    @Length(min=4,max=4,message = "新密码应为4位数字")
    private String newPin;


}



