package com.fenbei.fx.card.web.card.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 卡激活-获取验证码 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-07 14:48:23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCaptchaApplyReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;
    /**
     * 卡ID
     */
    @NotBlank(message = "卡号不能为空")
    private String fxCardId;


    /**
     *  重置实体卡密码: reset_card_pin
     *  实体卡激活   : physcard_active
     */
    @NotBlank(message = "验证码业务类型为空")
    private String busiType;

    /**
     * 不传默认MOBILE
     * 手机号:MOBILE
     * 邮件: EMAIL
     */
    @NotBlank(message = "验证码接受类型为空")
    private String sendType="MOBILE";

}



