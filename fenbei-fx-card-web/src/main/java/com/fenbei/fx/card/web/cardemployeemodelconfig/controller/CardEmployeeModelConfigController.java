package com.fenbei.fx.card.web.cardemployeemodelconfig.controller;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.cardemployeemodelconfig.CardEmployeeModelConfigService;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigAddReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigListResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigModifyReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigPageResDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigRemoveReqDTO;
import com.fenbei.fx.card.service.cardemployeemodelconfig.dto.CardEmployeeModelConfigShowResDTO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.converter.CardEmployeeModelConfigVOConverter;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigAddReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigListReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigListResVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigModifyReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigPageReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigPageResVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigRemoveReqVO;
import com.fenbei.fx.card.web.cardemployeemodelconfig.vo.CardEmployeeModelConfigShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 国际卡员工使用模式配置 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Api(tags = {"国际卡员工使用模式配置 API 接口"})
@Slf4j
@Validated
@RestController
public class CardEmployeeModelConfigController extends BaseController<CardEmployeeModelConfigService, CardEmployeeModelConfigVOConverter> {

    @ApiOperation(value = "国际卡员工使用模式配置-列表")
    @RequestMapping(value = "cardEmployeeModelConfig/list", method = {RequestMethod.GET})
    public ItemsResult<CardEmployeeModelConfigListResVO> list(CardEmployeeModelConfigListReqVO cardEmployeeModelConfigListReqVO) {
        CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = converter.convertToCardEmployeeModelConfigListReqDTO(cardEmployeeModelConfigListReqVO);

        List<CardEmployeeModelConfigListResDTO> cardEmployeeModelConfigListResDTOList = service.list(cardEmployeeModelConfigListReqDTO);
        List<CardEmployeeModelConfigListResVO> items = converter.convertToCardEmployeeModelConfigListResVOList(cardEmployeeModelConfigListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-First查询")
    @RequestMapping(value = "cardEmployeeModelConfig/listOne", method = {RequestMethod.GET})
    public ItemResult<CardEmployeeModelConfigListResVO> listOne(CardEmployeeModelConfigListReqVO cardEmployeeModelConfigListReqVO) {
        CardEmployeeModelConfigListReqDTO cardEmployeeModelConfigListReqDTO = converter.convertToCardEmployeeModelConfigListReqDTO(cardEmployeeModelConfigListReqVO);

        CardEmployeeModelConfigListResDTO cardEmployeeModelConfigListResDTO = service.listOne(cardEmployeeModelConfigListReqDTO);
        CardEmployeeModelConfigListResVO item = converter.convertToCardEmployeeModelConfigListResVO(cardEmployeeModelConfigListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardEmployeeModelConfig/page", method = {RequestMethod.GET})
    public PageResult<CardEmployeeModelConfigPageResVO> page(CardEmployeeModelConfigPageReqVO cardEmployeeModelConfigPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardEmployeeModelConfigPageReqDTO cardEmployeeModelConfigPageReqDTO = converter.convertToCardEmployeeModelConfigPageReqDTO(cardEmployeeModelConfigPageReqVO);

        Page<CardEmployeeModelConfigPageResDTO> cardEmployeeModelConfigPageResDTOPage = service.pagination(cardEmployeeModelConfigPageReqDTO, current, size);
        Page<CardEmployeeModelConfigPageResVO> cardEmployeeModelConfigPageResVOPage = converter.convertToCardEmployeeModelConfigPageResVOPage(cardEmployeeModelConfigPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardEmployeeModelConfigPageResVOPage.getTotal(), cardEmployeeModelConfigPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-新增")
    @RequestMapping(value = "cardEmployeeModelConfig/add", method = {RequestMethod.POST})
    public MessageResult add(CardEmployeeModelConfigAddReqVO cardEmployeeModelConfigAddReqVO) {
        CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO = converter.convertToCardEmployeeModelConfigAddReqDTO(cardEmployeeModelConfigAddReqVO);

        Boolean isSuccess = service.add(cardEmployeeModelConfigAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-新增(所有字段)")
    @RequestMapping(value = "cardEmployeeModelConfig/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardEmployeeModelConfigAddReqVO cardEmployeeModelConfigAddReqVO) {
        CardEmployeeModelConfigAddReqDTO cardEmployeeModelConfigAddReqDTO = converter.convertToCardEmployeeModelConfigAddReqDTO(cardEmployeeModelConfigAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardEmployeeModelConfigAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-批量新增(所有字段)")
    @RequestMapping(value = "cardEmployeeModelConfig/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardEmployeeModelConfigAddReqVO> cardEmployeeModelConfigAddReqVOList) {
        List<CardEmployeeModelConfigAddReqDTO> cardEmployeeModelConfigAddReqDTOList = converter.convertToCardEmployeeModelConfigAddReqDTOList(cardEmployeeModelConfigAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardEmployeeModelConfigAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardEmployeeModelConfig/show", method = {RequestMethod.GET})
    public ItemResult<CardEmployeeModelConfigShowResVO> show(String id) {
        CardEmployeeModelConfigShowResDTO cardEmployeeModelConfigShowResDTO = service.show(id);

        CardEmployeeModelConfigShowResVO item = converter.convertToCardEmployeeModelConfigShowResVO(cardEmployeeModelConfigShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardEmployeeModelConfig/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardEmployeeModelConfigShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardEmployeeModelConfigShowResDTO> cardEmployeeModelConfigShowResDTOList = service.showByIds(ids.getItems());

        List<CardEmployeeModelConfigShowResVO> items = converter.convertToCardEmployeeModelConfigShowResVOList(cardEmployeeModelConfigShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-更新")
    @RequestMapping(value = "cardEmployeeModelConfig/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardEmployeeModelConfigModifyReqVO cardEmployeeModelConfigModifyReqVO) {
        CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO = converter.convertToCardEmployeeModelConfigModifyReqDTO(cardEmployeeModelConfigModifyReqVO);

        Boolean isSuccess = service.modify(cardEmployeeModelConfigModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-更新(所有字段)")
    @RequestMapping(value = "cardEmployeeModelConfig/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardEmployeeModelConfigModifyReqVO cardEmployeeModelConfigModifyReqVO) {
        CardEmployeeModelConfigModifyReqDTO cardEmployeeModelConfigModifyReqDTO = converter.convertToCardEmployeeModelConfigModifyReqDTO(cardEmployeeModelConfigModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardEmployeeModelConfigModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardEmployeeModelConfig/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardEmployeeModelConfig/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡员工使用模式配置-删除(参数)")
    @RequestMapping(value = "cardEmployeeModelConfig/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardEmployeeModelConfigRemoveReqVO cardEmployeeModelConfigRemoveReqVO) {
        CardEmployeeModelConfigRemoveReqDTO cardEmployeeModelConfigRemoveReqDTO = converter.convertToCardEmployeeModelConfigRemoveReqDTO(cardEmployeeModelConfigRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardEmployeeModelConfigRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
