package com.fenbei.fx.card.web.cardorder.controller;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.service.remote.dto.BudgetCostAttributionDTO;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbei.fx.card.web.cardorder.converter.CardOrderVOConverter;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderModifyReqVO;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCompanyCardAcctChangeMsg;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.MessageResult;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡订单 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/internal/cardorder" )
public class CardOrderInternalController extends BaseController<CardOrderService, CardOrderVOConverter> {
    /**
     * 调整汇率
     *
     * @param cardOrderModifyReqVO
     * @return
     * @undone
     */
    @RequestMapping(value = "updateExchangeRate", method = {RequestMethod.POST})
    public MessageResult list(@RequestBody CardOrderModifyReqVO cardOrderModifyReqVO) {
        if(Objects.isNull(cardOrderModifyReqVO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        service.updateExchangeRate(String.valueOf(cardOrderModifyReqVO.getId()), cardOrderModifyReqVO.getTradeCnyExchangeRate());

        return responseMessage(ResponseCodeEnum.SUCCESS);
    }


    /**
     * 调整订单展示状态
     */
    @RequestMapping(value = "updateOrderShow", method = {RequestMethod.POST})
    public MessageResult updateOrderShow(@RequestBody CardOrderModifyReqVO cardOrderModifyReqVO) {
        if(Objects.isNull(cardOrderModifyReqVO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        service.updateOrderShow(String.valueOf(cardOrderModifyReqVO.getId()));

        return responseMessage(ResponseCodeEnum.SUCCESS);
    }


    /**
     * 调整订单展示状态
     */
    @RequestMapping(value = "apply/retry", method = {RequestMethod.POST})
    public MessageResult applyRetry(@RequestBody CardCreditManagerApplyReqDTO cardCreditManagerApplyReqDTO) {
        if(Objects.isNull(cardCreditManagerApplyReqDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        KafkaCompanyCardAcctChangeMsg kafkaCompanyCardAcctChangeMsg = new KafkaCompanyCardAcctChangeMsg();
        kafkaCompanyCardAcctChangeMsg.setCompanyId(cardCreditManagerApplyReqDTO.getCompanyId());
        kafkaCompanyCardAcctChangeMsg.setBankName(cardCreditManagerApplyReqDTO.getBankName());
        FinhubLogger.info("海外卡额度申请：{}", JsonUtils.toJson(cardCreditManagerApplyReqDTO));
        CardCreditManagerManager.me().applyRetry(kafkaCompanyCardAcctChangeMsg);
        return responseMessage(ResponseCodeEnum.SUCCESS);
    }

    /**
     * 更新连连订单
     */
    @RequestMapping(value = "order/update", method = {RequestMethod.POST})
    public MessageResult lianlianPhysicalCardOrder() {
        CardApplyManager.me().queryOrder();
        return responseMessage(ResponseCodeEnum.SUCCESS);
    }


    /**
     * 调整订单展示费率
     */
    @RequestMapping(value = "updateRate", method = {RequestMethod.POST})
    public MessageResult updateRate(@RequestBody CardOrderModifyReqVO cardOrderModifyReqVO) {
        if(Objects.isNull(cardOrderModifyReqVO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        service.updateHistoryValue(String.valueOf(cardOrderModifyReqVO.getId()));

        return responseMessage(ResponseCodeEnum.SUCCESS);
    }

}

