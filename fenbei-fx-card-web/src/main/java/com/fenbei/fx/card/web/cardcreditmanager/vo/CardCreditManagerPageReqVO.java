package com.fenbei.fx.card.web.cardcreditmanager.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
      import java.util.Date;

/**
 * 国际卡额度申请退回管理表 分页 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@ApiModel("国际卡额度申请退回管理表 分页 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditManagerPageReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "xxx", value = "id")
    private Long id;

    /**
     * 卡id
     */
    @ApiModelProperty(example = "xxx", value = "卡id")
    private String fxCardId;

    /**
     * 银行卡id
     */
    @ApiModelProperty(example = "xxx", value = "银行卡id")
    private String bankCardId;

    /**
     * 银行卡编号
     */
    @ApiModelProperty(example = "xxx", value = "银行卡编号")
    private String bankCardNo;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "xxx", value = "公司id")
    private String companyId;

    /**
     * 员工id
     */
    @ApiModelProperty(example = "xxx", value = "员工id")
    private String employeeId;

    /**
     * 公司账户id
     */
    @ApiModelProperty(example = "xxx", value = "公司账户id")
    private String companyAccountId;

    /**
     * 类型: 1额度申请,2额度退回
     */
    @ApiModelProperty(example = "xxx", value = "类型: 1额度申请,2额度退回")
    private Integer applyType;

    /**
     * 状态: 1成功,0失败
     */
    @ApiModelProperty(example = "xxx", value = "状态: 1成功,0失败")
    private Integer applyStatus;

    /**
     * 申请单ID
     */
    @ApiModelProperty(example = "xxx", value = "申请单ID")
    private String applyTransNo;

    /**
     * 申请单批次: 同一申请单可追加
     */
    @ApiModelProperty(example = "xxx", value = "申请单批次: 同一申请单可追加")
    private String applyTransBatchNo;

    /**
     * 额度退回时关联申请单ID
     */
    @ApiModelProperty(example = "xxx", value = "额度退回时关联申请单ID")
    private String oriApplyTransNo;

    /**
     * 申请事由
     */
    @ApiModelProperty(example = "xxx", value = "申请事由")
    private String applyReason;

    /**
     * 申请标题
     */
    @ApiModelProperty(example = "xxx", value = "申请标题")
    private String applyTitle;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "xxx", value = "币种 美元-USD")
    private String currency;

    /**
     * 申请金额
     */
    @ApiModelProperty(example = "xxx", value = "申请金额")
    private BigDecimal amount;

    /**
     * 已退还金额
     */
    @ApiModelProperty(example = "xxx", value = "已退还金额")
    private BigDecimal returnedAmount;

    /**
     * 已核销金额
     */
    @ApiModelProperty(example = "xxx", value = "已核销金额")
    private BigDecimal writenOffAmount;

    /**
     * 核销中金额
     */
    @ApiModelProperty(example = "xxx", value = "核销中金额")
    private BigDecimal writingOffAmount;

    /**
     * 无需核销金额
     */
    @ApiModelProperty(example = "xxx", value = "无需核销金额")
    private BigDecimal unwriteOffAmount;

    /**
     * 可用余额
     */
    @ApiModelProperty(example = "xxx", value = "可用余额")
    private BigDecimal avalibleAmount;

    /**
     * 卡模式: 1.普通模式,2备用金模式
     */
    @ApiModelProperty(example = "xxx", value = "卡模式: 1.普通模式,2备用金模式")
    private Integer cardModel;

    /**
     * 开卡渠道
     */
    @ApiModelProperty(example = "xxx", value = "开卡渠道")
    private String cardPlatform;

    /**
     * 发起人
     */
    @ApiModelProperty(example = "xxx", value = "发起人")
    private String operationUserId;

    /**
     * 申请人部门
     */
    @ApiModelProperty(example = "xxx", value = "申请人部门")
    private String operationUserDept;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "xxx", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

    /**
     * 费用类别
     */
    @ApiModelProperty(example = "xxx", value = "费用类别")
    private String costType;

    /**
     * 费用归属
     */
    @ApiModelProperty(example = "xxx", value = "费用归属")
    private String costAttribution;

}
