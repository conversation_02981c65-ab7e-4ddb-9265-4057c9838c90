package com.fenbei.fx.card.web.cardorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 国际卡订单 VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@ApiModel("国际卡订单 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOrderVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "xxx", value = "id")
    private Long id;

    /**
     * 卡ID
     */
    @ApiModelProperty(example = "xxx", value = "卡ID")
    private String fxCardId;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "xxx", value = "公司id")
    private String companyId;

    /**
     * 员工id
     */
    @ApiModelProperty(example = "xxx", value = "员工id")
    private String employeeId;

    /**
     * 交易单号
     */
    @ApiModelProperty(example = "xxx", value = "交易单号")
    private String bizNo;

    /**
     * 退款时代表原业务单号
     */
    @ApiModelProperty(example = "xxx", value = "退款时代表原业务单号")
    private String oriBizNo;

    /**
     * 11消费,12退款
     */
    @ApiModelProperty(example = "xxx", value = "11消费,12退款")
    private Integer type;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "xxx", value = "币种 美元-USD")
    private String tradeCurrency;

    /**
     * 操作金额 单位：分
     */
    @ApiModelProperty(example = "xxx", value = "操作金额 单位：分")
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    @ApiModelProperty(example = "xxx", value = "交易名")
    private String tradeName;

    /**
     * 交易时间
     */
    @ApiModelProperty(example = "xxx", value = "交易时间")
    private Date tradeTime;

    /**
     * 交易地
     */
    @ApiModelProperty(example = "xxx", value = "交易地")
    private String tradeAddress;

    /**
     * 交易备注
     */
    @ApiModelProperty(example = "xxx", value = "交易备注")
    private String tradeRemark;

    /**
     * 核销状态 0未核销 1已核销
     */
    @ApiModelProperty(example = "xxx", value = "核销状态 0未核销 1已核销")
    private Integer tradeWriteOffStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

}
