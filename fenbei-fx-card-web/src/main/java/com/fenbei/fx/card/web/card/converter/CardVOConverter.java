package com.fenbei.fx.card.web.card.converter;

import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.usercard.dto.UserCardCreditGrantListQueryDTO;
import com.fenbei.fx.card.web.bankcardflow.vo.UserCardCreditGrantListReqVO;
import com.fenbei.fx.card.web.card.vo.*;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardVOConverter extends BaseVOConverter<CardDTO, CardVO> {

    static CardVOConverter me() {
        return SpringUtil.getBean(CardVOConverter.class);
    }

    CardAddReqDTO convertToCardAddReqDTO(CardAddReqVO cardAddReqVO);

    CardShowResVO convertToCardShowResVO(CardShowResDTO cardShowResDTO);

    CardModifyReqDTO convertToCardModifyReqDTO(CardModifyReqVO cardModifyReqVO);

    CardRemoveReqDTO convertToCardRemoveReqDTO(CardRemoveReqVO cardRemoveReqVO);

    CardListReqDTO convertToCardListReqDTO(CardListReqVO cardReqVO);

    CardPageReqDTO convertToCardPageReqDTO(CardPageReqVO cardPageReqVO);



    CardListResVO convertToCardListResVO(CardListResDTO cardListResDTO);

    List<CardListResVO> convertToCardListResVOList(List<CardListResDTO> cardListResDTOList);

    Page<CardPageResVO> convertToCardPageResVOPage(Page<CardPageResDTO> cardPageResDTOPage);

    List<CardAddReqDTO> convertToCardAddReqDTOList(List<CardAddReqVO> cardAddReqVOList);

    List<CardShowResVO> convertToCardShowResVOList(List<CardShowResDTO> cardShowResDTOList);

    UpdateCardStatusReqDTO convertToUpdateCardStatusReqDTO(UpdateCardStatusReqVO updateCardStatusReqVO);

    ActivateCardReqDTO convertToActivateCardReqDTO(ActivateCardReqVO activateCardReqVO);

    UserCardCreditGrantListQueryDTO convertGrantListQueryDTO(UserCardCreditGrantListReqVO userCardCreditGrantListReqVO);


    CardCaptchaApplyReqDTO convertToCardCaptchaApplyReqDTO(CardCaptchaApplyReqVO cardCaptchaApplyReqVO);

    CardCaptchaApplyResVO convertToCardCaptchaApplyResVO(CardCaptchaApplyResDTO cardCaptchaApplyResDTO);


    CardPhyscardActiveReqDTO convertToCardPhyscardActiveReqDTO(CardPhyscardActiveReqVO cardPhyscardActiveReqVO);

    CardPhyscardResetPinCheckReqDTO convertToCardPhyscardResetPinCheckReqDTO(CardPhyscardResetPinCheckReqVO resetPinCheckReqVO);

    CardPhyscardResetPinReqDTO convertToCardPhyscardResetPinReqVO(CardPhyscardResetPinReqVO cardPhyscardResetPinReqVO);
}
