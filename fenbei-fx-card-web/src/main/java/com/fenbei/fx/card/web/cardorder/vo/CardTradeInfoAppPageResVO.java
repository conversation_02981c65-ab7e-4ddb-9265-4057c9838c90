package com.fenbei.fx.card.web.cardorder.vo;

import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.fenbei.fx.card.service.usercard.dto.TotalPrice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 交易记录 App 分页 ResVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardTradeInfoAppPageResVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    private String id;

    /** 分贝订单号 */
    private String orderId;
    /** 0 未关联新的交易记录 1 关联了新的交易记录*/
    private String isNew;
    /** 交易商户名 */
    private String shopName;
    /** II类账户银行卡号 */
    private String bankAccountNo;
    /**
     * "bankAccountNo": "**** 7817"
     * 示例：**** 7817
     */
    private String bankAccountNoMasked;
    /** 折算金额,美元 */
    private TotalPrice totalPrice;
    /** 实际交易金额 */
    private TotalPrice tradePrice;
    /** 用户是否标记 0 否  1是 */
    private KeyValueVO checkStatus;
    /** 交易类型 1消费  2退款 */
    private KeyValueVO transactionType;
    /** 创建时间 2018-12-29 16:52:22 */
    private String createTime;
    /** 创建时间 2022/04/11 16:52:22 */
    private String createTimeShow;
    /** 银行名称 */
    private String bankName;
    /** 银行中文名称 */
    private String bankNameString;
    /** 月份标识 2019年10月 */
    private String monthType;
    /** 银行+卡号后四位缩写 */
    private String bankDesc;
    /** 交易记录备注 */
    private String remarks;
    /** 备用金ID */
    private String pettyId;
    /** 2022.8.10日 增加 事由*/
    private String applyReason;
    /** 备用金名称 */
    private String title;
    /** 备用金名称 */
    private String pettyName;
    /** 审批单号 */
    private String bizNo;
    /** 是否存在关联交易 4.7.1添加 */
    private boolean hasRelate;
    /** 4.22日 增加 */
    private String pettyCreateTime;
    private BigDecimal applyAmount;
    //是否隐藏还款入口
    private boolean hasPayBackEntry;
    /** 是否创建费用: 1 未创建费用     2 已创建费用 */
    private KeyValueVO bankBindStatus;
    /** 父级交易编号 */
    private String fbOrderId;
    /** 平安银行虚拟卡交易记录联动 新增字段*/
    private String rootOrderId;
    /** 还款状态 0无还款 1已还款 2 部分还款*/
    private KeyValueVO payBackStatus;
    /** 已还款金额 */
    private TotalPrice payBackPrice;
    /** 退款状态 0无退款，1已退款，2已退款   （1部分退款，2全额退款,key不一样，value都是已退款） */
    private KeyValueVO refundStatus;
    /** 退款金额 */
    private TotalPrice refundPrice;
    /**支付方式  */
    private String payMethod;
    /**
     * 折算币种
     */
    private String billCurrencyCode;
    /**
     * 交易币种
     */
    private String currencyCode;

    /**
     * 人民币
     */
    private TotalPrice cnyTradePrice;
//
//    /**
//     * 交易记录 id
//     * 示例：*********
//     */
//    private String transactionId;
//
//    /**
//     * 持卡人
//     * 示例：张三
//     */
//    private String cardHolderName;
//
//    /**
//     * "bankAccountNo": "**** 7817", //卡号：masked_card_number,
//     * 示例：**** 7817
//     */
//    private String maskedCardNumber;
//
//    /**
//     * 卡片类型
//     * 示例：VISA
//     */
//    private String bankCardType;
//
//    /**
//     * "shopName": "中国银联无卡快捷支付业务二级商户信息测试",
//     * 商户名称：对应库表merchant_name
//     * 示例：中国银联无卡快捷支付业务二级商户信息测试
//     */
//    private String merchantName;
//
//    /**
//     * 消费类型 1-预定 4-预定释放 5-交易失败 11-消费 12-退款
//     * 示例：消费
//     */
//    private String transactionType;
//
//    /**
//     * 消费类型编码 1-预定 4-预定释放 5-交易失败 11-消费 12-退款
//     * 示例：1
//     */
//    private Integer transactionTypeCode;
//
//    /**
//     * 交易时间
//     * 示例：2021-05-20 12:00:00
//     */
//    private Date transactionDate;
//
//    /**
//     * 交易币种
//     * 示例：USD
//     */
//    private String transactionCurrency;
//
//    /**
//     * 交易金额
//     * 示例：100.00
//     */
//    private BigDecimal transactionAmount;
//
//    /**
//     * 交易金额描述
//     */
//    private TotalPrice transactionAmountDesc;
//
//    /**
//     * 交易地 "tradeAddress":"USA-NEW",
//     * 示例：USA-NEW
//     */
//    private String transactionAddress;
//
//    /**
//     * 折算币种
//     */
//    private String obversionCurrType;
//
//    /**
//     * 折算金额
//     */
//    private BigDecimal obversionTotalPrice;
//
//    /**
//     * 核销状态 0-无核销状态（如退款单，已全额退款的正向单） 2-未核销 3-核销中 4-已核销
//     * 示例：未核销
//     */
//    private String checkStatus;
//
//    /**
//     * 核销状态编码
//     * 示例：0-无核销状态（如退款单，已全额退款的正向单） 2-未核销 3-核销中 4-已核销
//     */
//    private Integer checkStatusCode;
//
//    /**
//     * "uncheckConsume": 0, // 未核销金额
//     */
//    private BigDecimal uncheckConsume;
//
//    /**
//     * 未核销金额描述
//     */
//    private TotalPrice uncheckConsumeDesc;
//
//    /**
//     * "unNeedCheckConsume": 0, // 无需核销金额
//     */
//    private BigDecimal unNeedCheckConsume;
//
//    /**
//     * 无需核销金额描述
//     */
//    private TotalPrice unNeedCheckConsumeDesc;
//
//    /**
//     * 交易备注
//     */
//    private String remark;

    private BigDecimal tradeCnyExchangeRate ;


}
