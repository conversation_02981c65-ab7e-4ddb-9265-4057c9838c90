package com.fenbei.fx.card.web.controller.common;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.PurposeEnum;
import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.ItemResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by FBT on 2023/4/23.
 */

@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/common/" )
public class FxBaseController extends ControllerSupport{

    @RequestMapping(value = "/queryPurpose", method = {RequestMethod.GET})
    public ItemResult<List<KeyValueVO>> queryPurpose(String platform) {
        List<KeyValueVO> keyValueVOList = PurposeEnum.getKeyValueInfo(platform);
        return responseItem(GlobalCoreResponseCode.SUCCESS, keyValueVOList);
    }

}
