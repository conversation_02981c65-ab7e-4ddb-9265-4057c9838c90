package com.fenbei.fx.card.web.card.controller.app;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.exception.FxCardException;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.usercard.UserCardHomePageManager;
import com.fenbei.fx.card.service.usercard.dto.UserCardHomePageDTO;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.web.card.converter.CardVOConverter;
import com.fenbei.fx.card.web.card.vo.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemR;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡 Controller
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"国际卡API接口卡实体卡激活与密码相关"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/app/card/v1")
public class CardAppV1Controller extends BaseController<CardService, CardVOConverter> {


    @ApiOperation(value = "卡激活-获取验证码")
    @Validated
    @RequestMapping(value = "/captcha/apply", method = {RequestMethod.POST})
    public ItemResult<CardCaptchaApplyResVO> captchaApply(@RequestBody @Valid CardCaptchaApplyReqVO cardCaptchaApplyReqVO) {
        CardCaptchaApplyReqDTO cardCaptchaApplyReqDTO = converter.convertToCardCaptchaApplyReqDTO(cardCaptchaApplyReqVO);
        CardCaptchaApplyResDTO cardCaptchaApplyResDTO  = service.captchaApply(cardCaptchaApplyReqDTO);
        CardCaptchaApplyResVO cardCaptchaApplyResVO = converter.convertToCardCaptchaApplyResVO(cardCaptchaApplyResDTO);
        return responseItem(GlobalCoreResponseCode.SUCCESS, cardCaptchaApplyResVO);
    }

    @ApiOperation(value = "卡激活-激活")
    @Validated
    @RequestMapping(value = "/physcard/active", method = {RequestMethod.POST})
    public ItemResult<CardPhyscardActiveResDTO> physcardActivate(@RequestBody @Valid  CardPhyscardActiveReqVO cardPhyscardActiveReqVO) {
        CardPhyscardActiveReqDTO cardPhyscardActiveReqDTO = converter.convertToCardPhyscardActiveReqDTO(cardPhyscardActiveReqVO);
        CardPhyscardActiveResDTO activeResDTO=new CardPhyscardActiveResDTO();
        activeResDTO.setFxCardId(cardPhyscardActiveReqVO.getFxCardId());
        boolean physcardActivate=false;
        String failedMessage = GlobalCoreResponseCode.CARD_PHYSICAL_ACTIVE_FAILED.getMessage();

        String message="激活成功，已可以使用实体卡进行消费";
        try {
            physcardActivate = service.physcardActivate(cardPhyscardActiveReqDTO);
            if(physcardActivate){
                activeResDTO.setMessage(message);
            }
        } catch(FxCardException cardException){
            message = MessageFormat.format(failedMessage, cardException.getMessage());
            activeResDTO.setMessage(message);
        } catch (Exception e) {
            message = MessageFormat.format(failedMessage, e.getMessage());
            activeResDTO.setMessage(message);
        }
        activeResDTO.setActiveStatus(physcardActivate);
        return responseItem(GlobalCoreResponseCode.SUCCESS , activeResDTO);
    }


    @ApiOperation(value = "卡密码修改-检查")
    @Validated
    @RequestMapping(value = "/physcard/resetPin/check", method = {RequestMethod.POST})
    public ItemResult<CardPhyscardResetPinCheckResDTO> physcardResetPinCheck(@RequestBody @Valid  CardPhyscardResetPinCheckReqVO resetPinCheckReqVO) {
        CardPhyscardResetPinCheckReqDTO physcardResetPinCheckDTO = converter.convertToCardPhyscardResetPinCheckReqDTO(resetPinCheckReqVO);
        CardPhyscardResetPinCheckResDTO checkResDTO = new CardPhyscardResetPinCheckResDTO();
        checkResDTO.setFxCardId(resetPinCheckReqVO.getFxCardId());
        boolean isSuccess = false;
        try {
            isSuccess = service.physcardResetPinCheck(physcardResetPinCheckDTO);
        } catch(FxCardException cardException){
            checkResDTO.setMessage(cardException.getMessage());
        } catch (Exception e) {
            checkResDTO.setMessage(e.getMessage());
        }
        checkResDTO.setCheckStatus(isSuccess);
        return  responseItem(GlobalCoreResponseCode.SUCCESS , checkResDTO);
    }

    @ApiOperation(value = "卡密码修改")
    @Validated
    @RequestMapping(value = "/physcard/resetPin", method = {RequestMethod.POST})
    public ItemResult<CardPhyscardResetPinResDTO> physcardResetPin(@RequestBody  @Valid   CardPhyscardResetPinReqVO cardPhyscardResetPinReqVO) {
        CardPhyscardResetPinReqDTO physcardResetPinReqDTO = converter.convertToCardPhyscardResetPinReqVO(cardPhyscardResetPinReqVO);
        CardPhyscardResetPinResDTO checkResDTO = new CardPhyscardResetPinResDTO();
        checkResDTO.setFxCardId(cardPhyscardResetPinReqVO.getFxCardId());
        boolean isSuccess = false;
        String failedMessage = GlobalCoreResponseCode.CARD_PHYSICAL_NEWPIN_FAILED.getMessage();
        String message="新密码：\t"+cardPhyscardResetPinReqVO.getNewPin()+"\n 您已更换新密码，请用新密码进行付款";
        try {
            isSuccess = service.physcardResetPin(physcardResetPinReqDTO);
            if(isSuccess){
                checkResDTO.setMessage(message);
            }
        } catch (FxCardException cardException){
            message = MessageFormat.format(failedMessage, cardException.getMessage());
            checkResDTO.setMessage(message);
        } catch (Exception e) {
            message = MessageFormat.format(failedMessage, e.getMessage());
            checkResDTO.setMessage(message);
        }
        checkResDTO.setResetStatus(isSuccess);
        return  responseItem(GlobalCoreResponseCode.SUCCESS , checkResDTO);
    }

}
