package com.fenbei.fx.card.web.card.vo;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CardWebListPageReqVO implements Serializable {
    /*公司ID*/
    private String companyId;
    /*员工名称*/
    private String employeeName;
    /*所属部门ID*/
    private String orgUnitId;
    /*员工手机号*/
    private String employeePhone;
    /*银行卡号*/
    private String bankAccountNo;
    /*所属银行*/
    private String bankName;
    /*卡的状态*/
    private Integer cardStatus;
    /**
     * 一级部门
     */
    private String stairOrgUnitId;
    /**
     * 员工id
     */
    private String employeeId;

    private Boolean isBalance = false;

    @Min(1L)
    @NotNull
    private Integer pageNo;
    @Min(1L)
    @NotNull
    private Integer pageSize;

    /**
     * 开始时间-导出部分时参数
     */
    private String startDate;
    /**
     * 结束时间-导出部分时参数
     */
    private String endDate;
}
