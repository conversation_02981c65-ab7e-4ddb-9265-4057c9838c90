package com.fenbei.fx.card.web.cardholderapply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.common.enums.CardPlatformEnum;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/18
 */
@Data
public class CardholderApplyCreateReqVO implements Serializable {

    private static final long serialVersionUID = 2329237403146067495L;

    /**
     * 申请单id，如果是编辑，传值
     */
    private String applyId;
    /**
     * 申请人名
     */
    @NotBlank
    private String firstName;
    /**
     * 申请人姓
     */
    @NotBlank
    private String lastName;

    /**
     * 证件的国家:US
     */
    @NotBlank
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    @NotNull
    private Integer identificationType;

    /**
     * 证件号
     */
    @NotBlank
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYYY-MM-DD
     * 如果是长期有效，此字段不需要传值
     */
    //@NotNull
    private String identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围 2-长期有效
     */
    @NotNull
    private Integer identificationExpiryType;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birth;

    private String nationCode;

    @NotNull
    private String phone;

    @NotBlank
    private String email;

    /**
     * 地区
     */
    @NotNull
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;

    /**
     * 开卡渠道,默认air，连连不支持
     */
    private String cardPlatform= CardPlatformEnum.AIRWALLEX.getCode();

}
