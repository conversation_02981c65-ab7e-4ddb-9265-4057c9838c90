package com.fenbei.fx.card.web.card.vo;

import com.fenbei.fx.card.service.usercard.dto.UserCardPettyInfoDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardTradeInfosDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
@ApiModel("国际卡 详情含交易 ResVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardDetailResVO implements Serializable {

    private CardShowResVO cardInfo;

    /**
     * 备用金信息
     */
    private UserCardPettyInfoDTO applyInfo;
    /**
     * 最近交易
     */
    private UserCardTradeInfosDTO tradeInfo;
}
