package com.fenbei.fx.card.web.card.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 卡激活-获取验证码 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-07 14:48:23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardPhyscardActiveReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;
    /**
     * 卡ID
     */
    @NotBlank(message = "卡号不能为空")
    private String fxCardId;


    /**
     * 卡的cvv
     */
    @NotBlank(message = "安全码不能为空")
    @Length(min=3,max=3,message = "安全码应为3位数字")
    private String cardCvv;


    /**
     * 设置密码
     */
    @NotBlank(message = "密码不能为空")
    @Length(min=4,max=4,message = "密码应为4位数字")
    private String cardPin;


    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Length(min=6,max=6,message = "验证码应为6位数字")
    private String verifyCode;

    @NotBlank(message = "请先获取验证码")
    private String token;
}



