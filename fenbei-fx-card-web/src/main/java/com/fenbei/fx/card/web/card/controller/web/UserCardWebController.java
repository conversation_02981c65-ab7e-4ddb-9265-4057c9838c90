package com.fenbei.fx.card.web.card.controller.web;


import com.fenbei.fx.card.service.usercard.UserCardCreditManager;
import com.fenbei.fx.card.service.usercard.dto.*;
import com.fenbei.fx.card.web.bankcardflow.vo.UserCardCreditGrantListReqVO;
import com.fenbei.fx.card.web.card.converter.CardVOConverter;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.ItemR;
import com.finhub.framework.web.vo.PageR;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-20
 */
@Api(tags = {"用户国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/web/usercard" )
public class UserCardWebController extends ControllerSupport {

    @Autowired
    CardVOConverter cardVOConverter;

    /**
     * 【WEB】额度下发记录
     * @param
     * @return
     */
    @RequestMapping(value = "/credit/grant/list",method = {RequestMethod.POST})
    public PageR<UserCardCreditGrantListDTO> creditGrantList(@RequestBody@Valid UserCardCreditGrantListReqVO userCardCreditGrantListReqVO){
        UserCardCreditGrantListQueryDTO userCardCreditGrantListQueryDTO = cardVOConverter.convertGrantListQueryDTO(userCardCreditGrantListReqVO);
        userCardCreditGrantListQueryDTO.setPageIndex(userCardCreditGrantListReqVO.getPageNo());
        userCardCreditGrantListQueryDTO.setCompanyId(UserAuthHolder.getCurrentUser().getCompany_id());
        Page<UserCardCreditGrantListDTO> userCardCreditGrantListDTOPage = UserCardCreditManager.me().creditGrantList(userCardCreditGrantListQueryDTO);
        return responsePageR(ResponseCodeEnum.SUCCESS, userCardCreditGrantListDTOPage.getTotal(),userCardCreditGrantListDTOPage.getRecords(),userCardCreditGrantListReqVO.getPageSize(),userCardCreditGrantListReqVO.getPageNo());
    }

    /**
     * 【WEB】额度下发记录导出
     * @param
     * @return
     */
    @RequestMapping(value = "/import/credit/grant/list",method = {RequestMethod.POST})
    public ItemR<List<UserCardCreditGrantListDTO>> importCreditGrantList(@RequestBody@Valid UserCardCreditGrantListReqVO userCardCreditGrantListReqVO){
        UserCardCreditGrantListQueryDTO userCardCreditGrantListQueryDTO = cardVOConverter.convertGrantListQueryDTO(userCardCreditGrantListReqVO);
        userCardCreditGrantListQueryDTO.setCompanyId(UserAuthHolder.getCurrentUser().getCompany_id());
        userCardCreditGrantListQueryDTO.setPageIndex(userCardCreditGrantListReqVO.getPageNo());
        Page<UserCardCreditGrantListDTO> userCardCreditGrantListDTOPage = UserCardCreditManager.me().creditGrantList(userCardCreditGrantListQueryDTO);
        return responseItemR(ResponseCodeEnum.SUCCESS, userCardCreditGrantListDTOPage.getRecords());
    }

    /**
     * 【WEB】人员配置模式列表（查询开卡记录维度）
     */
    @RequestMapping(value = "/employee/config/list",method = {RequestMethod.POST})
    public PageR<EmployeeModelConfigListDTO> employeeConfigList(@RequestBody EmployeeModelConfigListReqDTO employeeModelConfigListReqDTO){
        Page<EmployeeModelConfigListDTO> employeeConfigListDTOPage = UserCardCreditManager.me().employeeModelConfigList(employeeModelConfigListReqDTO);
        return responsePageR(ResponseCodeEnum.SUCCESS, employeeConfigListDTOPage.getTotal(),employeeConfigListDTOPage.getRecords(), employeeModelConfigListReqDTO.getPageSize(), employeeModelConfigListReqDTO.getPageIndex());
    }

    /**
     * 【WEB】卡额度变更记录列表
     */
    @RequestMapping(value = "/credit/change/record/list",method = {RequestMethod.POST})
    public PageR<BankCardCreditChangeDTO> creditChangeRecordList(@RequestBody BankCardCreditChangeRecordPageDTO bankCardCreditChangeRecordPageDTO){
        Page<BankCardCreditChangeDTO> bankCardFlowDTOPage = UserCardCreditManager.me().creditChangeRecordList(bankCardCreditChangeRecordPageDTO);
        return responsePageR(ResponseCodeEnum.SUCCESS, bankCardFlowDTOPage.getTotal(),bankCardFlowDTOPage.getRecords(), bankCardCreditChangeRecordPageDTO.getPageSize(), bankCardCreditChangeRecordPageDTO.getPageIndex());
    }
}
