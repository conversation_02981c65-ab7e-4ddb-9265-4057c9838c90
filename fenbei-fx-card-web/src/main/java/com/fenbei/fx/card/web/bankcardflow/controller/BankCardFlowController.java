package com.fenbei.fx.card.web.bankcardflow.controller;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.bankcardflow.BankCardFlowService;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowAddReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowListResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowModifyReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowPageResDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowRemoveReqDTO;
import com.fenbei.fx.card.service.bankcardflow.dto.BankCardFlowShowResDTO;
import com.fenbei.fx.card.web.bankcardflow.converter.BankCardFlowVOConverter;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowAddReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowListReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowListResVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowModifyReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowPageReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowPageResVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowRemoveReqVO;
import com.fenbei.fx.card.web.bankcardflow.vo.BankCardFlowShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 国际卡的操作流水,包含额度申请退回和消费退款 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Api(tags = {"国际卡的操作流水,包含额度申请退回和消费退款 API 接口"})
@Slf4j
@Validated
@RestController
public class BankCardFlowController extends BaseController<BankCardFlowService, BankCardFlowVOConverter> {

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-列表")
    @RequestMapping(value = "bankCardFlow/list", method = {RequestMethod.GET})
    public ItemsResult<BankCardFlowListResVO> list(BankCardFlowListReqVO bankCardFlowListReqVO) {
        BankCardFlowListReqDTO bankCardFlowListReqDTO = converter.convertToBankCardFlowListReqDTO(bankCardFlowListReqVO);

        List<BankCardFlowListResDTO> bankCardFlowListResDTOList = service.list(bankCardFlowListReqDTO);
        List<BankCardFlowListResVO> items = converter.convertToBankCardFlowListResVOList(bankCardFlowListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-First查询")
    @RequestMapping(value = "bankCardFlow/listOne", method = {RequestMethod.GET})
    public ItemResult<BankCardFlowListResVO> listOne(BankCardFlowListReqVO bankCardFlowListReqVO) {
        BankCardFlowListReqDTO bankCardFlowListReqDTO = converter.convertToBankCardFlowListReqDTO(bankCardFlowListReqVO);

        BankCardFlowListResDTO bankCardFlowListResDTO = service.listOne(bankCardFlowListReqDTO);
        BankCardFlowListResVO item = converter.convertToBankCardFlowListResVO(bankCardFlowListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "bankCardFlow/page", method = {RequestMethod.GET})
    public PageResult<BankCardFlowPageResVO> page(BankCardFlowPageReqVO bankCardFlowPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        BankCardFlowPageReqDTO bankCardFlowPageReqDTO = converter.convertToBankCardFlowPageReqDTO(bankCardFlowPageReqVO);

        Page<BankCardFlowPageResDTO> bankCardFlowPageResDTOPage = service.pagination(bankCardFlowPageReqDTO, current, size);
        Page<BankCardFlowPageResVO> bankCardFlowPageResVOPage = converter.convertToBankCardFlowPageResVOPage(bankCardFlowPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, bankCardFlowPageResVOPage.getTotal(), bankCardFlowPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-新增")
    @RequestMapping(value = "bankCardFlow/add", method = {RequestMethod.POST})
    public MessageResult add(BankCardFlowAddReqVO bankCardFlowAddReqVO) {
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = converter.convertToBankCardFlowAddReqDTO(bankCardFlowAddReqVO);

        Boolean isSuccess = service.add(bankCardFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-新增(所有字段)")
    @RequestMapping(value = "bankCardFlow/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(BankCardFlowAddReqVO bankCardFlowAddReqVO) {
        BankCardFlowAddReqDTO bankCardFlowAddReqDTO = converter.convertToBankCardFlowAddReqDTO(bankCardFlowAddReqVO);

        Boolean isSuccess = service.addAllColumn(bankCardFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-批量新增(所有字段)")
    @RequestMapping(value = "bankCardFlow/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<BankCardFlowAddReqVO> bankCardFlowAddReqVOList) {
        List<BankCardFlowAddReqDTO> bankCardFlowAddReqDTOList = converter.convertToBankCardFlowAddReqDTOList(bankCardFlowAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(bankCardFlowAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "bankCardFlow/show", method = {RequestMethod.GET})
    public ItemResult<BankCardFlowShowResVO> show(String id) {
        BankCardFlowShowResDTO bankCardFlowShowResDTO = service.show(id);

        BankCardFlowShowResVO item = converter.convertToBankCardFlowShowResVO(bankCardFlowShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "bankCardFlow/showByIds", method = {RequestMethod.POST})
    public ItemsResult<BankCardFlowShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<BankCardFlowShowResDTO> bankCardFlowShowResDTOList = service.showByIds(ids.getItems());

        List<BankCardFlowShowResVO> items = converter.convertToBankCardFlowShowResVOList(bankCardFlowShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-更新")
    @RequestMapping(value = "bankCardFlow/modify", method = {RequestMethod.POST})
    public MessageResult modify(BankCardFlowModifyReqVO bankCardFlowModifyReqVO) {
        BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO = converter.convertToBankCardFlowModifyReqDTO(bankCardFlowModifyReqVO);

        Boolean isSuccess = service.modify(bankCardFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-更新(所有字段)")
    @RequestMapping(value = "bankCardFlow/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(BankCardFlowModifyReqVO bankCardFlowModifyReqVO) {
        BankCardFlowModifyReqDTO bankCardFlowModifyReqDTO = converter.convertToBankCardFlowModifyReqDTO(bankCardFlowModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(bankCardFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "bankCardFlow/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "bankCardFlow/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡的操作流水,包含额度申请退回和消费退款-删除(参数)")
    @RequestMapping(value = "bankCardFlow/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(BankCardFlowRemoveReqVO bankCardFlowRemoveReqVO) {
        BankCardFlowRemoveReqDTO bankCardFlowRemoveReqDTO = converter.convertToBankCardFlowRemoveReqDTO(bankCardFlowRemoveReqVO);

        Boolean isSuccess = service.removeByParams(bankCardFlowRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
