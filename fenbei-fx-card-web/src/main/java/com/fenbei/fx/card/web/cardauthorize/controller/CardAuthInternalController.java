package com.fenbei.fx.card.web.cardauthorize.controller;

import com.fenbei.fx.card.api.card.ICardTradeService;
import com.fenbei.fx.card.api.card.dto.AuthorizationRpcReqDTO;
import com.fenbei.fx.card.api.card.dto.AuthorizationRpcRespDTO;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.service.cardauthorize.CardAuthorizeService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerApplyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.manager.CardCreditManagerManager;
import com.fenbei.fx.card.service.cardorder.CardOrderService;
import com.fenbei.fx.card.util.FinhubExceptionUtil;
import com.fenbei.fx.card.web.cardauthorize.converter.CardAuthorizeVOConverter;
import com.fenbei.fx.card.web.cardorder.converter.CardOrderVOConverter;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderModifyReqVO;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.MessageResult;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 国际卡订单 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/internal/cardauth" )
public class CardAuthInternalController extends BaseController<CardAuthorizeService, CardAuthorizeVOConverter> {

    @Autowired
    private ICardTradeService iCardTradeService;
    /**
     *
     * @param authorizationRpcReqDTO
     * @return
     * @undone
     */
    @RequestMapping(value = "/authorize", method = {RequestMethod.POST})
    public MessageResult authorize(@RequestBody AuthorizationRpcReqDTO authorizationRpcReqDTO) {
        if(Objects.isNull(authorizationRpcReqDTO)){
            throw FinhubExceptionUtil.exceptionFrom(GlobalCoreResponseCode.ILLEGAL_ARGUMENT);
        }
        AuthorizationRpcRespDTO authorize  = iCardTradeService.authorize(authorizationRpcReqDTO);
        return responseMessage(ResponseCodeEnum.SUCCESS,JsonUtils.toJson(authorize));
    }


}
