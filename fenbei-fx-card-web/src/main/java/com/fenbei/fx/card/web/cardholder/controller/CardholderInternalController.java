package com.fenbei.fx.card.web.cardholder.controller;

import com.fenbei.fx.card.service.cardholder.CardholderService;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.MessageResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * 持卡人操作 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"持卡人内部 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping("/fxcard/internal/cardholder")
public class CardholderInternalController extends ControllerSupport {

    /**CardholderServiceImpl.java
     * 持卡人全量信息比对
     * @return
     */
    @RequestMapping(value = "/allCompare")
    public MessageResult add() {
        CompletableFuture.runAsync(()->{
            CardholderService.me().allCompare();
        });
        return responseMessage(ResponseCodeEnum.SUCCESS);
    }

}
