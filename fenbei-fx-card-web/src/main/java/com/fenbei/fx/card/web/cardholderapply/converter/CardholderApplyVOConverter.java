package com.fenbei.fx.card.web.cardholderapply.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyAddReqDTO;
import com.fenbei.fx.card.service.cardholderapply.dto.CardholderApplyDTO;
import com.fenbei.fx.card.web.cardholderapply.vo.CardholderApplyCreateReqVO;
import com.fenbei.fx.card.web.cardholderapply.vo.CardholderApplyVO;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import org.mapstruct.Mapper;

/**
 * 持卡人操作申请 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardholderApplyVOConverter extends BaseVOConverter<CardholderApplyDTO, CardholderApplyVO> {

    static CardholderApplyVOConverter me() {
        return SpringUtil.getBean(CardholderApplyVOConverter.class);
    }

    CardholderApplyAddReqDTO convertToCardholderApplyReqDTO(CardholderApplyCreateReqVO cardholderApplyCreateReqVO);

}
