package com.fenbei.fx.card.web.card.controller.app;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.vo.KeyValueVO;
import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.usercard.UserCardHomePageManager;
import com.fenbei.fx.card.service.usercard.dto.UserCardHomePageDTO;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.CurrencyNumberFormatUtil;
import com.fenbei.fx.card.web.card.vo.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.CurrencyEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemR;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.web.card.converter.CardVOConverter;
import com.luastar.swift.base.utils.CollectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/app/card")
public class CardAppController extends BaseController<CardService, CardVOConverter> {

    @ApiOperation(value = "国际卡-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "/page", method = {RequestMethod.GET})
    public PageResult<CardPageResVO> page(CardPageReqVO cardPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardPageReqDTO cardPageReqDTO = converter.convertToCardPageReqDTO(cardPageReqVO);
        cardPageReqDTO.setCompanyId(user.getCompany_id());
        Page<CardPageResDTO> cardPageResDTOPage = service.pagination(cardPageReqDTO, current, size);
        List<CardPageResVO> applyListInfoVOS = CopyUtils.copyList(cardPageResDTOPage.getRecords(), CardPageResVO.class);

        return responsePage(GlobalCoreResponseCode.SUCCESS, cardPageResDTOPage.getTotal(), applyListInfoVOS, size, current);
    }


    /**
     * [web]卡可用余额汇总
     * @return
     */
    @RequestMapping(value = "/sum/balance", method = {RequestMethod.POST})
    public ItemR<CardSumBalanceResDTO> sumBalance() {

        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardSumBalanceResDTO cardSumBalance = service.cardSumBalance(user.getCompany_id());

        return responseItemR(GlobalCoreResponseCode.SUCCESS, cardSumBalance);
    }


    @RequestMapping(value = "/allCard", method = {RequestMethod.GET})
    public PageResult<CardPageResVO> allCard(CardPageReqVO cardPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardPageReqDTO cardPageReqDTO = converter.convertToCardPageReqDTO(cardPageReqVO);
        cardPageReqDTO.setCompanyId(user.getCompany_id());
        Page<CardPageResDTO> cardPageResDTOPage = service.allCard(cardPageReqDTO, current, size);
        List<CardPageResVO> applyListInfoVOS = CopyUtils.copyList(cardPageResDTOPage.getRecords(), CardPageResVO.class);

        return responsePage(GlobalCoreResponseCode.SUCCESS, cardPageResDTOPage.getTotal(), applyListInfoVOS, size, current);
    }


    /**
     * web端 查询企业下所有员工的虚拟卡信息(不受权限配置控制)
     * @param cardWebListPageReqVO
     * @return
     */
    @RequestMapping(value = "/query/vcard/noAuth/list", method = {RequestMethod.POST})
    public PageResult<CardWebListPageResVO> queryCardListForNoAuth(@RequestBody CardWebListPageReqVO cardWebListPageReqVO) {
        int current = Func.isNullOrZero(cardWebListPageReqVO.getPageNo()) ? 1 : cardWebListPageReqVO.getPageNo();
        int size = Func.isNullOrZero(cardWebListPageReqVO.getPageSize()) ? 10 : cardWebListPageReqVO.getPageSize();
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        cardWebListPageReqVO.setCompanyId(user.getCompany_id());
        CardWebListPageReqDTO cardWebListPageReqDTO =new CardWebListPageReqDTO();
        BeanUtils.copyProperties(cardWebListPageReqVO, cardWebListPageReqDTO);
        Page<CardWebListPageResDTO> cardPageResDTOPage = service.queryCardListForNoAuth(cardWebListPageReqDTO,current, size);

        // 转换为 CardWebListPageResVO
        List<CardWebListPageResVO> cardWebListPageResVOList = convertToCardWebListPageResVO(cardPageResDTOPage.getRecords());

        return responsePage(GlobalCoreResponseCode.SUCCESS, cardPageResDTOPage.getTotal(), cardWebListPageResVOList, size, current);
    }

    @ApiOperation(value = "国际卡-详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.GET})
    public ItemResult<CardShowResVO> detail(String fxCardId) {
        CardShowResDTO cardShowResDTO = service.cardDetail(fxCardId);

        CardShowResVO item = CopyUtils.convert(cardShowResDTO, CardShowResVO.class);
        CurrencyEnum currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(cardShowResDTO.getCurrency());
        if (!Objects.isNull(currencyEnum)){
            item.setCurrencySymbol(currencyEnum.getSymbol());
            item.setCurrencyName(currencyEnum.getDisplayName());
        }
        item.setShowUSDBalance(CurrencyNumberFormatUtil.moneyFormart(currencyEnum,BigDecimalUtils.fen2yuan(item.getBalance())));
        String formId = UserCardHomePageManager.me().getUserApplyFormId(cardShowResDTO.getCompanyId(), cardShowResDTO.getEmployeeId());
        item.setFormId(formId);

        return responseItem(GlobalCoreResponseCode.SUCCESS, item);
    }

    /**
     * 包含交易详情的卡详情
     * @param fxCardId
     * @return
     */
    @ApiOperation(value = "国际卡-详情含交易记录")
    @RequestMapping(value = "/detail/info", method = {RequestMethod.GET})
    public ItemResult<CardDetailResVO> detailWithTrade(String fxCardId) {
        CardShowResDTO cardShowResDTO = service.cardDetail(fxCardId);
        CardShowResVO item = CopyUtils.convert(cardShowResDTO, CardShowResVO.class);
        //处理币种
        CurrencyEnum currencyEnum = CurrencyEnum.getCurrencyByCodeIgnoreCase(cardShowResDTO.getCurrency());
        if (!Objects.isNull(currencyEnum)){
            item.setCurrencySymbol(currencyEnum.getSymbol());
            item.setCurrencyName(currencyEnum.getDisplayName());
        }
        // 备用金卡片 + 交易列表卡片（3条）
        UserCardHomePageDTO userCardHomePageDTO = UserCardHomePageManager.me().homePage(fxCardId);
        item.setShowUSDBalance(CurrencyNumberFormatUtil.moneyFormart(currencyEnum,BigDecimalUtils.fen2yuan(item.getBalance())));
        String formId = UserCardHomePageManager.me().getUserApplyFormId(cardShowResDTO.getCompanyId(), cardShowResDTO.getEmployeeId());
        item.setFormId(formId);
        CardDetailResVO cardDetailResVO = new CardDetailResVO();
        cardDetailResVO.setCardInfo(item);
        cardDetailResVO.setApplyInfo(userCardHomePageDTO.getApplyInfo());
        cardDetailResVO.setTradeInfo(userCardHomePageDTO.getTradeInfo());
        return responseItem(GlobalCoreResponseCode.SUCCESS, cardDetailResVO);
    }

    @ApiOperation(value = "激活卡")
    @RequestMapping(value = "/activateCard", method = {RequestMethod.POST})
    public MessageResult activateCard(@RequestBody  ActivateCardReqVO activateCardReqVO) {
        ActivateCardReqDTO activateCardReqDTO = converter.convertToActivateCardReqDTO(activateCardReqVO);
        Boolean isSuccess = service.activateCard(activateCardReqDTO);
        return responseMessage(isSuccess ? GlobalCoreResponseCode.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 转换 CardPageResDTO 为 CardWebListPageResVO
     */
    private List<CardWebListPageResVO> convertToCardWebListPageResVO(List<CardWebListPageResDTO> cardPageResDTOList) {
        List<CardWebListPageResVO> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(cardPageResDTOList)) {
            return resultList;
        }

        for (CardWebListPageResDTO cardWebListPageResDTO: cardPageResDTOList) {
            CardWebListPageResVO cardWebListPageResVO = new CardWebListPageResVO();
            BeanUtils.copyProperties(cardWebListPageResDTO, cardWebListPageResVO);
            if (cardWebListPageResDTO.getCardsInfo() != null) {
                List<CardWebListPageResVO.CardsInfo> cardsInfoList = new ArrayList<>();
                cardWebListPageResDTO.getCardsInfo().forEach(cardsInfo -> {
                    CardWebListPageResVO.CardsInfo cardsInfoVO = new CardWebListPageResVO.CardsInfo();
                    BeanUtils.copyProperties(cardsInfo, cardsInfoVO);
                    cardsInfoList.add(cardsInfoVO);
                });
                cardWebListPageResVO.setCardsInfo(cardsInfoList);
            }
            resultList.add(cardWebListPageResVO);
        }

        return resultList;
    }

    /**
     * 获取卡片状态描述
     */
    private String getCardStatusDesc(Integer cardStatus) {
        if (cardStatus == null) {
            return "未知";
        }

        switch (cardStatus) {
            case 1:
                return "生效中";
            case 2:
                return "已禁用";
            case 3:
                return "挂失";
            case 4:
                return "被盗";
            case 5:
                return "已注销";
            case 6:
                return "冻结";
            default:
                return "未知";
        }
    }

}
