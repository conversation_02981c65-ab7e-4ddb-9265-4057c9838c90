package com.fenbei.fx.card.web.cardholder.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
@Data
public class CardholderByPageReqVO implements Serializable {
    private static final long serialVersionUID = -9000061398757805342L;

    /**
     * 和前端约定，区分申请单表还是持卡人表
     */
    @NotNull
    private Integer showStatus;

    /**
     * 员工id
     */
    private String employeeId;
    private String name;

    private String phone;

    private String companyId;

    private Integer pageNo = 1;

    private Integer pageSize = 10;





}
