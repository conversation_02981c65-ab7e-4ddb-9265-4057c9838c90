package com.fenbei.fx.card.web.cardholderoperflow.controller;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.cardholderoperflow.CardholderOperFlowService;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import com.fenbei.fx.card.web.cardholderoperflow.converter.CardholderOperFlowVOConverter;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowAddReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowListReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowListResVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowModifyReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowPageReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowPageResVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowRemoveReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 持卡人被操作流水 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Api(tags = {"持卡人被操作流水 API 接口"})
@Slf4j
@Validated
@RestController
public class CardholderOperFlowController extends BaseController<CardholderOperFlowService, CardholderOperFlowVOConverter> {

    @ApiOperation(value = "持卡人被操作流水-列表")
    @RequestMapping(value = "cardholderOperFlow/list", method = {RequestMethod.GET})
    public ItemsResult<CardholderOperFlowListResVO> list(CardholderOperFlowListReqVO cardholderOperFlowListReqVO) {
        CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO = converter.convertToCardholderOperFlowListReqDTO(cardholderOperFlowListReqVO);

        List<CardholderOperFlowListResDTO> cardholderOperFlowListResDTOList = service.list(cardholderOperFlowListReqDTO);
        List<CardholderOperFlowListResVO> items = converter.convertToCardholderOperFlowListResVOList(cardholderOperFlowListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "持卡人被操作流水-First查询")
    @RequestMapping(value = "cardholderOperFlow/listOne", method = {RequestMethod.GET})
    public ItemResult<CardholderOperFlowListResVO> listOne(CardholderOperFlowListReqVO cardholderOperFlowListReqVO) {
        CardholderOperFlowListReqDTO cardholderOperFlowListReqDTO = converter.convertToCardholderOperFlowListReqDTO(cardholderOperFlowListReqVO);

        CardholderOperFlowListResDTO cardholderOperFlowListResDTO = service.listOne(cardholderOperFlowListReqDTO);
        CardholderOperFlowListResVO item = converter.convertToCardholderOperFlowListResVO(cardholderOperFlowListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "持卡人被操作流水-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardholderOperFlow/page", method = {RequestMethod.GET})
    public PageResult<CardholderOperFlowPageResVO> page(CardholderOperFlowPageReqVO cardholderOperFlowPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardholderOperFlowPageReqDTO cardholderOperFlowPageReqDTO = converter.convertToCardholderOperFlowPageReqDTO(cardholderOperFlowPageReqVO);

        Page<CardholderOperFlowPageResDTO> cardholderOperFlowPageResDTOPage = service.pagination(cardholderOperFlowPageReqDTO, current, size);
        Page<CardholderOperFlowPageResVO> cardholderOperFlowPageResVOPage = converter.convertToCardholderOperFlowPageResVOPage(cardholderOperFlowPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardholderOperFlowPageResVOPage.getTotal(), cardholderOperFlowPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "持卡人被操作流水-新增")
    @RequestMapping(value = "cardholderOperFlow/add", method = {RequestMethod.POST})
    public MessageResult add(CardholderOperFlowAddReqVO cardholderOperFlowAddReqVO) {
        CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO = converter.convertToCardholderOperFlowAddReqDTO(cardholderOperFlowAddReqVO);

        Boolean isSuccess = service.add(cardholderOperFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-新增(所有字段)")
    @RequestMapping(value = "cardholderOperFlow/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardholderOperFlowAddReqVO cardholderOperFlowAddReqVO) {
        CardholderOperFlowAddReqDTO cardholderOperFlowAddReqDTO = converter.convertToCardholderOperFlowAddReqDTO(cardholderOperFlowAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardholderOperFlowAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-批量新增(所有字段)")
    @RequestMapping(value = "cardholderOperFlow/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardholderOperFlowAddReqVO> cardholderOperFlowAddReqVOList) {
        List<CardholderOperFlowAddReqDTO> cardholderOperFlowAddReqDTOList = converter.convertToCardholderOperFlowAddReqDTOList(cardholderOperFlowAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardholderOperFlowAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardholderOperFlow/show", method = {RequestMethod.GET})
    public ItemResult<CardholderOperFlowShowResVO> show(String id) {
        CardholderOperFlowShowResDTO cardholderOperFlowShowResDTO = service.show(id);

        CardholderOperFlowShowResVO item = converter.convertToCardholderOperFlowShowResVO(cardholderOperFlowShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "持卡人被操作流水-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardholderOperFlow/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardholderOperFlowShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardholderOperFlowShowResDTO> cardholderOperFlowShowResDTOList = service.showByIds(ids.getItems());

        List<CardholderOperFlowShowResVO> items = converter.convertToCardholderOperFlowShowResVOList(cardholderOperFlowShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "持卡人被操作流水-更新")
    @RequestMapping(value = "cardholderOperFlow/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardholderOperFlowModifyReqVO cardholderOperFlowModifyReqVO) {
        CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO = converter.convertToCardholderOperFlowModifyReqDTO(cardholderOperFlowModifyReqVO);

        Boolean isSuccess = service.modify(cardholderOperFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-更新(所有字段)")
    @RequestMapping(value = "cardholderOperFlow/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardholderOperFlowModifyReqVO cardholderOperFlowModifyReqVO) {
        CardholderOperFlowModifyReqDTO cardholderOperFlowModifyReqDTO = converter.convertToCardholderOperFlowModifyReqDTO(cardholderOperFlowModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardholderOperFlowModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardholderOperFlow/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardholderOperFlow/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "持卡人被操作流水-删除(参数)")
    @RequestMapping(value = "cardholderOperFlow/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardholderOperFlowRemoveReqVO cardholderOperFlowRemoveReqVO) {
        CardholderOperFlowRemoveReqDTO cardholderOperFlowRemoveReqDTO = converter.convertToCardholderOperFlowRemoveReqDTO(cardholderOperFlowRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardholderOperFlowRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
