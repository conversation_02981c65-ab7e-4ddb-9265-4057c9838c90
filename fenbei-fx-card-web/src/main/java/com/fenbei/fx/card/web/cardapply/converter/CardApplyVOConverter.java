package com.fenbei.fx.card.web.cardapply.converter;

import com.fenbei.fx.card.service.card.dto.UpdateCardStatusReqDTO;
import com.fenbei.fx.card.service.cardapply.dto.*;
import com.fenbei.fx.card.web.card.vo.UpdateCardStatusReqVO;
import com.fenbei.fx.card.web.cardapply.vo.*;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡操作申请 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardApplyVOConverter extends BaseVOConverter<CardApplyDTO, CardApplyVO> {

    static CardApplyVOConverter me() {
        return SpringUtil.getBean(CardApplyVOConverter.class);
    }

    CardApplyAddReqDTO convertToCardApplyAddReqDTO(CardApplyAddReqVO cardApplyAddReqVO);

    CardApplyShowResVO convertToCardApplyShowResVO(CardApplyShowResDTO cardApplyShowResDTO);

    CardApplyModifyReqDTO convertToCardApplyModifyReqDTO(CardApplyModifyReqVO cardApplyModifyReqVO);

    CardApplyRemoveReqDTO convertToCardApplyRemoveReqDTO(CardApplyRemoveReqVO cardApplyRemoveReqVO);

    CardApplyListReqDTO convertToCardApplyListReqDTO(CardApplyListReqVO cardApplyReqVO);

    CardApplyPageReqDTO convertToCardApplyPageReqDTO(CardApplyPageReqVO cardApplyPageReqVO);

    CardApplyListResVO convertToCardApplyListResVO(CardApplyListResDTO cardApplyListResDTO);

    List<CardApplyListResVO> convertToCardApplyListResVOList(List<CardApplyListResDTO> cardApplyListResDTOList);

    Page<CardApplyPageResVO> convertToCardApplyPageResVOPage(Page<CardApplyPageResDTO> cardApplyPageResDTOPage);

    List<CardApplyAddReqDTO> convertToCardApplyAddReqDTOList(List<CardApplyAddReqVO> cardApplyAddReqVOList);

    List<CardApplyShowResVO> convertToCardApplyShowResVOList(List<CardApplyShowResDTO> cardApplyShowResDTOList);

    CreateCardApplyReqDTO convertToCreateCardApplyReqDTO(CreateCardApplyReqVO createCardApplyReqVO);

    UpdateCardApplyReqDTO convertToUpdateCardApplyReqDTO(UpdateCardApplyReqVO updateCardApplyReqVO);

}
