package com.fenbei.fx.card.web.cardholder.controller.web;

import com.fenbei.fx.card.service.cardholder.CardholderService;
import com.fenbei.fx.card.service.cardholder.dto.*;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.util.ValidateCommonUtils;
import com.fenbei.fx.card.web.cardholder.converter.CardholderVOConverter;
import com.fenbei.fx.card.web.cardholder.vo.CardholderByPageReqVO;
import com.fenbei.fx.card.web.cardholder.vo.CardholderDetailReqVO;
import com.fenbei.fx.card.web.cardholder.vo.CardholderModifyAndApproveReqVO;
import com.fenbei.fx.card.web.cardholder.vo.CardholderModifyReqVO;
import com.fenbei.fx.card.web.common.base.CommonController;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;
import com.luastar.swift.base.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 持卡人 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"持卡人 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping("/fxcard/web/cardholder")
public class CardholderWebController extends CommonController<CardholderService, CardholderVOConverter> {

    @DubboReference
    protected IBaseEmployeeExtService iBaseEmployeeExtService;

    @ApiOperation(value = "持卡人-列表查询(包括申请表数据)")
    @RequestMapping(value = "/findByPage", method = {RequestMethod.GET})
    public PageResult<CardholderByPageResDTO> findByPage(CardholderByPageReqVO reqVO) {
        CardholderByPageReqDTO reqDTO = new CardholderByPageReqDTO();
        BeanUtils.copyProperties(reqVO, reqDTO);
        Page<CardholderByPageResDTO> resPage = service.findCardholderOrApplyByPage(reqDTO);
        return responsePage(ResponseCodeEnum.SUCCESS, resPage.getTotal(), resPage.getRecords(), reqVO.getPageSize(), reqVO.getPageNo());

    }

    @ApiOperation(value = "持卡人-编辑审核入口(包括申请表编辑审核)")
    @RequestMapping(value = "/modify", method = {RequestMethod.POST})
    public ItemResult<CardholderDetailResDTO> modify(@RequestBody CardholderModifyReqVO reqVO) {
        ValidateCommonUtils.validate(reqVO);
        CardholderModifyReqDTO reqDTO = CopyUtils.convert(reqVO, CardholderModifyReqDTO.class);
        CardholderDetailResDTO detail = service.modify(reqDTO);
        return responseItem(ResponseCodeEnum.SUCCESS, detail);
    }

    @ApiOperation(value = "用户的持卡人信息(包括申请表数据)")
    @RequestMapping(value = "/findUserCardholderOrApply", method = {RequestMethod.GET})
    public ItemResult<CardholderOrApplyListResDTO> findUserCardholderOrApply(CardholderOrApplyListReqDTO reqDTO) {
        CardholderOrApplyListResDTO userCardholderOrApply = service.findUserCardholderOrApply(reqDTO);
        return responseItem(ResponseCodeEnum.SUCCESS, userCardholderOrApply);

    }

    @ApiOperation(value = "持卡人-详情(包括申请表详情)")
    @RequestMapping(value = "/detail", method = {RequestMethod.GET})
    public ItemResult<CardholderDetailResDTO> detail(CardholderDetailReqVO reqVO) {
//        checkPrivilege();
        CardholderDetailResDTO detail = service.detail(reqVO.getApplyId(), reqVO.getFxCardholderId(), reqVO.getShowStatus());

        return responseItem(ResponseCodeEnum.SUCCESS, detail);
    }

    @ApiOperation(value = "持卡人-启用/禁用")
    @RequestMapping(value = "/enableSwitch", method = {RequestMethod.POST})
    public MessageResult enableSwitch(@RequestBody CardholderEnableReqDTO reqVO) {
        service.enableSwitch(reqVO);
        return responseMessage(ResponseCodeEnum.SUCCESS);
    }
}
