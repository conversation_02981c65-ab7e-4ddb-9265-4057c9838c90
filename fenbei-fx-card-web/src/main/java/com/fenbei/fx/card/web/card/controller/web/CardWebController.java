package com.fenbei.fx.card.web.card.controller.web;

import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.common.enums.TransactionTypeEnum;
import com.fenbei.fx.card.common.kafka.KafkaProducer;
import com.fenbei.fx.card.service.card.CardService;
import com.fenbei.fx.card.service.card.dto.*;
import com.fenbei.fx.card.service.card.dto.UpdateCardStatusReqDTO;
import com.fenbei.fx.card.service.webservice.MessageService;
import com.fenbei.fx.card.service.webservice.dto.PushAlertDto;
import com.fenbei.fx.card.util.CopyUtils;
import com.fenbei.fx.card.web.card.converter.CardVOConverter;
import com.fenbei.fx.card.web.card.vo.*;
import com.fenbei.fx.card.web.card.vo.UpdateCardStatusReqVO;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.annotation.FinhubExcludeAuth;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.fenbeitong.finhub.common.constant.BizType;
import com.fenbeitong.finhub.common.constant.MessageSubType;
import com.fenbeitong.finhub.common.constant.MessageType;
import com.fenbeitong.finhub.common.constant.SenderType;
import com.fenbeitong.finhub.common.utils.DateUtils;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaSaasMessageMsg;
import com.fenbeitong.finhub.kafka.msg.saas.KafkaWebMessageMsg;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/web/card/")
public class CardWebController extends BaseController<CardService, CardVOConverter> {

    @ApiOperation(value = "国际卡-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "/page", method = {RequestMethod.GET})
    public PageResult<CardPageResVO> page(CardPageReqVO cardPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardPageReqDTO cardPageReqDTO = converter.convertToCardPageReqDTO(cardPageReqVO);
        cardPageReqDTO.setCompanyId(user.getCompany_id());
        Page<CardPageResDTO> cardPageResDTOPage = service.pagination(cardPageReqDTO, current, size);
        List<CardPageResVO> applyListInfoVOS = CopyUtils.copyList(cardPageResDTOPage.getRecords(), CardPageResVO.class);

        return responsePage(GlobalCoreResponseCode.SUCCESS, cardPageResDTOPage.getTotal(), applyListInfoVOS, size, current);
    }

    /**
     * 卡列表导出
     * @param cardPageReqVO
     * @return
     */
    @RequestMapping(value = "/export/page", method = {RequestMethod.POST})
    public ItemR<List<CardPageResDTO>> exportPage(@RequestBody CardPageReqVO cardPageReqVO) {
        int current = Func.isNullOrZero(cardPageReqVO.getPageNo()) ? 1 : cardPageReqVO.getPageNo();
        int size = Func.isNullOrZero(cardPageReqVO.getPageSize()) ? 10 : cardPageReqVO.getPageSize();
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardPageReqDTO cardPageReqDTO = converter.convertToCardPageReqDTO(cardPageReqVO);
        cardPageReqDTO.setCompanyId(user.getCompany_id());
        Page<CardPageResDTO> cardPageResDTOPage = service.pagination(cardPageReqDTO, current, size);

        return responseItemR(ResponseCodeEnum.SUCCESS, cardPageResDTOPage.getRecords());
    }

    /**
     * [web]卡可用余额汇总
     * @return
     * 后续使用sumBalanceList
     */
    @Deprecated
    @RequestMapping(value = "/sum/balance", method = {RequestMethod.POST})
    public ItemR<CardSumBalanceResDTO> sumBalance() {

        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        CardSumBalanceResDTO cardSumBalance = service.cardSumBalance(user.getCompany_id());

        return responseItemR(GlobalCoreResponseCode.SUCCESS, cardSumBalance);
    }


    /**
     * [web]卡可用余额汇总,支持多币种
     * @return
     */
    @RequestMapping(value = "/sum/balance/list", method = {RequestMethod.POST})
    public ItemR<List<CardSumBalanceListResDTO>> sumBalanceList() {

        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        List<CardSumBalanceListResDTO> cardSumBalanceList = service.cardSumBalanceList(user.getCompany_id());

        return responseItemR(GlobalCoreResponseCode.SUCCESS, cardSumBalanceList);
    }

    /**
     * [web]卡片详情
     * @param fxCardId
     * @return
     */
    @RequestMapping(value = "/detail", method = {RequestMethod.GET})
    public ItemR<CardShowResVO> detail(String fxCardId) {
        CardShowResDTO cardShowResDTO = service.cardDetail(fxCardId);

        CardShowResVO item = CopyUtils.convert(cardShowResDTO, CardShowResVO.class);

        return responseItemR(GlobalCoreResponseCode.SUCCESS, item);
    }

    @ApiOperation(value = "更新卡状态")
    @RequestMapping(value = "/updateCardStatus", method = {RequestMethod.POST})
    public MessageResult updateCardStatus(@RequestBody UpdateCardStatusReqVO updateCardStatusReqVO) {
        UpdateCardStatusReqDTO updateCardStatusReqDTO = converter.convertToUpdateCardStatusReqDTO(updateCardStatusReqVO);
        Boolean isSuccess = service.updateCardStatus(updateCardStatusReqDTO);
        return responseMessage(isSuccess ? GlobalCoreResponseCode.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "更新限额")
    @RequestMapping(value = "/updateLimits", method = {RequestMethod.POST})
    public MessageResult updateLimits(@RequestBody UpdateCardStatusReqVO updateCardStatusReqVO) {
        UpdateCardStatusReqDTO updateCardStatusReqDTO = CopyUtils.convert(updateCardStatusReqVO,UpdateCardStatusReqDTO.class);
        Boolean isSuccess = service.updateLimits(updateCardStatusReqDTO);
        return responseMessage(isSuccess ? GlobalCoreResponseCode.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }



    @FinhubExcludeAuth
    @RequestMapping(value = "/test", method = {RequestMethod.GET})
    public ItemR<CardShowResVO> test(String emplpyeeId ,String companyId ,String orderId) {

        sendPushAndMsg("1",orderId , TransactionTypeEnum.PRE_AUTH_RELEASE,emplpyeeId,companyId);

        return responseItemR(GlobalCoreResponseCode.SUCCESS, null);
    }


    public void sendPushAndMsg(String transAmount,String orderId ,TransactionTypeEnum transactionTypeEnum , String receiverId ,String companyId) {
        if (StringUtils.isNotBlank(receiverId)) {
            // 替换消息内容
            String content = "您有一笔${0}的{1}";
            String text = MessageFormat.format(content, transAmount ,transactionTypeEnum.getValue() );
            // APP push推送 / OA消息推送
            try {
                messageService.pushAlertMsg(buildAppAndOaPush(orderId,transactionTypeEnum, text, receiverId));
            } catch (IOException e) {
                log.error("APP push推送 / OA消息推送异常", e);
            }
            // 批量发送-APP消息中心
            KafkaProducer.me().publish(buildAppMsg(companyId,orderId,transactionTypeEnum, text, receiverId));
            // 批量发送-企业web消息中心
            KafkaProducer.me().publish(buildWebMsg(companyId,orderId,transactionTypeEnum, text, receiverId));
        }
    }


    @Autowired
    public MessageService messageService;

    private static final String TITLE = "海外卡通知-";

    private PushAlertDto buildAppAndOaPush(String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        PushAlertDto push = new PushAlertDto();
        push.setMsgType("81");
        push.setTitle(TITLE + transactionTypeEnum.getValue());
        push.setContent(content);
        push.setDesc(content);
        push.setUserId(receiver);
        // 自定义参数 提供路由跳转参数
        Map<String, Object> map = Maps.newHashMap();
        map.put("order_id", orderId);
        map.put("order_type", transactionTypeEnum.getKey());
        push.setMsgObj(map);
        return push;
    }

    private KafkaSaasMessageMsg buildAppMsg(String companyId , String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        log.info("海外卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId,transactionTypeEnum.getValue(), content);

        String titleName = TITLE + transactionTypeEnum.getValue();

        KafkaSaasMessageMsg kafkaSaasMessageMsg = new KafkaSaasMessageMsg();
        kafkaSaasMessageMsg.setBizType(81);
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.System.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setInfo(getInfo(orderId ,transactionTypeEnum));
        log.info("海外卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private KafkaWebMessageMsg buildWebMsg(String companyId , String orderId , TransactionTypeEnum transactionTypeEnum , String content, String receiver) {
        log.info("海外卡消费发送消息，参数:{}, 类型：{},状态：{}", orderId,transactionTypeEnum.getValue(), content);
        String titleName = TITLE + transactionTypeEnum.getValue();

        KafkaWebMessageMsg kafkaSaasMessageMsg = new KafkaWebMessageMsg();
        kafkaSaasMessageMsg.setBizType(BizType.AllConsume.getCode());
        kafkaSaasMessageMsg.setComment(content);
        kafkaSaasMessageMsg.setTitle(titleName);
        kafkaSaasMessageMsg.setCompanyId(companyId);
        kafkaSaasMessageMsg.setMsgType(MessageType.Consume.getCode());
        kafkaSaasMessageMsg.setSenderType(SenderType.HL.getCode());
        kafkaSaasMessageMsg.setReceiver(receiver);
        kafkaSaasMessageMsg.setSender(receiver);
        kafkaSaasMessageMsg.setBizOrder(orderId);
        kafkaSaasMessageMsg.setMsgSubType(MessageSubType.LARGE_OVER.getCode());
        log.info("海外卡消费发送消息，企业编号" + companyId + "," + JsonUtils.toJson(kafkaSaasMessageMsg));
        return kafkaSaasMessageMsg;
    }

    private String getInfo(String orderId ,TransactionTypeEnum transactionTypeEnum) {
        JSONObject json = new JSONObject();
        json.put("generate_time", DateUtils.format(new Date(), DateUtils.FORMAT_DATE_TIME_PATTERN));
        json.put("apply_type", transactionTypeEnum.getKey());
        json.put("apply_id", orderId);
        json.put("data_type" , transactionTypeEnum.getKey());
        return json.toJSONString();
    }

}
