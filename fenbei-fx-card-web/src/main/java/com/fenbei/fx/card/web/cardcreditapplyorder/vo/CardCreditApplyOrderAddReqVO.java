package com.fenbei.fx.card.web.cardcreditapplyorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡额度发放单 添加 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */
@ApiModel("国际卡额度发放单 添加 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditApplyOrderAddReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private String id;

    /**
     * 公司ID
     */
    @ApiModelProperty(example = "0", value = "公司ID")
    private String companyId;

    /**
     * 批量创建的批次号
     */
    @ApiModelProperty(example = "0", value = "批量创建的批次号")
    private String applyBatchNo;

    /**
     * 单据主键ID
     */
    @ApiModelProperty(example = "0", value = "单据主键ID")
    private String applyOrderId;

    /**
     * 申请单ID编号
     */
    @ApiModelProperty(example = "0", value = "申请单ID编号")
    private String applyId;

    /**
     * 单据编号
     */
    @ApiModelProperty(example = "0", value = "单据编号")
    private String meaningNo;

    /**
     * 审批单类型  40海外卡发放单(大类别)
     */
    @ApiModelProperty(example = "0", value = "审批单类型  40海外卡发放单(大类别)")
    private Integer applyOrderType;

    /**
     * 43:海外卡发放单(小类别)
     */
    @ApiModelProperty(example = "0", value = "43:海外卡发放单(小类别)")
    private Integer applyOrderSubType;

    /**
     * 1: 普通模式2: 备用金模式;
     */
    @ApiModelProperty(example = "0", value = "1: 普通模式2: 备用金模式;")
    private Integer activeModel;

    /**
     * 标题
     */
    @ApiModelProperty(example = "0", value = "标题")
    private String title;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "0", value = "币种 美元-USD")
    private String currency;

    /**
     * 申请总金额(单位：分)
     */
    @ApiModelProperty(example = "0", value = "申请总金额(单位：分)")
    private BigDecimal applyAmount;

    /**
     * 申请事由
     */
    @ApiModelProperty(example = "0", value = "申请事由")
    private String applyReason;

    /**
     * 申请事由id
     */
    @ApiModelProperty(example = "0", value = "申请事由id")
    private Integer applyReasonId;

    /**
     * -1:制单失败 0:制单中 1.制单成功(待发放) 2.发放中 3.下发额度成功 4.下发额度失败
     */
    @ApiModelProperty(example = "0", value = "-1:制单失败 0:制单中 1.制单成功(待发放) 2.发放中 3.下发额度成功 4.下发额度失败")
    private Integer applyState;

    /**
     * 制单和下发结果描述
     */
    @ApiModelProperty(example = "0", value = "制单和下发结果描述")
    private String applyResultDesc;

    /**
     * 1核销扣;2申请扣
     */
    @ApiModelProperty(example = "0", value = "1核销扣;2申请扣")
    private Integer deductionMode;

    /**
     * 制单人ID
     */
    @ApiModelProperty(example = "0", value = "制单人ID")
    private String createrId;

    /**
     * 制单人名称
     */
    @ApiModelProperty(example = "0", value = "制单人名称")
    private String createrName;

    /**
     * 申请人id
     */
    @ApiModelProperty(example = "0", value = "申请人id")
    private String applicantId;

    /**
     * 申请人姓名
     */
    @ApiModelProperty(example = "0", value = "申请人姓名")
    private String applicantName;

    /**
     * 申请人直属部门ID
     */
    @ApiModelProperty(example = "0", value = "申请人直属部门ID")
    private String applicantOrgId;

    /**
     * 申请人直属部门名称
     */
    @ApiModelProperty(example = "0", value = "申请人直属部门名称")
    private String applicantOrgName;

    /**
     * 申请人卡id
     */
    @ApiModelProperty(example = "0", value = "申请人卡id")
    private String fxCardId;

    /**
     * 申请人银行卡号
     */
    @ApiModelProperty(example = "0", value = "申请人银行卡号")
    private String bankCardNo;

    /**
     * 卡归属银行
     */
    @ApiModelProperty(example = "0", value = "卡归属银行")
    private String bankName;

    /**
     * 发放人ID
     */
    @ApiModelProperty(example = "0", value = "发放人ID")
    private String issuedId;

    /**
     * 发放人名称
     */
    @ApiModelProperty(example = "0", value = "发放人名称")
    private String issuedName;

    /**
     * 发放时间
     */
    @ApiModelProperty(example = "0", value = "发放时间")
    private Date issuedTime;

    /**
     * 费用ID
     */
    @ApiModelProperty(example = "0", value = "费用ID")
    private String costCategoryId;

    /**
     * 费用类别名称
     */
    @ApiModelProperty(example = "0", value = "费用类别名称")
    private String costCategoryName;

    /**
     * 费用归属配置项
     */
    @ApiModelProperty(example = "0", value = "费用归属配置项")
    private Integer costAttributionOpt;

    /**
     * 费用归属
     */
    @ApiModelProperty(example = "0", value = "费用归属")
    private String costAttributions;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * true: 员工所属部门 false: 不是
     */
    @ApiModelProperty(example = "0", value = "true: 员工所属部门 false: 不是")
    private Integer employeeDept;

}
