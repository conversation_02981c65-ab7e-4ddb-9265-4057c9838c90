package com.fenbei.fx.card.web.bankcardflow.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbeitong.usercenter.api.model.dto.page.BasePageDTO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-05-20 下午3:44
 */
@Data
public class UserCardCreditGrantListReqVO extends BasePageDTO {

    /**
     * 申请单号
     */
    private String applyTransNo;

    /**
     * 申请单号（meaningNo）
     */
    private String applyMeaningNo;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 申请标题
     */
    private String applyTitle;

    /**
     * 申请人
     */
    private String applyOperationUserName;

    /**
     * 申请开始时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date beginApplyTime;

    /**
     * 申请结束时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date endApplyTime;

    /**
     * 费用归属
     */
    private String costAttribution;

    /**
     * 申请状态 1成功,0失败
     */
    private Integer applyStatus;

    /**
     * 是否查看有效金额 是 true 否 false
     */
    private Boolean avalibleAmountFlag = false;

    private Integer pageNo = 1;
}
