package com.fenbei.fx.card.web.cardmodelconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡使用模式配置 分页 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@ApiModel("国际卡使用模式配置 分页 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardModelConfigPageReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    @ApiModelProperty(example = "xxx", value = "主键ID")
    private String id;

    /**
     * 企业ID
     */
    @ApiModelProperty(example = "xxx", value = "企业ID")
    private String companyId;

    /**
     * 模式配置: 1.企业统一模式,2.人员配置使用模式
     */
    @ApiModelProperty(example = "xxx", value = "模式配置: 1.企业统一模式,2.人员配置使用模式")
    private Integer modelType;

    /**
     * 国际卡企业生效模式: 1.普通模式,2.备用金模式
     */
    @ApiModelProperty(example = "xxx", value = "国际卡企业生效模式: 1.普通模式,2.备用金模式")
    private Integer activeModel;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

}
