package com.fenbei.fx.card.web.card.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel("国际卡 退回 ResVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardCreditManagerRefundVO implements Serializable {
    /**
     * 员工
     */
    private String employeeId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 卡编码
     */
    @NotBlank
    private String fxCardId;

    /**
     * 申请事由
     */
    private String applyReason;

    /**
     * 事由
     */
    private String applyReasonDesc;

    /**
     * 是否为离职员工回收额度
     * true：是，false：不是
     */
    private boolean dismissionEmployee;
    /**
     * 关联的额度申请单和退还金额
     * key 为申请单ID
     * value 为金额
     */
    private Map<String, BigDecimal> returnAmount;
    /**
     *  退还总额
     */
    private BigDecimal refundCreditAmount;
    /**
     * 选中的额度申请单
     */
    private List<SelectedCreditApply> selectedCreditApplyList;
}
