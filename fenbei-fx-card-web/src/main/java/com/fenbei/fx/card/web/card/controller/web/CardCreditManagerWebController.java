package com.fenbei.fx.card.web.card.controller.web;

import com.fenbei.fx.card.common.constant.GlobalCoreResponseCode;
import com.fenbei.fx.card.service.card.dto.UpdateCardStatusReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerReturnReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerReturnRespDTO;
import com.fenbei.fx.card.web.card.vo.CardCreditManagerRefundVO;
import com.fenbei.fx.card.web.card.vo.UpdateCardStatusReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.converter.CardCreditManagerVOConverter;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.MessageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = {"国际卡额度管理 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/web/card/credit/")
public class CardCreditManagerWebController extends BaseController<CardCreditManagerService, CardCreditManagerVOConverter> {

    @ApiOperation(value = "额度回收")
    @RequestMapping(value = "/refund", method = {RequestMethod.POST})
    public MessageResult refund(@RequestBody  CardCreditManagerRefundVO cardCreditManagerRefundVO) {
        CardCreditManagerReturnReqDTO cardCreditManagerReturnReqDTO = new CardCreditManagerReturnReqDTO();
        cardCreditManagerReturnReqDTO.setFxCardId(cardCreditManagerRefundVO.getFxCardId());
        cardCreditManagerReturnReqDTO.setReturnAmount(cardCreditManagerRefundVO.getReturnAmount());
        cardCreditManagerReturnReqDTO.setApplyReason(cardCreditManagerRefundVO.getApplyReason());
        cardCreditManagerReturnReqDTO.setCompanyId(cardCreditManagerRefundVO.getCompanyId());
        cardCreditManagerReturnReqDTO.setEmployeeId(cardCreditManagerRefundVO.getEmployeeId());
        cardCreditManagerReturnReqDTO.setApplyReasonDesc(cardCreditManagerRefundVO.getApplyReasonDesc());
        cardCreditManagerReturnReqDTO.setOperationType(51);
        CardCreditManagerReturnRespDTO isSuccess = service.refund(cardCreditManagerReturnReqDTO);
        return responseMessage(isSuccess!=null ? GlobalCoreResponseCode.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
