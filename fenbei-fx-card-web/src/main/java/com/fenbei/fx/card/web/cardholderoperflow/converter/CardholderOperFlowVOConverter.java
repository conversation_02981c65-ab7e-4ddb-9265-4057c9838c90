package com.fenbei.fx.card.web.cardholderoperflow.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowListResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowModifyReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowPageResDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowRemoveReqDTO;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowShowResDTO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowAddReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowListReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowListResVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowModifyReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowPageReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowPageResVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowRemoveReqVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowShowResVO;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 持卡人被操作流水 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardholderOperFlowVOConverter extends BaseVOConverter<CardholderOperFlowDTO, CardholderOperFlowVO> {

    static CardholderOperFlowVOConverter me() {
        return SpringUtil.getBean(CardholderOperFlowVOConverter.class);
    }

    CardholderOperFlowAddReqDTO convertToCardholderOperFlowAddReqDTO(CardholderOperFlowAddReqVO cardholderOperFlowAddReqVO);

    CardholderOperFlowShowResVO convertToCardholderOperFlowShowResVO(CardholderOperFlowShowResDTO cardholderOperFlowShowResDTO);

    CardholderOperFlowModifyReqDTO convertToCardholderOperFlowModifyReqDTO(CardholderOperFlowModifyReqVO cardholderOperFlowModifyReqVO);

    CardholderOperFlowRemoveReqDTO convertToCardholderOperFlowRemoveReqDTO(CardholderOperFlowRemoveReqVO cardholderOperFlowRemoveReqVO);

    CardholderOperFlowListReqDTO convertToCardholderOperFlowListReqDTO(CardholderOperFlowListReqVO cardholderOperFlowReqVO);

    CardholderOperFlowPageReqDTO convertToCardholderOperFlowPageReqDTO(CardholderOperFlowPageReqVO cardholderOperFlowPageReqVO);

    CardholderOperFlowListResVO convertToCardholderOperFlowListResVO(CardholderOperFlowListResDTO cardholderOperFlowListResDTO);

    List<CardholderOperFlowListResVO> convertToCardholderOperFlowListResVOList(List<CardholderOperFlowListResDTO> cardholderOperFlowListResDTOList);

    Page<CardholderOperFlowPageResVO> convertToCardholderOperFlowPageResVOPage(Page<CardholderOperFlowPageResDTO> cardholderOperFlowPageResDTOPage);

    List<CardholderOperFlowAddReqDTO> convertToCardholderOperFlowAddReqDTOList(List<CardholderOperFlowAddReqVO> cardholderOperFlowAddReqVOList);

    List<CardholderOperFlowShowResVO> convertToCardholderOperFlowShowResVOList(List<CardholderOperFlowShowResDTO> cardholderOperFlowShowResDTOList);
}
