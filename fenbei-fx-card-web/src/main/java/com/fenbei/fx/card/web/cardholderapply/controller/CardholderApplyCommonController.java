package com.fenbei.fx.card.web.cardholderapply.controller;

import com.fenbei.fx.card.service.cardholderapply.CardholderApplyService;
import com.fenbei.fx.card.service.redis.RedissonService;
import com.fenbei.fx.card.util.ValidateCommonUtils;
import com.fenbei.fx.card.web.cardholderapply.vo.CardholderApplyCreateReqVO;
import com.fenbei.fx.card.web.common.base.CommonController;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.finhub.framework.common.service.BaseService;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ValidateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/19
 */
public class CardholderApplyCommonController<S extends BaseService, C extends BaseVOConverter> extends CommonController<S, C> {

    @DubboReference
    protected IBaseEmployeeExtService iBaseEmployeeExtService;
    protected void commonCheck(CardholderApplyCreateReqVO reqVO) {
        ValidateCommonUtils.validate(reqVO);

    }
}
