package com.fenbei.fx.card.web;

import com.fenbei.fx.card.dao.configuration.DaoConfiguration;
import com.fenbei.fx.card.service.configuration.ServiceConfiguration;
import com.fenbei.fx.card.web.configuration.WebConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.Import;

/**
 * <pre>
 * Web Server
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-28
 */
@SpringBootApplication
@Import({WebConfiguration.class, ServiceConfiguration.class, DaoConfiguration.class})
public class WebServer {

    public static void start(SpringApplicationBuilder springApplicationBuilder, String[] args) {
        springApplicationBuilder.run(args);
    }
}
