package com.fenbei.fx.card.web.cardoperflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 卡操作流水 分页 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("卡操作流水 分页 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardOperFlowPageReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private Long id;

    /**
     * 操作记录id
     */
    @ApiModelProperty(example = "0", value = "操作记录id")
    private String operFlowId;

    /**
     * 操作申请id
     */
    @ApiModelProperty(example = "0", value = "操作申请id")
    private String applyId;

    /**
     * 卡id
     */
    @ApiModelProperty(example = "0", value = "卡id")
    private String fxCardId;

    /**
     * 银行卡id
     */
    @ApiModelProperty(example = "0", value = "银行卡id")
    private String bankCardId;

    /**
     * 银行卡编号
     */
    @ApiModelProperty(example = "0", value = "银行卡编号")
    private String bankCardNo;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "0", value = "公司id")
    private String companyId;

    /**
     * 操作人id
     */
    @ApiModelProperty(example = "0", value = "操作人id")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(example = "0", value = "操作人姓名")
    private String operatorName;

    /**
     * 操作类型 1.创建 2.更新
     */
    @ApiModelProperty(example = "0", value = "操作类型 1.创建 2.更新")
    private Integer operateType;

    /**
     * 操作描述
     */
    @ApiModelProperty(example = "0", value = "操作描述")
    private String operateDesc;

    /**
     * 操作前快照
     */
    @ApiModelProperty(example = "0", value = "操作前快照")
    private String preSnapshot;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "0", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

}
