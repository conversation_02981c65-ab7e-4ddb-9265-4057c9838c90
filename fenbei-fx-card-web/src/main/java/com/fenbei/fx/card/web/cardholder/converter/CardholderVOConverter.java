package com.fenbei.fx.card.web.cardholder.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardholder.dto.CardholderDTO;
import com.fenbei.fx.card.web.cardholder.vo.CardholderVO;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import org.mapstruct.Mapper;

/**
 * 持卡人 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardholderVOConverter extends BaseVOConverter<CardholderDTO, CardholderVO> {

    static CardholderVOConverter me() {
        return SpringUtil.getBean(CardholderVOConverter.class);
    }

}
