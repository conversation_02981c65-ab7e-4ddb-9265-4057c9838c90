package com.fenbei.fx.card.web.cardauthorize.controller;

import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.cardauthorize.CardAuthorizeService;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeAddReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeListResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeModifyReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizePageResDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeRemoveReqDTO;
import com.fenbei.fx.card.service.cardauthorize.dto.CardAuthorizeShowResDTO;
import com.fenbei.fx.card.web.cardauthorize.converter.CardAuthorizeVOConverter;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeAddReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeListReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeListResVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeModifyReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizePageReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizePageResVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeRemoveReqVO;
import com.fenbei.fx.card.web.cardauthorize.vo.CardAuthorizeShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 国际卡授权表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Api(tags = {"国际卡授权表 API 接口"})
@Slf4j
@Validated
@RestController
public class CardAuthorizeController extends BaseController<CardAuthorizeService, CardAuthorizeVOConverter> {

    @ApiOperation(value = "国际卡授权表-列表")
    @RequestMapping(value = "cardAuthorize/list", method = {RequestMethod.GET})
    public ItemsResult<CardAuthorizeListResVO> list(CardAuthorizeListReqVO cardAuthorizeListReqVO) {
        CardAuthorizeListReqDTO cardAuthorizeListReqDTO = converter.convertToCardAuthorizeListReqDTO(cardAuthorizeListReqVO);

        List<CardAuthorizeListResDTO> cardAuthorizeListResDTOList = service.list(cardAuthorizeListReqDTO);
        List<CardAuthorizeListResVO> items = converter.convertToCardAuthorizeListResVOList(cardAuthorizeListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡授权表-First查询")
    @RequestMapping(value = "cardAuthorize/listOne", method = {RequestMethod.GET})
    public ItemResult<CardAuthorizeListResVO> listOne(CardAuthorizeListReqVO cardAuthorizeListReqVO) {
        CardAuthorizeListReqDTO cardAuthorizeListReqDTO = converter.convertToCardAuthorizeListReqDTO(cardAuthorizeListReqVO);

        CardAuthorizeListResDTO cardAuthorizeListResDTO = service.listOne(cardAuthorizeListReqDTO);
        CardAuthorizeListResVO item = converter.convertToCardAuthorizeListResVO(cardAuthorizeListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡授权表-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardAuthorize/page", method = {RequestMethod.GET})
    public PageResult<CardAuthorizePageResVO> page(CardAuthorizePageReqVO cardAuthorizePageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardAuthorizePageReqDTO cardAuthorizePageReqDTO = converter.convertToCardAuthorizePageReqDTO(cardAuthorizePageReqVO);

        Page<CardAuthorizePageResDTO> cardAuthorizePageResDTOPage = service.pagination(cardAuthorizePageReqDTO, current, size);
        Page<CardAuthorizePageResVO> cardAuthorizePageResVOPage = converter.convertToCardAuthorizePageResVOPage(cardAuthorizePageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardAuthorizePageResVOPage.getTotal(), cardAuthorizePageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "国际卡授权表-新增")
    @RequestMapping(value = "cardAuthorize/add", method = {RequestMethod.POST})
    public MessageResult add(CardAuthorizeAddReqVO cardAuthorizeAddReqVO) {
        CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO = converter.convertToCardAuthorizeAddReqDTO(cardAuthorizeAddReqVO);

        Boolean isSuccess = service.add(cardAuthorizeAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-新增(所有字段)")
    @RequestMapping(value = "cardAuthorize/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardAuthorizeAddReqVO cardAuthorizeAddReqVO) {
        CardAuthorizeAddReqDTO cardAuthorizeAddReqDTO = converter.convertToCardAuthorizeAddReqDTO(cardAuthorizeAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardAuthorizeAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-批量新增(所有字段)")
    @RequestMapping(value = "cardAuthorize/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardAuthorizeAddReqVO> cardAuthorizeAddReqVOList) {
        List<CardAuthorizeAddReqDTO> cardAuthorizeAddReqDTOList = converter.convertToCardAuthorizeAddReqDTOList(cardAuthorizeAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardAuthorizeAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardAuthorize/show", method = {RequestMethod.GET})
    public ItemResult<CardAuthorizeShowResVO> show(String id) {
        CardAuthorizeShowResDTO cardAuthorizeShowResDTO = service.show(id);

        CardAuthorizeShowResVO item = converter.convertToCardAuthorizeShowResVO(cardAuthorizeShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡授权表-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardAuthorize/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardAuthorizeShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardAuthorizeShowResDTO> cardAuthorizeShowResDTOList = service.showByIds(ids.getItems());

        List<CardAuthorizeShowResVO> items = converter.convertToCardAuthorizeShowResVOList(cardAuthorizeShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡授权表-更新")
    @RequestMapping(value = "cardAuthorize/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardAuthorizeModifyReqVO cardAuthorizeModifyReqVO) {
        CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO = converter.convertToCardAuthorizeModifyReqDTO(cardAuthorizeModifyReqVO);

        Boolean isSuccess = service.modify(cardAuthorizeModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-更新(所有字段)")
    @RequestMapping(value = "cardAuthorize/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardAuthorizeModifyReqVO cardAuthorizeModifyReqVO) {
        CardAuthorizeModifyReqDTO cardAuthorizeModifyReqDTO = converter.convertToCardAuthorizeModifyReqDTO(cardAuthorizeModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardAuthorizeModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardAuthorize/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardAuthorize/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡授权表-删除(参数)")
    @RequestMapping(value = "cardAuthorize/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardAuthorizeRemoveReqVO cardAuthorizeRemoveReqVO) {
        CardAuthorizeRemoveReqDTO cardAuthorizeRemoveReqDTO = converter.convertToCardAuthorizeRemoveReqDTO(cardAuthorizeRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardAuthorizeRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
