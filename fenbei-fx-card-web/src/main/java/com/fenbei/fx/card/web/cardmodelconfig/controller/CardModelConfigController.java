package com.fenbei.fx.card.web.cardmodelconfig.controller;

import com.fenbei.fx.card.common.enums.ActiveModelEnum;
import com.fenbei.fx.card.common.enums.ModelTypeEnum;
import com.fenbei.fx.card.service.cardmodelconfig.CardModelConfigService;
import com.fenbei.fx.card.service.cardmodelconfig.dto.*;
import com.fenbei.fx.card.web.cardmodelconfig.converter.CardModelConfigVOConverter;
import com.fenbei.fx.card.web.cardmodelconfig.vo.*;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 国际卡使用模式配置 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Api(tags = {"国际卡使用模式配置 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/web" )
public class CardModelConfigController extends BaseController<CardModelConfigService, CardModelConfigVOConverter> {

    @ApiOperation(value = "国际卡使用模式配置-列表")
    @RequestMapping(value = "cardModelConfig/list", method = {RequestMethod.GET})
    public ItemsResult<CardModelConfigListResVO> list(CardModelConfigListReqVO cardModelConfigListReqVO) {
        CardModelConfigListReqDTO cardModelConfigListReqDTO = converter.convertToCardModelConfigListReqDTO(cardModelConfigListReqVO);

        List<CardModelConfigListResDTO> cardModelConfigListResDTOList = service.list(cardModelConfigListReqDTO);
        List<CardModelConfigListResVO> items = converter.convertToCardModelConfigListResVOList(cardModelConfigListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡使用模式配置-列表")
    @RequestMapping(value = "cardModelConfig/haveModel", method = {RequestMethod.GET})
    public ItemsResult<EmployeeModelConfigDTO> haveModel() {
        List<EmployeeModelConfigDTO> employeeModelConfigDTOList = service.companyHaveModel();
        return responseItems(ResponseCodeEnum.SUCCESS, employeeModelConfigDTOList);
    }

    /**
     * 【web】国际卡使用模式查询
     * @return
     */
    @RequestMapping(value = "cardModelConfig/listOne", method = {RequestMethod.GET})
    public ItemR<CardModelConfigListResVO> listOne() {
        String company_id = UserAuthHolder.getCurrentUser().getCompany_id();
        CardModelConfigListReqDTO cardModelConfigListReqDTO = new CardModelConfigListReqDTO();
        cardModelConfigListReqDTO.setCompanyId(company_id);
        CardModelConfigListResDTO cardModelConfigListResDTO = service.listOne(cardModelConfigListReqDTO);
        if(Objects.isNull(cardModelConfigListResDTO)){
            cardModelConfigListResDTO = new CardModelConfigListResDTO();
            cardModelConfigListResDTO.setActiveModel(ActiveModelEnum.NORMAL.getCode());
            cardModelConfigListResDTO.setModelType(ModelTypeEnum.COMPANY_UNIFY.getCode());
        }
        CardModelConfigListResVO item = converter.convertToCardModelConfigListResVO(cardModelConfigListResDTO);
        return responseItemR(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡使用模式配置-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardModelConfig/page", method = {RequestMethod.GET})
    public PageResult<CardModelConfigPageResVO> page(CardModelConfigPageReqVO cardModelConfigPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardModelConfigPageReqDTO cardModelConfigPageReqDTO = converter.convertToCardModelConfigPageReqDTO(cardModelConfigPageReqVO);

        Page<CardModelConfigPageResDTO> cardModelConfigPageResDTOPage = service.pagination(cardModelConfigPageReqDTO, current, size);
        Page<CardModelConfigPageResVO> cardModelConfigPageResVOPage = converter.convertToCardModelConfigPageResVOPage(cardModelConfigPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardModelConfigPageResVOPage.getTotal(), cardModelConfigPageResVOPage.getRecords(), size, current);
    }

    /**
     * 【web】国际卡使用模式配置保存
     * @param cardModelConfigAddReqVO
     * @return
     */
    @RequestMapping(value = "cardModelConfig/add", method = {RequestMethod.POST})
    public MessageResult add(@RequestBody @Validated CardModelConfigAddReqVO cardModelConfigAddReqVO) {
        CardModelConfigAddReqDTO cardModelConfigAddReqDTO = converter.convertToCardModelConfigAddReqDTO(cardModelConfigAddReqVO);
        Boolean isSuccess = service.saveOrUpdate(cardModelConfigAddReqDTO);
        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 【web】人员模式默认变更
     * @param activeModel
     * @return
     */
    @RequestMapping(value = "cardModelConfig/employee/default/{activeModel}", method = {RequestMethod.GET})
    public MessageResult employeeModelDefaultChange(@PathVariable("activeModel")Integer activeModel) {
        Boolean isSuccess = service.employeeModelDefaultChange(activeModel);
        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    /**
     * 【web】人员模式默认获取
     * @param
     * @return
     */
    @RequestMapping(value = "cardModelConfig/employee/default", method = {RequestMethod.GET})
    public ItemR<Integer> employeeModelDefault() {
        Integer activeModel = service.employeeModelDefault();
        return responseItemR( ResponseCodeEnum.SUCCESS ,activeModel);
    }


    @ApiOperation(value = "国际卡使用模式配置-新增(所有字段)")
    @RequestMapping(value = "cardModelConfig/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardModelConfigAddReqVO cardModelConfigAddReqVO) {
        CardModelConfigAddReqDTO cardModelConfigAddReqDTO = converter.convertToCardModelConfigAddReqDTO(cardModelConfigAddReqVO);

        Boolean isSuccess = service.addAllColumn(cardModelConfigAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-批量新增(所有字段)")
    @RequestMapping(value = "cardModelConfig/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardModelConfigAddReqVO> cardModelConfigAddReqVOList) {
        List<CardModelConfigAddReqDTO> cardModelConfigAddReqDTOList = converter.convertToCardModelConfigAddReqDTOList(cardModelConfigAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardModelConfigAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardModelConfig/show", method = {RequestMethod.GET})
    public ItemResult<CardModelConfigShowResVO> show(String id) {
        CardModelConfigShowResDTO cardModelConfigShowResDTO = service.show(id);

        CardModelConfigShowResVO item = converter.convertToCardModelConfigShowResVO(cardModelConfigShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡使用模式配置-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardModelConfig/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardModelConfigShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardModelConfigShowResDTO> cardModelConfigShowResDTOList = service.showByIds(ids.getItems());

        List<CardModelConfigShowResVO> items = converter.convertToCardModelConfigShowResVOList(cardModelConfigShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡使用模式配置-更新")
    @RequestMapping(value = "cardModelConfig/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardModelConfigModifyReqVO cardModelConfigModifyReqVO) {
        CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO = converter.convertToCardModelConfigModifyReqDTO(cardModelConfigModifyReqVO);

        Boolean isSuccess = service.modify(cardModelConfigModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-更新(所有字段)")
    @RequestMapping(value = "cardModelConfig/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardModelConfigModifyReqVO cardModelConfigModifyReqVO) {
        CardModelConfigModifyReqDTO cardModelConfigModifyReqDTO = converter.convertToCardModelConfigModifyReqDTO(cardModelConfigModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardModelConfigModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardModelConfig/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardModelConfig/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡使用模式配置-删除(参数)")
    @RequestMapping(value = "cardModelConfig/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardModelConfigRemoveReqVO cardModelConfigRemoveReqVO) {
        CardModelConfigRemoveReqDTO cardModelConfigRemoveReqDTO = converter.convertToCardModelConfigRemoveReqDTO(cardModelConfigRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardModelConfigRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
