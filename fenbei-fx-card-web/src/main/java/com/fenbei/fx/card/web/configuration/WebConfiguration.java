package com.fenbei.fx.card.web.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@Configuration
@ComponentScan(basePackages = {"com.fenbei.fx.card.web","com.fenbei.fx.card","com.fenbeitong.finhub","com.fenbeitong.saasplus"})
public class WebConfiguration implements WebMvcConfigurer {

}
