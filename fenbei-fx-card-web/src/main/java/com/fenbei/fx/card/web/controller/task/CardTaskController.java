package com.fenbei.fx.card.web.controller.task;

import com.fenbei.fx.card.service.card.manager.CardManager;
import com.fenbei.fx.card.service.cardapply.manager.CardApplyManager;
import com.fenbeitong.finhub.auth.annotation.FinhubExcludeAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * Created by FBT on 2023/4/19.
 */

@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/task/card/" )
@FinhubExcludeAuth
public class CardTaskController {

    @RequestMapping(value = "/queryPendingTask", method = {RequestMethod.POST})
    public String queryPendingTask() {
        CompletableFuture.runAsync(() -> {

            CardApplyManager.me().dealPendingApplyInfo();

        });
        return "success";
    }


    @RequestMapping(value = "/dayCheckTask", method = {RequestMethod.POST})
    public String list() {
        CompletableFuture.runAsync(() -> {

            CardManager.me().dayCheckTask();

        });
        return "success";
    }

}
