package com.fenbei.fx.card.web.cardapply.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 国际卡操作申请 列表 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("国际卡操作申请 列表 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardApplyListReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private Long id;

    /**
     * 操作申请id
     */
    @ApiModelProperty(example = "0", value = "操作申请id")
    private String applyId;

    /**
     * 卡id
     */
    @ApiModelProperty(example = "0", value = "卡id")
    private String fxCardId;

    /**
     * 银行卡id
     */
    @ApiModelProperty(example = "0", value = "银行卡id")
    private String bankCardId;

    /**
     * 银行卡编号
     */
    @ApiModelProperty(example = "0", value = "银行卡编号")
    private String bankCardNo;

    /**
     * 公司账户id
     */
    @ApiModelProperty(example = "0", value = "公司账户id")
    private String companyAccountId;

    /**
     * 申请类型 1.创建 2.更新
     */
    @ApiModelProperty(example = "0", value = "申请类型 1.创建 2.更新")
    private Integer applyType;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功
     */
    @ApiModelProperty(example = "0", value = "申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功")
    private Integer applyStatus;

    /**
     * 发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL
     */
    @ApiModelProperty(example = "0", value = "发给谁企业或者个人：1-ORGANISATION 2-INDIVIDUAL")
    private Integer cardIssueTo;

    /**
     * 卡片形式：1-PHYSICAL、2-VIRTUAL
     */
    @ApiModelProperty(example = "0", value = "卡片形式：1-PHYSICAL、2-VIRTUAL")
    private Integer cardFormFactor;

    /**
     * 卡的cvv
     */
    @ApiModelProperty(example = "0", value = "卡的cvv")
    private String cardCvv;

    /**
     * 卡的到期年份
     */
    @ApiModelProperty(example = "0", value = "卡的到期年份")
    private String cardExpiryYear;

    /**
     * 卡的到期月份
     */
    @ApiModelProperty(example = "0", value = "卡的到期月份")
    private String cardExpiryMonth;

    /**
     * 卡片上的姓名
     */
    @ApiModelProperty(example = "0", value = "卡片上的姓名")
    private String nameOnCard;

    /**
     * 发卡渠道 AIRWALLEX
     */
    @ApiModelProperty(example = "0", value = "发卡渠道 AIRWALLEX")
    private String cardPlatform;

    /**
     * 发卡的品牌 VISA
     */
    @ApiModelProperty(example = "0", value = "发卡的品牌 VISA")
    private String cardBrand;

    /**
     * 发卡时间
     */
    @ApiModelProperty(example = "0", value = "发卡时间")
    private Date cardPublicTime;

    /**
     * 持卡人id
     */
    @ApiModelProperty(example = "0", value = "持卡人id")
    private String fxCardholderId;

    /**
     * 区号
     */
    @ApiModelProperty(example = "0", value = "区号")
    private String nationCode;

    /**
     * 申请人手机号+区号
     */
    @ApiModelProperty(example = "0", value = "申请人手机号+区号")
    private String applyerPhone;

    /**
     * 申请人名
     */
    @ApiModelProperty(example = "0", value = "申请人名")
    private String applyerFirstName;

    /**
     * 申请人姓
     */
    @ApiModelProperty(example = "0", value = "申请人姓")
    private String applyerLastName;

    /**
     * 卡用途
     */
    @ApiModelProperty(example = "0", value = "卡用途")
    private String cardPurpose;

    /**
     * 审批人
     */
    @ApiModelProperty(example = "0", value = "审批人")
    private String approveUserId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(example = "0", value = "审批人姓名")
    private String approveUserName;

    /**
     * 审批通过时间
     */
    @ApiModelProperty(example = "0", value = "审批通过时间")
    private Date approveTime;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "0", value = "币种 美元-USD")
    private String currency;

    /**
     * 管控规则：频率，币种，金额
     */
    @ApiModelProperty(example = "0", value = "管控规则：频率，币种，金额")
    private String cardLimits;

    /**
     * 创建人
     */
    @ApiModelProperty(example = "0", value = "创建人")
    private String createUserId;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "0", value = "公司id")
    private String companyId;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "0", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

}
