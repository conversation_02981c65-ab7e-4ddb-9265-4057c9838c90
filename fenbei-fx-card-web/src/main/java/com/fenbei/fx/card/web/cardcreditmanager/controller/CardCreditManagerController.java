package com.fenbei.fx.card.web.cardcreditmanager.controller;

import com.fenbei.fx.card.common.enums.CardPlatformCaseEnum;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.ItemResult;
import com.finhub.framework.web.vo.ItemsResult;
import com.finhub.framework.web.vo.ListParam;
import com.finhub.framework.web.vo.MessageResult;
import com.finhub.framework.web.vo.PageResult;

import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerAddReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerListResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerModifyReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerPageResDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerRemoveReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerShowResDTO;
import com.fenbei.fx.card.web.cardcreditmanager.converter.CardCreditManagerVOConverter;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerAddReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerListReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerListResVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerModifyReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerPageReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerPageResVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerRemoveReqVO;
import com.fenbei.fx.card.web.cardcreditmanager.vo.CardCreditManagerShowResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 国际卡额度申请退回管理表 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-22
 */
@Api(tags = {"国际卡额度申请退回管理表 API 接口"})
@Slf4j
@Validated
@RestController
public class CardCreditManagerController extends BaseController<CardCreditManagerService, CardCreditManagerVOConverter> {

    @ApiOperation(value = "国际卡额度申请退回管理表-列表")
    @RequestMapping(value = "cardCreditManager/list", method = {RequestMethod.GET})
    public ItemsResult<CardCreditManagerListResVO> list(CardCreditManagerListReqVO cardCreditManagerListReqVO) {
        CardCreditManagerListReqDTO cardCreditManagerListReqDTO = converter.convertToCardCreditManagerListReqDTO(cardCreditManagerListReqVO);

        List<CardCreditManagerListResDTO> cardCreditManagerListResDTOList = service.list(cardCreditManagerListReqDTO);
        List<CardCreditManagerListResVO> items = converter.convertToCardCreditManagerListResVOList(cardCreditManagerListResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-First查询")
    @RequestMapping(value = "cardCreditManager/listOne", method = {RequestMethod.GET})
    public ItemResult<CardCreditManagerListResVO> listOne(CardCreditManagerListReqVO cardCreditManagerListReqVO) {
        CardCreditManagerListReqDTO cardCreditManagerListReqDTO = converter.convertToCardCreditManagerListReqDTO(cardCreditManagerListReqVO);

        CardCreditManagerListResDTO cardCreditManagerListResDTO = service.listOne(cardCreditManagerListReqDTO);
        CardCreditManagerListResVO item = converter.convertToCardCreditManagerListResVO(cardCreditManagerListResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "当前页码", paramType = "query", dataTypeClass = Integer.class, example = "2", defaultValue = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页大小", paramType = "query", dataTypeClass = Integer.class, example = "20", defaultValue = "10")
    })
    @RequestMapping(value = "cardCreditManager/page", method = {RequestMethod.GET})
    public PageResult<CardCreditManagerPageResVO> page(CardCreditManagerPageReqVO cardCreditManagerPageReqVO, Integer pageNo, Integer pageSize) {
        int current = Func.isNullOrZero(pageNo) ? 1 : pageNo;
        int size = Func.isNullOrZero(pageSize) ? 10 : pageSize;
        CardCreditManagerPageReqDTO cardCreditManagerPageReqDTO = converter.convertToCardCreditManagerPageReqDTO(cardCreditManagerPageReqVO);

        Page<CardCreditManagerPageResDTO> cardCreditManagerPageResDTOPage = service.pagination(cardCreditManagerPageReqDTO, current, size);
        Page<CardCreditManagerPageResVO> cardCreditManagerPageResVOPage = converter.convertToCardCreditManagerPageResVOPage(cardCreditManagerPageResDTOPage);

        return responsePage(ResponseCodeEnum.SUCCESS, cardCreditManagerPageResVOPage.getTotal(), cardCreditManagerPageResVOPage.getRecords(), size, current);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-新增")
    @RequestMapping(value = "cardCreditManager/add", method = {RequestMethod.POST})
    public MessageResult add(CardCreditManagerAddReqVO cardCreditManagerAddReqVO) {
        CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO = converter.convertToCardCreditManagerAddReqDTO(cardCreditManagerAddReqVO);

        CardPlatformCaseEnum enumByFxAcctChannel = CardPlatformCaseEnum.getEnumByFxAcctChannel(cardCreditManagerAddReqDTO.getCardPlatform());
        cardCreditManagerAddReqDTO.setCurrency(StringUtils.isNotBlank(cardCreditManagerAddReqDTO.getCurrency())?cardCreditManagerAddReqDTO.getCurrency():enumByFxAcctChannel.getCurrencyEnum().getCurrencyCode());

        Boolean isSuccess = service.add(cardCreditManagerAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-新增(所有字段)")
    @RequestMapping(value = "cardCreditManager/addAllColumn", method = {RequestMethod.POST})
    public MessageResult addAllColumn(CardCreditManagerAddReqVO cardCreditManagerAddReqVO) {
        CardCreditManagerAddReqDTO cardCreditManagerAddReqDTO = converter.convertToCardCreditManagerAddReqDTO(cardCreditManagerAddReqVO);

        CardPlatformCaseEnum enumByFxAcctChannel = CardPlatformCaseEnum.getEnumByFxAcctChannel(cardCreditManagerAddReqDTO.getCardPlatform());
        cardCreditManagerAddReqDTO.setCurrency(StringUtils.isNotBlank(cardCreditManagerAddReqDTO.getCurrency())?cardCreditManagerAddReqDTO.getCurrency():enumByFxAcctChannel.getCurrencyEnum().getCurrencyCode());


        Boolean isSuccess = service.addAllColumn(cardCreditManagerAddReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-批量新增(所有字段)")
    @RequestMapping(value = "cardCreditManager/addBatchAllColumn", method = {RequestMethod.POST})
    public MessageResult addBatchAllColumn(List<CardCreditManagerAddReqVO> cardCreditManagerAddReqVOList) {
        List<CardCreditManagerAddReqDTO> cardCreditManagerAddReqDTOList = converter.convertToCardCreditManagerAddReqDTOList(cardCreditManagerAddReqVOList);

        Boolean isSuccess = service.addBatchAllColumn(cardCreditManagerAddReqDTOList);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardCreditManager/show", method = {RequestMethod.GET})
    public ItemResult<CardCreditManagerShowResVO> show(String id) {
        CardCreditManagerShowResDTO cardCreditManagerShowResDTO = service.show(id);

        CardCreditManagerShowResVO item = converter.convertToCardCreditManagerShowResVO(cardCreditManagerShowResDTO);

        return responseItem(ResponseCodeEnum.SUCCESS, item);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-详情(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardCreditManager/showByIds", method = {RequestMethod.POST})
    public ItemsResult<CardCreditManagerShowResVO> showByIds(@RequestBody ListParam<String> ids) {
        List<CardCreditManagerShowResDTO> cardCreditManagerShowResDTOList = service.showByIds(ids.getItems());

        List<CardCreditManagerShowResVO> items = converter.convertToCardCreditManagerShowResVOList(cardCreditManagerShowResDTOList);

        return responseItems(ResponseCodeEnum.SUCCESS, items);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-更新")
    @RequestMapping(value = "cardCreditManager/modify", method = {RequestMethod.POST})
    public MessageResult modify(CardCreditManagerModifyReqVO cardCreditManagerModifyReqVO) {
        CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = converter.convertToCardCreditManagerModifyReqDTO(cardCreditManagerModifyReqVO);

        Boolean isSuccess = service.modify(cardCreditManagerModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-更新(所有字段)")
    @RequestMapping(value = "cardCreditManager/modifySelective", method = {RequestMethod.POST})
    public MessageResult modifyAllColumn(CardCreditManagerModifyReqVO cardCreditManagerModifyReqVO) {
        CardCreditManagerModifyReqDTO cardCreditManagerModifyReqDTO = converter.convertToCardCreditManagerModifyReqDTO(cardCreditManagerModifyReqVO);

        Boolean isSuccess = service.modifyAllColumn(cardCreditManagerModifyReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-删除")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "主键 ID", paramType = "query", dataTypeClass = String.class, example = "1001")
    })
    @RequestMapping(value = "cardCreditManager/remove", method = {RequestMethod.POST})
    public MessageResult remove(String id) {
        Boolean isSuccess = service.remove(id);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-删除(批量)")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "ids", value = "主键 ID 集合")
    })
    @RequestMapping(value = "cardCreditManager/removeBatch", method = {RequestMethod.POST})
    public MessageResult removeBatch(@RequestBody ListParam<String> ids) {
        Boolean isSuccess = service.removeBatch(ids.getItems());

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

    @ApiOperation(value = "国际卡额度申请退回管理表-删除(参数)")
    @RequestMapping(value = "cardCreditManager/removeByParams", method = {RequestMethod.POST})
    public MessageResult removeByParams(CardCreditManagerRemoveReqVO cardCreditManagerRemoveReqVO) {
        CardCreditManagerRemoveReqDTO cardCreditManagerRemoveReqDTO = converter.convertToCardCreditManagerRemoveReqDTO(cardCreditManagerRemoveReqVO);

        Boolean isSuccess = service.removeByParams(cardCreditManagerRemoveReqDTO);

        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }
}
