package com.fenbei.fx.card.web.cardholder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 持卡人 修改 ReqVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("持卡人 修改 ReqVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderModifyReqVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    //private Long id;

    /**
     * 申请单id，如果是编辑，传值
     */
    private String applyId;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    /**
     * 申请人名
     */
    @NotBlank
    private String firstName;

    /**
     * 申请人姓
     */
    @NotBlank
    private String lastName;

    /**
     * email地址
     */
    @NotBlank
    private String email;

    /**
     * 出生日期，格式为YYY-MM-DD
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birth;


    /**
     * 区号
     */
    private String nationCode;

    /**
     * 持卡人手机号
     */
    @NotBlank
    private String phone;

    /**
     * 展示状态，根据此状态判断是申请单还是持卡人
     * 1：待审核 2：更新中 4：更新失败3：生效中 5：已失效
     */
    @NotNull
    private Integer showStatus;

    /**
     * 证件的国家:US
     */
    @NotBlank
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    @NotNull
    private Integer identificationType;

    /**
     * 证件号
     */
    @NotBlank
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    private String identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围 2-长期有效
     */
    @NotNull
    private Integer identificationExpiryType;

    /**
     * 地区
     */
    @NotNull
    private AddressDto addressDto;

    /**
     * 邮寄地区
     */
    private AddressDto postalAddressDto;

}
