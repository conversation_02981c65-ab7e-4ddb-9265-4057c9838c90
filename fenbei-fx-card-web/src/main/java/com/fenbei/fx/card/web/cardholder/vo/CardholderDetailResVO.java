package com.fenbei.fx.card.web.cardholder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fenbei.fx.card.service.cardholderapply.dto.AddressDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/23
 */
@Data
public class CardholderDetailResVO implements Serializable {
    private static final long serialVersionUID = 8294975763387927213L;

    private String bizId;
    private String name;

    private String phone;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birth;

    private AddressDto address;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    private Integer identificationType;

    /**
     * 证件号
     */
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围 2-长期有效
     */
    private Integer identificationExpiryType;

    private AddressDto postalAddress;

    /**
     * 展示的状态描述
     */
    private String showStatusStr;

    /**
     * 展示的状态
     */
    private Integer showStatus;
}
