package com.fenbei.fx.card.web.cardcreditapplyorder.controller;

import com.alibaba.fastjson.JSONObject;
import com.fenbei.fx.card.common.enums.ActiveModelEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderStateEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderSubTypeEnum;
import com.fenbei.fx.card.common.enums.ApplyOrderTypeEnum;
import com.fenbei.fx.card.service.cardcreditapplyorder.dto.*;
import com.fenbei.fx.card.util.BigDecimalUtils;
import com.fenbei.fx.card.util.BizIdUtils;
import com.fenbei.fx.card.web.cardcreditapplyorder.converter.CardCreditApplyOrderVOConverter;
import com.fenbeitong.finhub.common.saas.entity.CostAttribution;
import com.fenbeitong.finhub.common.saas.entity.CostAttributionGroup;
import com.fenbeitong.finhub.common.saas.entity.CostInfo;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.fenbeitong.finhub.auth.UserAuthHolder;
import com.fenbeitong.finhub.auth.entity.base.UserComInfoVO;
import com.finhub.framework.web.controller.BaseController;
import com.finhub.framework.web.vo.*;

import com.fenbei.fx.card.service.cardcreditapplyorder.CardCreditApplyOrderService;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 国际卡额度发放单 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-08
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/fxcard/web/card/creditorder")
public class CardCreditApplyOrderController extends BaseController<CardCreditApplyOrderService, CardCreditApplyOrderVOConverter> {





    /**
     * 国际卡额度发放单-分页
     *
     * @param cardCreditApplyOrderPageReqDTO 国际卡额度发放单分页请求DTO
     * @return PageR<CardCreditApplyOrderPageResDTO> 分页结果
     * @undone
     */
    @ApiOperation(value = "国际卡额度下发单管理表-分页")
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    public PageResult<CardCreditApplyOrderPageResDTO>  page(@RequestBody CardCreditApplyOrderPageReqDTO cardCreditApplyOrderPageReqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        cardCreditApplyOrderPageReqDTO.setCompanyId(user.getCompany_id());
        FinhubLogger.info("【web端-额度下发单列表】请求参数{}", JSONObject.toJSON(cardCreditApplyOrderPageReqDTO));
        if(ApplyOrderStateEnum.isAll(cardCreditApplyOrderPageReqDTO.getApplyState())){
            cardCreditApplyOrderPageReqDTO.setApplyState(null);
        }
        Page<CardCreditApplyOrderPageResDTO> cardCreditApplyOrderPageResDTOPage = new Page<>();
        cardCreditApplyOrderPageResDTOPage = service.paginationByCondition(cardCreditApplyOrderPageReqDTO, cardCreditApplyOrderPageReqDTO.getPageIndex(), cardCreditApplyOrderPageReqDTO.getPageSize());
        List<CardCreditApplyOrderPageResDTO> records = cardCreditApplyOrderPageResDTOPage.getRecords();
        for (CardCreditApplyOrderPageResDTO record : records) {
            record.setApplyAmount(BigDecimalUtils.fenToYuan(record.getApplyAmount()));
            record.setApplyOrderSubTypeDesc(ApplyOrderSubTypeEnum.getDescByType(record.getApplyOrderSubType()));
            record.setApplyOrderTypeDesc(ApplyOrderTypeEnum.getDescByType(record.getApplyOrderType()));
            record.setApplyStateDesc(ApplyOrderStateEnum.getDescByCode(record.getApplyState()));
            record.setApproveStateDesc("-");
            record.setActiveModelDesc(ActiveModelEnum.getEnum(record.getActiveModel()).getName());
            String costAttributions = record.getCostAttributions();
            if (ObjUtils.isEmpty(costAttributions)) {
                log.warn("费用归属为空, detailVo = {}", JSONObject.toJSON(costAttributions));
                record.setCostAttributeName("-");
            } else {
                try {
                    CostInfo costInfo = JsonUtils.toObj(costAttributions, CostInfo.class);
                    List<CostAttributionGroup> costAttributionGroupList = costInfo.getCostAttributionGroupList();
                    if (ObjUtils.isNotEmpty(costAttributionGroupList)) {
                        StringBuffer splitName = new StringBuffer();
                        for (CostAttributionGroup costAttributionGroup : costAttributionGroupList) {
                            if(costAttributionGroup!=null){
                                List<CostAttribution> costAttributionList = costAttributionGroup.getCostAttributionList();
                                if (ObjUtils.isNotEmpty(costAttributionList)) {
                                    splitName.append(costAttributionGroup.getCategoryName());
                                    splitName.append(":");
                                    splitName.append(costAttributionList.get(0).getName());
                                    splitName.append(";");
                                }
                            }
                        }
                        record.setCostAttributeName(StringUtils.substring(splitName.toString(), 0, splitName.length() - 1));
                    }else{
                        record.setCostAttributeName("-");
                    }
                }catch (Exception e){
                    log.error("【web端-额度下发单列表】查询异常", e);
                    record.setCostAttributeName("-");
                }
            }
        }
        FinhubLogger.info("【web端-额度下发单列表】查询结果{}", JSONObject.toJSON(cardCreditApplyOrderPageReqDTO));
        return responsePage(ResponseCodeEnum.SUCCESS, cardCreditApplyOrderPageResDTOPage.getTotal(), cardCreditApplyOrderPageResDTOPage.getRecords(), cardCreditApplyOrderPageReqDTO.getPageSize(), cardCreditApplyOrderPageReqDTO.getPageIndex());
    }


    /**
     * 国际卡额度发放单-新增
     *
     * @param cardCreditApplyOrderAddReqVO 国际卡额度发放单新增请求DTO
     * @return MessageR 新增结果
     * @undone
     */
    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    public MessageResult add(@RequestBody CardCreditApplyOrderAddReqVO cardCreditApplyOrderAddReqVO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String loginEmployeeId = user.getUser_id();
        String loginEmployeeName = user.getUser_name();
        String companyId = user.getCompany_id();
        //设置公司id
        cardCreditApplyOrderAddReqVO.setCompanyId(companyId);
        //创建人
        cardCreditApplyOrderAddReqVO.setCreaterId(StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getCreaterId())?loginEmployeeId:cardCreditApplyOrderAddReqVO.getCreaterId());
        cardCreditApplyOrderAddReqVO.setCreaterName(StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getCreaterName())?loginEmployeeName:cardCreditApplyOrderAddReqVO.getCreaterName());

        //发放人
        cardCreditApplyOrderAddReqVO.setIssuedId(StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getIssuedId())?loginEmployeeId:cardCreditApplyOrderAddReqVO.getIssuedId());
        cardCreditApplyOrderAddReqVO.setIssuedName(StringUtils.isBlank(cardCreditApplyOrderAddReqVO.getIssuedName())?loginEmployeeName:cardCreditApplyOrderAddReqVO.getIssuedName());

        cardCreditApplyOrderAddReqVO.setApplyOrderType(ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal());
        cardCreditApplyOrderAddReqVO.setApplyOrderSubType(ApplyOrderSubTypeEnum.QUOTA_GRANT.getTypeReal());

        String fxCreditApplyOrderId = BizIdUtils.getFxCreditApplyOrderId();
        cardCreditApplyOrderAddReqVO.setApplyId(fxCreditApplyOrderId);
        cardCreditApplyOrderAddReqVO.setMeaningNo(fxCreditApplyOrderId);
        cardCreditApplyOrderAddReqVO.setApplyOrderId(fxCreditApplyOrderId);

        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = service.createApplyOrder(cardCreditApplyOrderAddReqVO);

        return responseMessage(ApplyOrderStateEnum.isSuccess(cardCreditApplyOrderDTO.getApplyState()) ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR,ApplyOrderStateEnum.isSuccess(cardCreditApplyOrderDTO.getApplyState())?ResponseCodeEnum.SUCCESS.getMessage():cardCreditApplyOrderDTO.getApplyResultDesc());
    }


    /**
     * 国际卡额度发放单-更新
     *
     * @param creditApplyOrderModifyReqVO 国际卡额度发放单更新请求DTO
     * @return MessageR 更新结果
     * @undone
     */
    @RequestMapping(value = "/modify", method = {RequestMethod.POST})
    public MessageResult modify(@RequestBody CardCreditApplyOrderModifyReqVO creditApplyOrderModifyReqVO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String loginEmployeeId = user.getUser_id();
        String loginEmployeeName = user.getUser_name();
        String companyId = user.getCompany_id();
        //设置公司id
        //设置制单人和发放人
        creditApplyOrderModifyReqVO.setCreaterId(loginEmployeeId);
        creditApplyOrderModifyReqVO.setCreaterName(loginEmployeeName);
        if (StringUtils.isBlank(creditApplyOrderModifyReqVO.getIssuedId())) {
            creditApplyOrderModifyReqVO.setIssuedId(loginEmployeeId);
        }
        if (StringUtils.isBlank(creditApplyOrderModifyReqVO.getIssuedName())) {
            creditApplyOrderModifyReqVO.setIssuedName(loginEmployeeName);
        }

        creditApplyOrderModifyReqVO.setApplyOrderType(ApplyOrderTypeEnum.QUOTA_GRANT.getTypeReal());
        creditApplyOrderModifyReqVO.setApplyOrderSubType(ApplyOrderSubTypeEnum.QUOTA_GRANT.getTypeReal());
        CardCreditApplyOrderDTO cardCreditApplyOrderDTO = service.modifyByApplyOrderId(companyId,creditApplyOrderModifyReqVO);
        return responseMessage(ApplyOrderStateEnum.isSuccess(cardCreditApplyOrderDTO.getApplyState()) ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR,ApplyOrderStateEnum.isSuccess(cardCreditApplyOrderDTO.getApplyState())?ResponseCodeEnum.SUCCESS.getMessage():cardCreditApplyOrderDTO.getApplyResultDesc());
    }

    /**
     * 国际卡额度发放单-更新后重新发放
     *
     * @param cardCreditApplyOrderTrySendReqDTO 国际卡额度发放单更新请求DTO
     * @return MessageR 更新结果
     * @undone
     */
    @RequestMapping(value = "/trysend", method = {RequestMethod.POST})
    public MessageResult trySend(@RequestBody CardCreditApplyOrderTrySendReqVO cardCreditApplyOrderTrySendReqDTO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        CardCreditApplyOrderTrySendResVO trySendResVO = service.trySend(companyId,cardCreditApplyOrderTrySendReqDTO);
        return responseMessage(ApplyOrderStateEnum.isSuccess(trySendResVO.getApplyState()) ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR,ApplyOrderStateEnum.isSuccess(trySendResVO.getApplyState())?ResponseCodeEnum.SUCCESS.getMessage():trySendResVO.getApplyResultDesc());

    }

    /**
     * 国际卡额度发放单-批量发放
     *
     * @param batchTrySendReqVO 批量发放请求DTO
     * @return ItemResult<CardCreditApplyOrderBatchTrySendResVO> 批量发放结果
     */
    @ApiOperation("国际卡额度发放单批量发放")
    @RequestMapping(value = "/batch_trysend", method = {RequestMethod.POST})
    public ItemResult<CardCreditApplyOrderBatchTrySendResVO> batchTrySend(@RequestBody CardCreditApplyOrderBatchTrySendReqVO batchTrySendReqVO) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();

        CardCreditApplyOrderBatchTrySendResVO batchResult = service.batchTrySend(companyId, batchTrySendReqVO);

        // 根据批量发放结果判断整体状态
        ResponseCodeEnum responseCode = batchResult.getSuccessCount() > 0 ?
            ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR;

        String message = String.format("批量发放完成：总数 %d，成功 %d，失败 %d",
            batchResult.getTotalCount(), batchResult.getSuccessCount(), batchResult.getFailCount());

        return responseItem(responseCode, message, batchResult);
    }


    /**
     * 国际卡额度发放单-详情
     *
     * @param applyOrderId 主键 ID
     * @return ItemR<CardCreditApplyOrderShowResDTO> 详情结果
     * @undone
     */
    @ApiOperation("国际卡额度发放单详情")
    @RequestMapping(value = "/show", method = {RequestMethod.GET})
    public ItemResult<CardCreditApplyOrderShowResDTO> show(String applyOrderId) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        return responseItem(ResponseCodeEnum.SUCCESS, service.showByApplyOrderId(companyId,applyOrderId));
    }


    @ApiOperation(value = "国际卡额度发放单-删除")
    @RequestMapping(value = "/remove", method = {RequestMethod.GET})
    public MessageResult remove(String applyOrderId) {
        UserComInfoVO user = UserAuthHolder.getCurrentUser();
        String companyId = user.getCompany_id();
        Boolean isSuccess = service.removeByApplyOrderId(companyId,applyOrderId);
        return responseMessage(isSuccess ? ResponseCodeEnum.SUCCESS : ResponseCodeEnum.INTERNAL_ERROR);
    }

}
