package com.fenbei.fx.card.web.cardholder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/20
 */
@Data
public class CardholderByPageResVO implements Serializable {
    private static final long serialVersionUID = 1128647178359387774L;

    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 持卡人id
     */
    private String fxCardholderId;

    private String name;

    private String phone;

    /**
     * 展示的状态描述
     */
    private String statusStr;

    /**
     * 展示的状态
     */
    private Integer status;

    /**
     * 展示的状态描述
     */
    private String showStatusStr;

    /**
     * 展示的状态
     */
    private Integer showStatus;

    //@JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
