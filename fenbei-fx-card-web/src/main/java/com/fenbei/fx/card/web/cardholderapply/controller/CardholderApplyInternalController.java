package com.fenbei.fx.card.web.cardholderapply.controller;

import com.fenbei.fx.card.service.cardholderapply.CardholderApplyService;
import com.fenbei.fx.card.service.cardholderapply.dto.*;
import com.fenbei.fx.card.service.cardholderoperflow.dto.CardholderOperFlowAddReqDTO;
import com.fenbei.fx.card.web.cardholderapply.converter.CardholderApplyVOConverter;
import com.fenbei.fx.card.web.cardholderapply.vo.*;
import com.fenbei.fx.card.web.cardholderoperflow.vo.CardholderOperFlowAddReqVO;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.finhub.framework.core.Func;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.core.page.Page;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 持卡人操作申请内部 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-18
 */
@Api(tags = {"持卡人操作申请内部 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping("/fxcard/internal/cardholderApply")
public class CardholderApplyInternalController extends ControllerSupport {

    /**
     * 进行中状态的持卡人申请轮询
     * @return
     */
    @RequestMapping(value = "/handleBankDealingData")
    public MessageResult add() {
        CompletableFuture.runAsync(()->{
            CardholderApplyService.me().handleBankDealingData();
        });
        return responseMessage(ResponseCodeEnum.SUCCESS);
    }


}
