package com.fenbei.fx.card.web.cardverificationflow.converter;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardverificationflow.dto.*;
import com.fenbei.fx.card.web.cardverificationflow.vo.*;
import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 核销记录表 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-07
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardVerificationFlowVOConverter extends BaseVOConverter<CardVerificationFlowDTO, CardVerificationFlowVO> {

    static CardVerificationFlowVOConverter me() {
        return SpringUtil.getBean(CardVerificationFlowVOConverter.class);
    }

    CardVerificationFlowAddReqDTO convertToCardVerificationFlowAddReqDTO(CardVerificationFlowAddReqVO cardVerificationFlowAddReqVO);

    CardVerificationFlowShowResVO convertToCardVerificationFlowShowResVO(CardVerificationFlowShowResDTO cardVerificationFlowShowResDTO);

    CardVerificationFlowModifyReqDTO convertToCardVerificationFlowModifyReqDTO(CardVerificationFlowModifyReqVO cardVerificationFlowModifyReqVO);

    CardVerificationFlowRemoveReqDTO convertToCardVerificationFlowRemoveReqDTO(CardVerificationFlowRemoveReqVO cardVerificationFlowRemoveReqVO);

    CardVerificationFlowListReqDTO convertToCardVerificationFlowListReqDTO(CardVerificationFlowListReqVO cardVerificationFlowReqVO);

    CardVerificationFlowPageReqDTO convertToCardVerificationFlowPageReqDTO(CardVerificationFlowPageReqVO cardVerificationFlowPageReqVO);

    CardVerificationFlowListResVO convertToCardVerificationFlowListResVO(CardVerificationFlowListResDTO cardVerificationFlowListResDTO);

    List<CardVerificationFlowListResVO> convertToCardVerificationFlowListResVOList(List<CardVerificationFlowListResDTO> cardVerificationFlowListResDTOList);

    Page<CardVerificationFlowPageResVO> convertToCardVerificationFlowPageResVOPage(Page<CardVerificationFlowPageResDTO> cardVerificationFlowPageResDTOPage);

    List<CardVerificationFlowAddReqDTO> convertToCardVerificationFlowAddReqDTOList(List<CardVerificationFlowAddReqVO> cardVerificationFlowAddReqVOList);

    List<CardVerificationFlowShowResVO> convertToCardVerificationFlowShowResVOList(List<CardVerificationFlowShowResDTO> cardVerificationFlowShowResDTOList);
}
