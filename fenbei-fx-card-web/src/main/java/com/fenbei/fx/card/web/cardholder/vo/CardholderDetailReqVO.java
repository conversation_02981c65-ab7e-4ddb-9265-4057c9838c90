package com.fenbei.fx.card.web.cardholder.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/23
 */
@Data
public class CardholderDetailReqVO implements Serializable {
    private static final long serialVersionUID = -1177759596265351458L;

    /**
     * 持卡人id
     */
    private String applyId;

    /**
     * 申请单id
     */
    private String fxCardholderId;

    /**
     * 和前端约定的状态
     * @see com.fenbei.fx.card.common.enums.CardholderApplyStatusEnum
     * @see com.fenbei.fx.card.common.enums.CardholderShowStatusEnum
     */
    @NotNull
    private Integer showStatus;

}
