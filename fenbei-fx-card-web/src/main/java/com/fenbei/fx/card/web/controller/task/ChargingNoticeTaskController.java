package com.fenbei.fx.card.web.controller.task;

import com.alibaba.fastjson.JSON;
import com.fenbei.fx.card.service.cardchargingnotice.CardChargingNoticeService;
import com.fenbei.fx.card.service.cardchargingnotice.dto.CardChargingNoticeRetryDTO;
import com.fenbeitong.finhub.auth.annotation.FinhubExcludeAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023-06-06 下午5:03
 */
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/task/charging/" )
@FinhubExcludeAuth
public class ChargingNoticeTaskController {

    @Autowired
    private CardChargingNoticeService cardChargingNoticeService;

    @RequestMapping(value = "/notice", method = {RequestMethod.POST})
    public String notice(@RequestParam("jobConfig") String jobConfig) {
        CompletableFuture.runAsync(() -> {
            CardChargingNoticeRetryDTO cardChargingNoticeRetryDTO = JSON.parseObject(jobConfig, CardChargingNoticeRetryDTO.class);
            cardChargingNoticeService.noticeRetry(cardChargingNoticeRetryDTO);
        });
        return "success";
    }

}
