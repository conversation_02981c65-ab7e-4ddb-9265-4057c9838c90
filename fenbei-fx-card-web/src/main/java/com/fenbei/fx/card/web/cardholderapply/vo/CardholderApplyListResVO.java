package com.fenbei.fx.card.web.cardholderapply.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 持卡人操作申请 列表 ResVO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-19
 */
@ApiModel("持卡人操作申请 列表 ResVO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardholderApplyListResVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * id
     */
    @ApiModelProperty(example = "0", value = "id")
    private Long id;

    /**
     * 操作申请id
     */
    @ApiModelProperty(example = "0", value = "操作申请id")
    private String applyId;

    /**
     * 持卡人id
     */
    @ApiModelProperty(example = "0", value = "持卡人id")
    private String fxCardholderId;

    /**
     * 渠道方持卡人id
     */
    @ApiModelProperty(example = "0", value = "渠道方持卡人id")
    private String bankCardholderId;

    /**
     * 员工id
     */
    @ApiModelProperty(example = "0", value = "员工id")
    private String employeeId;

    /**
     * 员工名称
     */
    @ApiModelProperty(example = "0", value = "员工名称")
    private String employeeName;

    /**
     * 公司id
     */
    @ApiModelProperty(example = "0", value = "公司id")
    private String companyId;

    /**
     * 申请类型 1.创建 2.更新
     */
    @ApiModelProperty(example = "0", value = "申请类型 1.创建 2.更新")
    private Integer applyType;

    /**
     * 申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功
     */
    @ApiModelProperty(example = "0", value = "申请状态 1.待审核 2.审核通过 3.审核拒绝，3.银行处理中 3.银行失败 4.银行成功")
    private Integer applyStatus;

    /**
     * 拒绝原因
     */
    @ApiModelProperty(example = "0", value = "拒绝原因")
    private String refuseReason;

    /**
     * 申请人名
     */
    @ApiModelProperty(example = "0", value = "申请人名")
    private String firstName;

    /**
     * 申请人姓
     */
    @ApiModelProperty(example = "0", value = "申请人姓")
    private String lastName;

    /**
     * email地址
     */
    @ApiModelProperty(example = "0", value = "email地址")
    private String email;

    /**
     * 出生日期，格式为YYY-MM-DD
     */
    @ApiModelProperty(example = "0", value = "出生日期，格式为YYY-MM-DD")
    private Date birth;

    /**
     * 区号
     */
    @ApiModelProperty(example = "0", value = "区号")
    private String nationCode;
    /**
     * 持卡人手机号
     */
    @ApiModelProperty(example = "0", value = "持卡人手机号")
    private String phone;

    /**
     * 证件的国家:US
     */
    @ApiModelProperty(example = "0", value = "证件的国家:US")
    private String identificationCountry;

    /**
     * 证件类型 1-身份证，2-护照，3-驾照
     */
    @ApiModelProperty(example = "0", value = "证件类型 1-身份证，2-护照，3-驾照")
    private Integer identificationType;

    /**
     * 证件号
     */
    @ApiModelProperty(example = "0", value = "证件号")
    private String identificationNumber;

    /**
     * 证件的到期日，格式为YYY-MM-DD
     */
    @ApiModelProperty(example = "0", value = "证件的到期日，格式为YYY-MM-DD")
    private Date identificationExpiryDate;

    /**
     * 证件有效期类型 1-时间范围 2-长期有效
     */
    @ApiModelProperty(example = "1", value = "证件类型 1-时间范围 2-长期有效")
    private Integer identificationExpiryType;

    /**
     * 地址详情：city，country，postcode，state，detail
     */
    @ApiModelProperty(example = "0", value = "地址详情：city，country，postcode，state，detail")
    private String address;

    /**
     * 邮寄地址详情：city，country，postcode，state，detail
     */
    @ApiModelProperty(example = "0", value = "邮寄地址详情：city，country，postcode，state，detail")
    private String postalAddress;

    /**
     * 审批人
     */
    @ApiModelProperty(example = "0", value = "审批人")
    private String approveUserId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(example = "0", value = "审批人姓名")
    private String approveUserName;

    /**
     * 审批通过时间
     */
    @ApiModelProperty(example = "0", value = "审批通过时间")
    private Date approveTime;

    /**
     * 创建人
     */
    @ApiModelProperty(example = "0", value = "创建人")
    private String createUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "0", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "0", value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除字段 0正常 1删除
     */
    @ApiModelProperty(example = "0", value = "逻辑删除字段 0正常 1删除")
    private Integer deleteFlag;

}
