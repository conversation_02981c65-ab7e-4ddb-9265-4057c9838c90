package com.fenbei.fx.card.web.cardauthorize.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
      import java.math.BigDecimal;
      import java.util.Date;
      import java.util.Date;
      import java.util.Date;

/**
 * 国际卡授权表 VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@ApiModel("国际卡授权表 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardAuthorizeVO implements Serializable {

    private static final long serialVersionUID = 5409185459234711691L;

    /**
     * 主键ID
     */
    @ApiModelProperty(example = "xxx", value = "主键ID")
    private String id;

    /**
     * 开卡渠道
     */
    @ApiModelProperty(example = "xxx", value = "开卡渠道")
    private String cardPlatform;

    /**
     * 币种 美元-USD
     */
    @ApiModelProperty(example = "xxx", value = "币种 美元-USD")
    private String tradeCurrency;

    /**
     * 交易金额 单位：分
     */
    @ApiModelProperty(example = "xxx", value = "交易金额 单位：分")
    private BigDecimal tradeAmount;

    /**
     * 交易名
     */
    @ApiModelProperty(example = "xxx", value = "交易名")
    private String tradeName;

    /**
     * 交易时间
     */
    @ApiModelProperty(example = "xxx", value = "交易时间")
    private Date tradeTime;

    /**
     * 交易地
     */
    @ApiModelProperty(example = "xxx", value = "交易地")
    private String tradeAddress;

    /**
     * 原始请求
     */
    @ApiModelProperty(example = "xxx", value = "原始请求")
    private String sourceData;

    /**
     * 授权状态
     */
    @ApiModelProperty(example = "xxx", value = "授权状态")
    private Integer authStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(example = "xxx", value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(example = "xxx", value = "更新时间")
    private Date updateTime;

}
