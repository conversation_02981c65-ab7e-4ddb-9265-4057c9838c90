package com.fenbei.fx.card.web.card.controller.app;

import com.fenbei.fx.card.service.cardcreditmanager.CardCreditManagerService;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerReturnReqDTO;
import com.fenbei.fx.card.service.cardcreditmanager.dto.CardCreditManagerReturnRespDTO;
import com.fenbei.fx.card.service.usercard.UserCardCreditManager;
import com.fenbei.fx.card.service.usercard.dto.UserCardCreditGrantRecordDTO;
import com.fenbei.fx.card.service.usercard.dto.UserCardCreditGrantRecordDetail;
import com.fenbei.fx.card.web.card.vo.CardCreditManagerRefundVO;
import com.fenbei.fx.card.web.card.vo.SelectedCreditApply;
import com.fenbei.fx.card.web.card.vo.UserCardCreditGrantRecordVO;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.finhub.framework.core.enums.ResponseCodeEnum;
import com.finhub.framework.web.controller.ControllerSupport;
import com.finhub.framework.web.vo.ItemR;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户国际卡 Controller
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-20
 */
@Api(tags = {"用户国际卡 API 接口"})
@Slf4j
@Validated
@RestController
@RequestMapping( value = "/fxcard/app/usercard" )
public class UserCardCreditAppController extends ControllerSupport {


    /**
     * 关联额度申请单
     * @return UserCardCreditGrantRecordVO
     */
    @RequestMapping(value = "/credit/refund/relation", method = {RequestMethod.GET})
    public ItemR<UserCardCreditGrantRecordVO> userCards(@RequestParam("fxCardId")String fxCardId,@RequestParam("returnAmount")BigDecimal returnAmount) {
        if (StringUtils.isEmpty(fxCardId)){
            return responseItemR(ResponseCodeEnum.SUCCESS, null);
        }
        //关联申请单应该查未使用余额
        UserCardCreditGrantRecordDTO userCardCreditGrantRecordDTO = UserCardCreditManager.me().relationApplyRecord(fxCardId);
        List<UserCardCreditGrantRecordDetail> dataList = userCardCreditGrantRecordDTO.getDataList();
        BigDecimal remainedReturnAmount = returnAmount;
        for (UserCardCreditGrantRecordDetail userCardCreditGrantRecordVO: dataList){
            BigDecimal useBalance = userCardCreditGrantRecordVO.getUseBalance();
            if (BigDecimalUtils.hasPrice(remainedReturnAmount)) {
                if (useBalance.compareTo(remainedReturnAmount) >= 0) {
                    userCardCreditGrantRecordVO.setOperationAmount(remainedReturnAmount);
                    remainedReturnAmount = BigDecimal.ZERO;
                } else {
                    userCardCreditGrantRecordVO.setOperationAmount(useBalance);
                    remainedReturnAmount = remainedReturnAmount.subtract(useBalance);
                }
            }
        }
        UserCardCreditGrantRecordVO userCardCreditGrantRecordVO = new UserCardCreditGrantRecordVO();
        userCardCreditGrantRecordVO.setDataList(userCardCreditGrantRecordDTO.getDataList());
        userCardCreditGrantRecordVO.setTotalCount(userCardCreditGrantRecordVO.getTotalCount());
        return responseItemR(ResponseCodeEnum.SUCCESS, userCardCreditGrantRecordVO);
    }


    @ApiOperation(value = "额度回收")
    @RequestMapping(value = "/credit/refund", method = {RequestMethod.POST})
    public ItemR<CardCreditManagerReturnRespDTO> refund(@RequestBody CardCreditManagerRefundVO cardCreditManagerRefundVO) {
        CardCreditManagerReturnReqDTO cardCreditManagerReturnReqDTO = new CardCreditManagerReturnReqDTO();
        cardCreditManagerReturnReqDTO.setFxCardId(cardCreditManagerRefundVO.getFxCardId());
        Map<String, BigDecimal> returnAmount = new HashMap<>();
        for (SelectedCreditApply selectedCreditApply :cardCreditManagerRefundVO.getSelectedCreditApplyList()) {
            returnAmount.put(selectedCreditApply.getApplyId(),selectedCreditApply.getUseBalance());
        }
        cardCreditManagerReturnReqDTO.setReturnAmount(returnAmount);
        cardCreditManagerReturnReqDTO.setApplyReason(cardCreditManagerRefundVO.getApplyReason());
        cardCreditManagerReturnReqDTO.setCompanyId(cardCreditManagerRefundVO.getCompanyId());
//        cardCreditManagerReturnReqDTO.setDismissionEmployee(cardCreditManagerRefundVO.getD);
        cardCreditManagerReturnReqDTO.setEmployeeId(cardCreditManagerRefundVO.getEmployeeId());
        cardCreditManagerReturnReqDTO.setApplyReasonDesc(cardCreditManagerRefundVO.getApplyReasonDesc());
        cardCreditManagerReturnReqDTO.setRefundCreditAmount(cardCreditManagerRefundVO.getRefundCreditAmount());
        CardCreditManagerReturnRespDTO cardCreditManagerReturnRespDTO = CardCreditManagerService.me().refund(cardCreditManagerReturnReqDTO);
        return responseItemR(ResponseCodeEnum.SUCCESS, cardCreditManagerReturnRespDTO);
    }
}
