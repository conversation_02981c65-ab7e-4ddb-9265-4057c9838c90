package com.fenbei.fx.card.web.cardorder.converter;

import com.finhub.framework.core.converter.BaseVOConverter;
import com.finhub.framework.core.converter.BaseVOConverterConfig;
import com.finhub.framework.core.page.Page;

import cn.hutool.extra.spring.SpringUtil;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderAddReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderListResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderModifyReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderRemoveReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardOrderShowResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppPageReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppPageResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppRemarkReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoAppShowResDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoWebPageReqDTO;
import com.fenbei.fx.card.service.cardorder.dto.CardTradeInfoWebPageResDTO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderAddReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderListReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderListResVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderModifyReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderRemoveReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderShowResVO;
import com.fenbei.fx.card.web.cardorder.vo.CardOrderVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoAppPageReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoAppPageResVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoAppRemarkReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoAppShowResVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoWebPageReqVO;
import com.fenbei.fx.card.web.cardorder.vo.CardTradeInfoWebPageResVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 国际卡订单 VOConverter
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-20
 */
@Mapper(config = BaseVOConverterConfig.class)
public interface CardOrderVOConverter extends BaseVOConverter<CardOrderDTO, CardOrderVO> {

    static CardOrderVOConverter me() {
        return SpringUtil.getBean(CardOrderVOConverter.class);
    }

    CardOrderAddReqDTO convertToCardOrderAddReqDTO(CardOrderAddReqVO cardOrderAddReqVO);

    CardOrderShowResVO convertToCardOrderShowResVO(CardOrderShowResDTO cardOrderShowResDTO);

    CardOrderModifyReqDTO convertToCardOrderModifyReqDTO(CardOrderModifyReqVO cardOrderModifyReqVO);

    CardOrderRemoveReqDTO convertToCardOrderRemoveReqDTO(CardOrderRemoveReqVO cardOrderRemoveReqVO);

    CardOrderListReqDTO convertToCardOrderListReqDTO(CardOrderListReqVO cardOrderReqVO);

    CardTradeInfoWebPageReqDTO convertToCardTradeInfoWebPageReqDTO(CardTradeInfoWebPageReqVO cardTradeInfoWebPageReqVO);

    CardOrderListResVO convertToCardOrderListResVO(CardOrderListResDTO cardOrderListResDTO);

    List<CardOrderListResVO> convertToCardOrderListResVOList(List<CardOrderListResDTO> cardOrderListResDTOList);

    Page<CardTradeInfoWebPageResVO> convertToCardTradeInfoWebPageResVOPage(Page<CardTradeInfoWebPageResDTO> cardOrderPageResDTOPage);

    List<CardOrderAddReqDTO> convertToCardOrderAddReqDTOList(List<CardOrderAddReqVO> cardOrderAddReqVOList);

    List<CardOrderShowResVO> convertToCardOrderShowResVOList(List<CardOrderShowResDTO> cardOrderShowResDTOList);

    CardTradeInfoAppShowResVO convertToCardTradeInfoAppShowResVO(CardTradeInfoAppShowResDTO cardTradeInfoAppShowResDTO);

    CardTradeInfoAppRemarkReqDTO convertToCardTradeInfoAppRemarkReqDTO(CardTradeInfoAppRemarkReqVO cardTradeInfoAppRemarkReqVO);
}
