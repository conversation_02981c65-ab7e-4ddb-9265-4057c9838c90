<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fenbei.fx.card</groupId>
        <artifactId>fenbei-fx-card</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fenbei-fx-card-web</artifactId>
    <version>${fenbei-fx-card.version}</version>
    <packaging>jar</packaging>

    <properties>
        <skip-maven-deploy>true</skip-maven-deploy>
    </properties>

    <dependencies>
        <!-- Module -->
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-service</artifactId>
        </dependency>
        <!-- END -->

        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.finhub.framework</groupId>
            <artifactId>finhub-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fenbeitong</groupId>
            <artifactId>finhub-common</artifactId>
        </dependency>

        <!-- spring-boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- END -->

        <!-- web 依赖包 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
        <!-- END -->

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fenbei.fx.card</groupId>
            <artifactId>fenbei-fx-card-api</artifactId>
        </dependency>
    </dependencies>
</project>
